"""
自动Cookie延长管理器模块
"""

import os
import json
import time
import datetime
import threading
from datetime import datetime, timedelta
from flask import current_app


class AutoLoginManager:
    """自动Cookie延长管理器 - 负责定时检查和自动重新登录"""

    def __init__(self, data_manager, logger, flask_app=None):
        self.data_manager = data_manager
        self.logger = logger
        self.flask_app = flask_app  # 保存Flask应用实例
        self.running = False
        self.check_thread = None
        self.check_interval = 300  # 5分钟检查一次
        self.cookie_threshold_minutes = 15  # Cookie剩余15分钟以下触发自动登录
        self.retry_interval_minutes = 15  # 自动重试间隔15分钟

        # 移除pending_sms_devices - 不再支持短信验证码等待状态

        # 存储自动登录历史
        self.auto_login_history = {}  # device_id -> [{'timestamp', 'status', 'message'}]

    def start(self):
        """启动自动登录管理器"""
        if self.running:
            self.logger.warning("自动登录管理器已在运行")
            return False

        self.running = True
        self.check_thread = threading.Thread(target=self._check_loop, daemon=True)
        self.check_thread.start()
        self.logger.info("[INFO] 自动Cookie延长检查循环已启动")
        return True

    def stop(self):
        """停止自动登录管理器"""
        if not self.running:
            return False

        self.running = False
        if self.check_thread and self.check_thread.is_alive():
            self.check_thread.join(timeout=5)
        self.logger.info("[STOP] 自动Cookie延长管理器已停止")
        return True

    def _check_loop(self):
        """后台检查循环"""
        while self.running:
            try:
                self._check_all_devices()

                # 等待下次检查，支持提前停止
                for _ in range(self.check_interval):
                    if not self.running:
                        break
                    time.sleep(1)

            except Exception as e:
                self.logger.error(f"自动Cookie延长检查循环出错: {e}")
                time.sleep(30)  # 出错后等待30秒再继续

        self.logger.info("[INFO] 自动Cookie延长检查循环已退出")

    def _check_all_devices(self):
        """检查所有启用设备的Cookie状态"""
        try:
            grab_device_model = getattr(self.data_manager, 'grab_device_model', None)
            if not grab_device_model:
                return

            devices = grab_device_model.get_all()
            enabled_devices = [d for d in devices if d.get('enabled')]

            if not enabled_devices:
                return

            # 筛选出已登录的设备用于Cookie检查
            logged_devices = [d for d in enabled_devices if d.get('access_token')]

            if logged_devices:
                self.logger.debug(f"检查 {len(logged_devices)} 个已登录启用设备的Cookie状态")
            else:
                self.logger.debug(f"发现 {len(enabled_devices)} 个启用设备，但没有已登录的设备需要检查Cookie状态")

            for device in logged_devices:
                if not self.running:
                    break
                self._check_device_cookie(device)

        except Exception as e:
            self.logger.error(f"检查所有设备Cookie状态失败: {e}")

    def _check_device_cookie(self, device):
        """检查单个设备的Cookie状态"""
        device_id = device['id']
        username = device['username']
        access_token = device.get('access_token')

        if not access_token:
            return

        try:
            def check_with_context():
                # 计算Cookie剩余时间
                from .login_helper import HuhhothomeLoginHelper
                login_helper = HuhhothomeLoginHelper()
                remaining_hours = login_helper.get_cookie_remaining_hours(access_token)

                if remaining_hours is None:
                    self.logger.warning(f"设备 {username} 无法解析Cookie有效期")
                    return

                remaining_minutes = remaining_hours * 60

                # 检查是否需要触发自动登录
                if remaining_minutes <= self.cookie_threshold_minutes:
                    self._handle_cookie_expiring(device, remaining_minutes)
                else:
                    # Cookie状态正常，无需处理
                    pass

            # 确保在Flask应用上下文中执行
            if self.flask_app:
                with self.flask_app.app_context():
                    check_with_context()
            else:
                # 如果没有Flask应用实例，直接执行（可能在主线程中）
                check_with_context()

        except Exception as e:
            self.logger.error(f"检查设备 {username} Cookie状态失败: {e}")

    def _handle_cookie_expiring(self, device, remaining_minutes):
        """处理Cookie即将过期的设备"""
        device_id = device['id']
        username = device['username']

        # 不再支持短信验证码等待状态，直接处理
        pass

        self.logger.warning(f"设备 {username} Cookie剩余 {remaining_minutes:.1f} 分钟，触发自动重新登录")

        # 记录自动登录历史
        self._add_auto_login_history(device_id, 'triggered', f'Cookie剩余{remaining_minutes:.1f}分钟，触发自动登录')

        # 执行自动Cookie延长
        success = self._trigger_auto_extend(device)

        if not success:
            # 自动延长失败，设置下次重试时间
            next_retry = datetime.now() + timedelta(minutes=self.retry_interval_minutes)
            self._add_auto_login_history(device_id, 'failed', '自动Cookie延长失败，将在15分钟后重试')

    def _trigger_auto_extend(self, device):
        """触发自动Cookie延长 - 只使用autologin接口"""
        device_id = device['id']
        username = device['username']
        phone = device.get('phone')
        access_token = device.get('access_token')
        sessioncode = device.get('sessioncode')

        if not phone:
            self.logger.error(f"设备 {username} 未配置手机号，无法自动延长Cookie")
            return False

        if not access_token or not sessioncode:
            missing = []
            if not access_token: missing.append('access_token')
            if not sessioncode: missing.append('sessioncode')
            self.logger.error(f"设备 {username} 缺少必要参数: {', '.join(missing)}，需要手动重新登录")
            self._add_auto_login_history(device_id, 'missing_params', f'缺少必要参数: {", ".join(missing)}，需要手动重新登录')
            return False

        try:
            def execute_with_context():
                from .login_helper import HuhhothomeLoginHelper
                login_helper = HuhhothomeLoginHelper()

                self.logger.info(f"设备 {username} 开始使用autologin接口延长Cookie...")

                extend_result = login_helper.auto_extend_cookie(phone, access_token, sessioncode)

                if extend_result.get('success'):
                    # autologin延长成功，更新设备信息
                    self.logger.info(f"[OK] 设备 {username} autologin延长成功，剩余时间: {extend_result['remaining_hours']:.2f} 小时")

                    self._update_device_after_extend(device, extend_result)
                    self._add_auto_login_history(device_id, 'autologin_success',
                                               f'使用autologin接口成功延长Cookie，剩余时间: {extend_result["remaining_hours"]:.2f} 小时')

                    # 向管理员发送延长成功通知
                    self._notify_admin_extend_success(device, extend_result['remaining_hours'])

                    return True
                else:
                    self.logger.warning(f"设备 {username} autologin延长失败: {extend_result.get('message', '未知错误')}")
                    self._add_auto_login_history(device_id, 'autologin_failed',
                                               f'autologin延长失败: {extend_result.get("message", "未知错误")}')
                    return False

            # 确保在Flask应用上下文中执行
            if self.flask_app:
                with self.flask_app.app_context():
                    return execute_with_context()
            else:
                return execute_with_context()

        except Exception as e:
            self.logger.error(f"设备 {username} 自动Cookie延长过程异常: {e}")
            import traceback
            self.logger.error(f"详细错误堆栈: {traceback.format_exc()}")
            self._add_auto_login_history(device_id, 'error', f'自动Cookie延长异常: {str(e)}')
            return False

    def _update_device_after_extend(self, device, extend_result):
        """autologin延长成功后更新设备信息"""
        try:
            grab_device_model = getattr(self.data_manager, 'grab_device_model', None)
            if not grab_device_model:
                return

            from .login_helper import HuhhothomeLoginHelper
            login_helper = HuhhothomeLoginHelper()

            # 计算新的过期时间
            expires_at = None
            new_access_token = extend_result['access_token']
            if new_access_token:
                jwt_payload = login_helper.parse_jwt_token(new_access_token)
                if jwt_payload and 'exp' in jwt_payload:
                    expires_at = datetime.fromtimestamp(jwt_payload['exp'])

            # 更新设备数据
            device_update = {
                'id': device['id'],
                'username': device['username'],
                'phone': device['phone'],
                'target_estate': device.get('target_estate', ''),
                'house_id': device.get('house_id', ''),
                'enabled': device.get('enabled', True),
                'conditions': device.get('conditions', []),
                'access_token': new_access_token,
                'sessioncode': device.get('sessioncode', ''),  # 保持原有sessioncode
                'cookie': json.dumps(extend_result['cookies']),
                'cookie_expires_at': expires_at.isoformat() if expires_at else None,
                'last_login_at': datetime.now().isoformat()
            }

            grab_device_model.create_or_update(device_update)
            self.logger.info(f"设备 {device['username']} 数据已更新")

        except Exception as e:
            self.logger.error(f"更新设备数据失败: {e}")

    # 移除_notify_admin_sms_waiting方法 - 不再支持短信验证码等待通知

    # 移除_check_pending_sms_retry方法 - 不再支持短信验证码重试逻辑

    def _add_auto_login_history(self, device_id, status, message):
        """添加自动登录历史记录"""
        if device_id not in self.auto_login_history:
            self.auto_login_history[device_id] = []

        history_entry = {
            'timestamp': datetime.now().isoformat(),
            'status': status,
            'message': message
        }

        self.auto_login_history[device_id].append(history_entry)

        # 保持历史记录不超过50条
        if len(self.auto_login_history[device_id]) > 50:
            self.auto_login_history[device_id] = self.auto_login_history[device_id][-50:]

    # 移除complete_sms_login方法 - 不再支持短信验证码登录完成

    def _notify_admin_extend_success(self, device, cookie_remaining_hours):
        """向管理员发送登录成功通知"""
        try:
            # 直接从data_manager获取配置
            admin_notifier_config = self.data_manager.get_config('admin_notifier', {})

            if not admin_notifier_config.get('enabled', True):
                return

            admin_device_ids = admin_notifier_config.get('device_ids', [])
            if not admin_device_ids:
                return

            device_model = getattr(self.data_manager, 'device_model', None)
            if not device_model:
                return

            # 构建推送内容
            username = device['username']
            title = f"[OK] 抢房设备Cookie延长成功"
            content = f"""设备名称: {username}
状态: 登录成功
Cookie有效期: {cookie_remaining_hours:.2f}小时

抢房设备已成功完成自动重新登录，Cookie有效期已延长。设备将继续正常工作。"""

            # 向管理员设备发送成功通知
            successful_count = 0
            for admin_device_id in admin_device_ids:
                try:
                    admin_device = device_model.get_by_id(admin_device_id)
                    if not admin_device:
                        continue

                    # 检查设备是否过期
                    if admin_device.get('expire_date'):
                        expire_date = datetime.fromisoformat(admin_device['expire_date'])
                        if expire_date < datetime.now():
                            continue

                    success = self._send_notification_sync(admin_device, title, content)

                    if success:
                        successful_count += 1

                except Exception:
                    continue

            if successful_count > 0:
                self.logger.info(f"[OK] 已向 {successful_count} 个管理员设备发送登录成功通知")

        except Exception as e:
            self.logger.error(f"发送登录成功通知时异常: {e}")

    def get_pending_devices(self):
        """获取等待处理的设备列表（已移除短信验证码支持）"""
        return {}  # 不再支持等待短信验证码的设备

    def get_auto_login_history(self, device_id):
        """获取设备的自动登录历史"""
        return self.auto_login_history.get(device_id, [])

    def get_status(self):
        """获取自动登录管理器状态"""
        return {
            'running': self.running,
            'check_interval_seconds': self.check_interval,
            'cookie_threshold_minutes': self.cookie_threshold_minutes,
            'retry_interval_minutes': self.retry_interval_minutes,
            'pending_devices_count': 0,  # 不再支持等待短信验证码的设备
            'pending_devices': {}
        }