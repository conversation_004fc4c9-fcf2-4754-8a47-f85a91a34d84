"""验证器工具模块"""

from typing import Dict, Any, List
import re
from datetime import datetime


def validate_device_config(device: Dict[str, Any]) -> tuple[bool, str]:
    """验证设备配置
    
    Returns:
        tuple: (是否有效, 错误信息)
    """
    if not device.get('id'):
        return False, '缺少设备ID'
    
    if not device.get('name'):
        return False, '缺少设备名称'
    
    device_type = device.get('type')
    if not device_type:
        return False, '缺少设备类型'
    
    # 验证设备类型特定的字段
    if device_type == 'bark':
        if not device.get('bark_key'):
            return False, '缺少Bark Key'
    elif device_type == 'wxpush':
        if not device.get('uid'):
            return False, '缺少微信推送UID'
        # 验证app_token格式
        app_token = device.get('app_token', '')
        if app_token and not app_token.startswith('AT_'):
            return False, 'App Token格式无效'
    elif device_type == 'pushme':
        if not device.get('pushme_key'):
            return False, '缺少PushMe Key'
    else:
        return False, f'不支持的设备类型: {device_type}'
    
    # 验证过期日期格式
    expire_date = device.get('expire_date')
    if expire_date:
        try:
            datetime.strptime(expire_date, "%Y-%m-%d")
        except ValueError:
            return False, '过期日期格式无效，应为YYYY-MM-DD'
    
    return True, ''


def validate_house_config(house: Dict[str, Any]) -> tuple[bool, str]:
    """验证房源配置
    
    Returns:
        tuple: (是否有效, 错误信息)
    """
    if not house.get('name'):
        return False, '缺少房源名称'
    
    # 验证房源名称格式（中文字符）
    name = house.get('name', '')
    if not re.match(r'^[\u4e00-\u9fa5\w\s\(\)（）]+$', name):
        return False, '房源名称包含无效字符'
    
    device_ids = house.get('device_ids', [])
    if not isinstance(device_ids, list):
        return False, '设备ID列表格式无效'
    
    return True, ''


def validate_proxy_url(url: str) -> tuple[bool, str]:
    """验证代理API URL格式
    
    Returns:
        tuple: (是否有效, 错误信息)
    """
    if not url:
        return False, '代理URL不能为空'
    
    if not url.startswith(('http://', 'https://')):
        return False, '代理URL必须以http://或https://开头'
    
    return True, ''


def validate_time_format(time_str: str) -> tuple[bool, str]:
    """验证时间格式 HH:MM
    
    Returns:
        tuple: (是否有效, 错误信息)
    """
    if not time_str:
        return False, '时间不能为空'
    
    try:
        datetime.strptime(time_str, "%H:%M")
        return True, ''
    except ValueError:
        return False, '时间格式无效，应为HH:MM'


def validate_schedule_config(schedule: Dict[str, Any]) -> tuple[bool, str]:
    """验证调度配置
    
    Returns:
        tuple: (是否有效, 错误信息)
    """
    tasks = schedule.get('tasks', {})
    if not isinstance(tasks, dict):
        return False, '任务配置格式无效'
    
    for task_name, task_config in tasks.items():
        if not isinstance(task_config, dict):
            return False, f'任务 {task_name} 配置格式无效'
        
        # 验证时间格式
        start_time = task_config.get('start_time')
        if start_time:
            valid, msg = validate_time_format(start_time)
            if not valid:
                return False, f'任务 {task_name} 开始{msg}'
        
        stop_time = task_config.get('stop_time')
        if stop_time:
            valid, msg = validate_time_format(stop_time)
            if not valid:
                return False, f'任务 {task_name} 结束{msg}'
        
        # 验证星期设置
        days = task_config.get('days', [])
        if not isinstance(days, list):
            return False, f'任务 {task_name} 执行日期格式无效'
        
        for day in days:
            if not isinstance(day, int) or day < 0 or day > 6:
                return False, f'任务 {task_name} 包含无效的星期值: {day}'
    
    return True, '' 