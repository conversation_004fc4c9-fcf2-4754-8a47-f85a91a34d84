{"login_phone": "申请人手机号", "house_id": "ARcaVHdSBt8niiV3LMV", "application_data": {"name": "house_approve", "familys": [{"cardno": "父亲的身份证号", "name": "父亲的姓名", "age": 54, "sex": "男", "nation": "汉", "relationship": "之父", "phone": "父亲的手机号", "card1": ["father_id_back.jpg"], "card0": ["father_id_front.jpg"]}, {"cardno": "母亲的身份证号", "name": "母亲的姓名", "age": 54, "sex": "女", "nation": "汉", "relationship": "之母", "phone": "15598886480", "card1": ["mother_id_back.jpg"], "card0": ["mother_id_front.jpg"]}], "itemmap": {"group": "高学历人才", "selecthouse": "此字段会被脚本自动填充", "sex": "男", "marriage": "未婚", "education": "本科", "leasetime": "1", "rewardattmts": [], "achievementattmts": [], "personnel": [], "company": "公司名称", "country": "nevx7NlPE7lGU70JgnQ", "township": "f5G2cSdzZPROi91FtlM", "village": "wlWXfKLpxNed0VrNell", "addr": "公司地址", "household": [], "work": ["work_proof.jpg"], "nohouse": [], "educationattachment": ["education_cert.pdf"], "approvefile": [], "remark": "", "canchange": true, "educationtype": "PDF文件", "educationcode": ""}}}