"""通知管理器"""

from typing import Dict, List, Optional
import asyncio

from .base import BaseNotifier
from .notifiers import BarkNotifier, WxPushNotifier, PushMeNotifier
from core.data.models import Device, MonitorResult, HouseDetail
from core.network.http_client import HttpClient
from core.utils.unified_logging import Logger
from datetime import datetime
from collections import defaultdict


class NotificationManager:
    """通知管理器，负责向所有设备发送通知"""

    def __init__(self, http_client: HttpClient, logger: Logger, config: Dict, data_manager=None):
        self.logger = logger
        self.config = config
        self.http_client = http_client
        self.data_manager = data_manager  # 添加数据管理器引用
        self._notifiers: Dict[str, BaseNotifier] = self._init_notifiers()

    def _init_notifiers(self) -> Dict[str, BaseNotifier]:
        """初始化所有通知器"""
        # 使用固定的WxPush app token（从原始代码中提取）
        wxpush_token = self.config.get("wxpush_default_token", "AT_SEGaKHPlPgjziZNkZVJwISroZvFcxQDZ")

        notifiers = {
            "bark": BarkNotifier(self.http_client),
            "wxpush": WxPushNotifier(self.http_client, self.logger, wxpush_token),
            "pushme": PushMeNotifier(self.http_client)
        }
        self.logger.info(f"已初始化 {len(notifiers)} 个通知器平台")
        return notifiers

    def update_config(self, config: Dict):
        """更新配置"""
        self.config = config
        self.logger.info("通知管理器配置已更新")

        # 检查设备列表
        device_list = self.config.get("device_list", [])
        self.logger.info(f"配置中包含 {len(device_list)} 个设备")

    def _get_devices_by_ids(self, device_ids: List[str]) -> List[Device]:
        """根据设备ID列表获取设备对象"""
        # 添加调试日志
        self.logger.debug(f"设备ID列表: {device_ids}")

        # 从数据库获取设备列表
        if self.data_manager:
            all_devices_list = self.data_manager.get_devices()
            self.logger.debug(f"从数据库获取了 {len(all_devices_list)} 个设备")

            # 将设备列表转换为字典，方便查找
            all_devices = {}
            for d in all_devices_list:
                if d.get('id'):
                    all_devices[d['id']] = Device(
                        id=d['id'],
                        name=d.get('name', d['id']),
                        type=d.get('type'),
                        config=d
                    )
        else:
            # 如果没有数据管理器，则从配置中获取设备列表（兼容旧代码）
            device_list_config = self.config.get("device_list", [])
            self.logger.debug(f"配置中的设备数量: {len(device_list_config)}")

            # 将设备配置转换为Device对象
            all_devices = {d['id']: Device(
                id=d['id'],
                name=d.get('name', d['id']),
                type=d.get('type'),
                config=d
            ) for d in device_list_config if d.get('id')}

        # 添加调试日志
        self.logger.debug(f"有效设备ID: {list(all_devices.keys())}")

        # 检查每个请求的设备ID是否存在
        for device_id in device_ids:
            if device_id not in all_devices:
                self.logger.warning(f"设备ID '{device_id}' 在数据库中不存在")

        # 筛选出需要的设备
        result = [all_devices[id] for id in device_ids if id in all_devices]
        self.logger.debug(f"找到 {len(result)} 个有效设备")
        return result

    def _group_devices_by_type(self, devices: List[Device]) -> Dict[str, List[Device]]:
        """将设备按类型分组

        Args:
            devices: 设备列表

        Returns:
            Dict[str, List[Device]]: 按类型分组的设备字典
        """
        grouped = defaultdict(list)
        for device in devices:
            grouped[device.type].append(device)

        # 输出日志
        for device_type, type_devices in grouped.items():
            self.logger.debug(f"设备类型 {device_type} 包含 {len(type_devices)} 个设备")

        return grouped

    async def send_notification_for_result(self, result: MonitorResult, device_ids: List[str]):
        """根据监控结果发送通知"""
        if not result.has_change:
            return

        # 添加调试日志
        self.logger.debug(f"准备为房源 [{result.house_name}] 发送通知")
        self.logger.debug(f"传入的设备ID列表: {device_ids}")

        # 获取设备
        devices = self._get_devices_by_ids(device_ids)

        if not devices:
            self.logger.warning(f"房源 [{result.house_name}] 未配置有效设备，无法推送")
            return

        title, content = self._format_message(result)

        # 按类型分组设备
        grouped_devices = self._group_devices_by_type(devices)

        tasks = []

        # 特殊处理WxPush设备，批量发送
        if "wxpush" in grouped_devices and len(grouped_devices["wxpush"]) > 0:
            wxpush_devices = grouped_devices["wxpush"]
            self.logger.info(f"为房源 [{result.house_name}] 批量发送WxPush通知到 {len(wxpush_devices)} 个设备")
            tasks.append(self._send_wxpush_batch(wxpush_devices, title, content, result.house_name))
            # 从分组中移除已处理的WxPush设备
            del grouped_devices["wxpush"]

        # 处理其他类型设备（单个发送）
        for device_type, type_devices in grouped_devices.items():
            notifier = self._notifiers.get(device_type)
            if notifier:
                for device in type_devices:
                    tasks.append(self._send_to_device(notifier, device, title, content, result.house_name))
            else:
                self.logger.warning(f"未找到设备类型为 {device_type} 的通知器")

        if tasks:
            await asyncio.gather(*tasks)

    async def _send_wxpush_batch(self, devices: List[Device], title: str, content: str, house_name: str):
        """批量发送WxPush通知

        Args:
            devices: WxPush设备列表
            title: 通知标题
            content: 通知内容
            house_name: 房源名称（用于日志）
        """
        if not devices:
            return

        notifier = self._notifiers.get("wxpush")
        if not notifier:
            self.logger.error("未找到WxPush通知器")
            return

        # 收集所有有效的UID
        uids = []
        device_names = []
        for device in devices:
            uid = device.config.get("uid")
            if uid:
                uids.append(uid)
                device_names.append(device.name)
            else:
                self.logger.warning(f"设备 [{device.name}] 缺少WxPush UID，跳过")

        if not uids:
            self.logger.warning(f"房源 [{house_name}] 没有有效的WxPush UID，无法推送")
            return

        # 使用第一个设备的配置发送批量通知
        first_device = devices[0]

        try:
            self.logger.info(f"正在批量向 {len(uids)} 个WxPush设备推送房源[{house_name}]的消息")
            success = await notifier.send(first_device, title, content, uids=uids)
            if success:
                self.logger.info(f"成功批量推送到 {len(uids)} 个WxPush设备: {', '.join(device_names)}")
            else:
                self.logger.warning(f"批量推送到 {len(uids)} 个WxPush设备失败")
        except Exception as e:
            error_type = type(e).__name__
            self.logger.error(f"批量推送到 {len(uids)} 个WxPush设备异常: {error_type}: {str(e)}")

    def _format_message(self, result: MonitorResult) -> tuple[str, str]:
        """格式化通知消息"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 检查是否为抢房结果通知
        if hasattr(result, 'change_type') and result.change_type == "grab_result":
            # 抢房结果通知，使用预设的标题和内容
            title = result.house_name
            content = getattr(result, '_content', f"抢房结果通知\n时间: {current_time}")
            return title, content

        if result.details:
            # 详情推送消息
            title = f"{result.house_name} 新增房源详情"
            content = (f"时间: {current_time}\n"
                       f"新增 {len(result.details)} 个房源详情:\n")
            for house in result.details:
                content += (f"- [{house.outtype}] {house.position} - {house.roomno}, "
                            f"面积: {house.area}㎡, 月租: {house.rent}元\n")
        else:
            # 简单变化通知
            if result.change_type.value == "increase":
                title = f"{result.house_name} 房源数量增加"
            elif result.change_type.value == "decrease":
                title = f"{result.house_name} 房源数量减少"
            else:
                title = f"{result.house_name} 房源变化"

            content = (f"时间：{current_time}\n"
                       f"数量更新：{result.old_count} → {result.new_count}")

        return title, content

    async def _send_to_device(self, notifier: BaseNotifier, device: Device, title: str, content: str, house_name: str):
        """发送到单个设备"""
        device_info = f"设备[{device.name}]({notifier.platform_name})"
        try:
            self.logger.info(f"正在向{device_info}推送房源[{house_name}]的消息")
            success = await notifier.send(device, title, content)
            if success:
                self.logger.info(f"向{device_info}推送成功")
            else:
                self.logger.warning(f"向{device_info}推送失败 - 返回状态为失败")
        except Exception as e:
            # 更详细的错误信息
            error_type = type(e).__name__
            error_details = str(e)

            # 特殊处理网络连接错误
            if "Server disconnected" in error_details:
                self.logger.error(f"向{device_info}推送失败 - 服务器连接中断，可能是网络或代理问题")
            elif "ClientConnectorError" in error_type:
                self.logger.error(f"向{device_info}推送失败 - 网络连接错误: {error_details}")
            elif "TimeoutError" in error_type:
                self.logger.error(f"向{device_info}推送失败 - 请求超时: {error_details}")
            else:
                self.logger.error(f"向{device_info}推送失败 - {error_type}: {error_details}")

            # 对于调试目的，可以记录详细的异常堆栈
            self.logger.debug(f"向{device_info}推送异常详情", context=e)

    async def send_admin_notification(self, title: str, content: str):
        """向管理员发送通知"""
        admin_notifier = self.config.get("admin_notifier", {})
        device_ids = admin_notifier.get("device_ids", [])

        if not device_ids:
            self.logger.warning("没有配置管理员设备，无法发送管理员通知")
            return

        self.logger.info(f"向管理员发送通知: {title}")
        devices = self._get_devices_by_ids(device_ids)

        if not devices:
            self.logger.warning("未找到有效的管理员设备")
            return

        # 按类型分组设备
        grouped_devices = self._group_devices_by_type(devices)

        tasks = []

        # 特殊处理WxPush设备，批量发送
        if "wxpush" in grouped_devices and len(grouped_devices["wxpush"]) > 0:
            wxpush_devices = grouped_devices["wxpush"]
            self.logger.info(f"批量发送WxPush管理员通知到 {len(wxpush_devices)} 个设备")
            tasks.append(self._send_wxpush_batch(wxpush_devices, title, content, "管理员通知"))
            # 从分组中移除已处理的WxPush设备
            del grouped_devices["wxpush"]

        # 处理其他类型设备（单个发送）
        for device_type, type_devices in grouped_devices.items():
            notifier = self._notifiers.get(device_type)
            if notifier:
                for device in type_devices:
                    tasks.append(self._send_to_device(notifier, device, title, content, "管理员通知"))

        if tasks:
            await asyncio.gather(*tasks)

    async def close_service(self):
        """关闭所有通知器和它们的HTTP会话"""
        self.logger.info("开始关闭通知服务资源...")
        close_tasks = []

        for notifier_type, notifier in self._notifiers.items():
            if hasattr(notifier, 'close') and callable(notifier.close):
                self.logger.debug(f"关闭 {notifier_type} 通知器...")
                close_tasks.append(notifier.close())

        if close_tasks:
            try:
                # 使用容忍异常的方式等待所有关闭任务
                await asyncio.gather(*close_tasks, return_exceptions=True)
                self.logger.info(f"所有 {len(close_tasks)} 个通知器已尝试关闭")
            except Exception as e:
                self.logger.warning(f"关闭通知服务时发生异常: {str(e)}")

        # 最后额外等待以确保所有连接都有机会关闭
        await asyncio.sleep(0.2)
        self.logger.info("通知服务资源关闭完成")