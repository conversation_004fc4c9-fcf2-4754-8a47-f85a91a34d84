#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传缓存管理器
用于优化抢房流程中的文件上传性能
"""

import json
import hashlib
import os
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Optional, Any, List
import logging


class FileUploadCache:
    """文件上传缓存管理器"""

    def __init__(self, cache_dir: str = "cache/uploads"):
        """
        初始化缓存管理器

        Args:
            cache_dir: 缓存目录路径
        """
        self.cache_dir = Path(cache_dir)
        self.cache_file = self.cache_dir / "upload_cache.json"
        self.lock = threading.RLock()  # 线程安全锁
        self.cache_data = {}
        self.logger = logging.getLogger(__name__)

        # 创建缓存目录
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # 加载现有缓存
        self._load_cache()

    def _load_cache(self):
        """加载缓存数据"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self.cache_data = json.load(f)
                self.logger.info(f"缓存数据加载成功，包含 {len(self.cache_data)} 个用户的缓存")
            else:
                self.cache_data = {}
                self.logger.info("缓存文件不存在，初始化空缓存")
        except Exception as e:
            self.logger.error(f"加载缓存失败: {e}")
            self.cache_data = {}

    def _save_cache(self):
        """保存缓存数据到文件"""
        try:
            with self.lock:
                with open(self.cache_file, 'w', encoding='utf-8') as f:
                    json.dump(self.cache_data, f, ensure_ascii=False, indent=2)
                self.logger.debug("缓存数据已保存")
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")

    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件SHA256哈希值"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return f"sha256:{hash_sha256.hexdigest()}"
        except Exception as e:
            self.logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return None

    def get_cached_upload(self, user_id: str, file_path: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的上传结果

        Args:
            user_id: 用户ID
            file_path: 本地文件路径

        Returns:
            缓存的上传结果，如果无效则返回None
        """
        try:
            with self.lock:
                if user_id not in self.cache_data:
                    return None

                user_cache = self.cache_data[user_id]
                filename = os.path.basename(file_path)

                if filename not in user_cache.get('files', {}):
                    return None

                cached_result = user_cache['files'][filename]

                # 检查文件是否变化
                if not os.path.exists(file_path):
                    self.logger.warning(f"缓存文件不存在: {file_path}")
                    return None

                current_hash = self._calculate_file_hash(file_path)
                if not current_hash or current_hash != cached_result.get('file_hash'):
                    self.logger.info(f"文件已变化，缓存失效: {filename}")
                    return None

                # 检查缓存是否过期
                if not self.is_cache_valid(cached_result):
                    self.logger.info(f"缓存已过期: {filename}")
                    return None

                self.logger.info(f"使用缓存文件: {filename} -> {cached_result['server_name']}")
                return {
                    'path': cached_result['server_path'],
                    'name': cached_result['server_name']
                }

        except Exception as e:
            self.logger.error(f"获取缓存失败 {user_id}/{file_path}: {e}")
            return None

    def cache_upload_result(self, user_id: str, file_path: str, upload_result: Dict[str, Any]):
        """
        缓存上传结果

        Args:
            user_id: 用户ID
            file_path: 本地文件路径
            upload_result: 上传结果 {'path': '...', 'name': '...'}
        """
        try:
            if not upload_result or 'path' not in upload_result or 'name' not in upload_result:
                self.logger.warning(f"无效的上传结果，跳过缓存: {upload_result}")
                return

            filename = os.path.basename(file_path)
            file_hash = self._calculate_file_hash(file_path)

            if not file_hash:
                self.logger.warning(f"无法计算文件哈希，跳过缓存: {file_path}")
                return

            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            current_time = datetime.now().isoformat()
            expires_at = (datetime.now() + timedelta(hours=24)).isoformat()

            with self.lock:
                if user_id not in self.cache_data:
                    self.cache_data[user_id] = {
                        'files': {},
                        'last_preupload': current_time,
                        'cache_version': '1.0'
                    }

                self.cache_data[user_id]['files'][filename] = {
                    'server_path': upload_result['path'],
                    'server_name': upload_result['name'],
                    'file_hash': file_hash,
                    'upload_time': current_time,
                    'file_size': file_size,
                    'expires_at': expires_at
                }

                self.cache_data[user_id]['last_preupload'] = current_time

                self._save_cache()

            self.logger.info(f"缓存上传结果: {user_id}/{filename} -> {upload_result['name']}")

        except Exception as e:
            self.logger.error(f"缓存上传结果失败 {user_id}/{file_path}: {e}")

    def clear_file_cache(self, user_id: str, file_path: str):
        """
        清除指定文件的缓存

        Args:
            user_id: 用户ID
            file_path: 文件路径
        """
        try:
            filename = os.path.basename(file_path)

            with self.lock:
                if user_id in self.cache_data:
                    user_cache = self.cache_data[user_id]
                    if filename in user_cache.get('files', {}):
                        del user_cache['files'][filename]
                        self._save_cache()
                        self.logger.info(f"清除文件缓存: {user_id}/{filename}")

        except Exception as e:
            self.logger.error(f"清除文件缓存失败 {user_id}/{file_path}: {e}")

    def is_cache_valid(self, cached_result: Dict[str, Any], max_age_hours: int = 24) -> bool:
        """
        检查缓存是否有效

        Args:
            cached_result: 缓存结果
            max_age_hours: 最大缓存时间（小时）

        Returns:
            是否有效
        """
        try:
            expires_at_str = cached_result.get('expires_at')
            if not expires_at_str:
                return False

            expires_at = datetime.fromisoformat(expires_at_str)
            return datetime.now() < expires_at

        except Exception as e:
            self.logger.error(f"检查缓存有效性失败: {e}")
            return False

    def get_user_cached_files(self, user_id: str) -> Dict[str, Dict[str, Any]]:
        """
        获取用户的所有缓存文件

        Args:
            user_id: 用户ID

        Returns:
            用户的缓存文件字典
        """
        with self.lock:
            if user_id not in self.cache_data:
                return {}
            return self.cache_data[user_id].get('files', {})

    def get_user_cache_data(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户的详细缓存数据（用于前端显示）

        Args:
            user_id: 用户ID

        Returns:
            用户的详细缓存数据字典
        """
        with self.lock:
            if user_id not in self.cache_data:
                return {}

            user_cache = self.cache_data[user_id]
            files_data = user_cache.get('files', {})

            # 返回文件缓存数据，包含详细信息
            return files_data

    def clear_user_cache(self, user_id: str):
        """清除指定用户的缓存"""
        try:
            with self.lock:
                if user_id in self.cache_data:
                    del self.cache_data[user_id]
                    self._save_cache()
                    self.logger.info(f"已清除用户缓存: {user_id}")
        except Exception as e:
            self.logger.error(f"清除用户缓存失败 {user_id}: {e}")

    def clear_expired_cache(self):
        """清除过期的缓存"""
        try:
            with self.lock:
                users_to_remove = []
                files_removed = 0

                for user_id, user_cache in self.cache_data.items():
                    files_to_remove = []

                    for filename, cached_result in user_cache.get('files', {}).items():
                        if not self.is_cache_valid(cached_result):
                            files_to_remove.append(filename)

                    for filename in files_to_remove:
                        del user_cache['files'][filename]
                        files_removed += 1

                    # 如果用户没有任何缓存文件，移除用户
                    if not user_cache.get('files'):
                        users_to_remove.append(user_id)

                for user_id in users_to_remove:
                    del self.cache_data[user_id]

                if files_removed > 0 or users_to_remove:
                    self._save_cache()
                    self.logger.info(f"清除过期缓存: {files_removed} 个文件, {len(users_to_remove)} 个用户")

        except Exception as e:
            self.logger.error(f"清除过期缓存失败: {e}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            with self.lock:
                total_users = len(self.cache_data)
                total_files = sum(len(user_cache.get('files', {})) for user_cache in self.cache_data.values())

                valid_files = 0
                expired_files = 0

                for user_cache in self.cache_data.values():
                    for cached_result in user_cache.get('files', {}).values():
                        if self.is_cache_valid(cached_result):
                            valid_files += 1
                        else:
                            expired_files += 1

                return {
                    'total_users': total_users,
                    'total_files': total_files,
                    'valid_files': valid_files,
                    'expired_files': expired_files,
                    'cache_file_size': self.cache_file.stat().st_size if self.cache_file.exists() else 0
                }

        except Exception as e:
            self.logger.error(f"获取缓存统计失败: {e}")
            return {}
