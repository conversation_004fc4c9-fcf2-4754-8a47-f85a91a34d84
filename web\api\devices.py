"""
设备API模块 - 处理设备管理相关的API接口
"""

from flask import Blueprint, jsonify, request, current_app
from web.utils.decorators import login_required
from datetime import datetime
from core.utils.validators import validate_device_config
from web.clients.monitor_client_simple import simple_reload_config
import re


devices_api = Blueprint('devices_api', __name__)


def _reload_monitor_config():
    """重新加载监控服务配置的辅助函数"""
    try:
        success, data = simple_reload_config(current_app.logger)
        if success:
            current_app.logger.info("监控服务配置重载成功")
        else:
            current_app.logger.warning(f"监控服务配置重载失败: {data}")
    except Exception as e:
        current_app.logger.warning(f"无法重载监控服务配置: {str(e)}")


def _detect_key_confusion(device):
    """检测设备key值是否可能输入错误"""
    device_type = device.get('type', '')
    suggestions = []

    # 检测bark设备是否错误输入了其他类型的key
    if device_type == 'bark':
        bark_key = device.get('bark_key', '')
        uid = device.get('uid', '')
        pushme_key = device.get('pushme_key', '')

        # 检查是否在bark_key字段输入了UID格式的值
        if bark_key and re.match(r'^UID_\w+$', bark_key):
            suggestions.append('Bark Key不应该是UID格式，请检查是否输入了微信推送的UID')

        # 检查是否在其他字段输入了值
        if uid:
            suggestions.append('Bark设备不需要填写UID字段，请清空该字段')
        if pushme_key:
            suggestions.append('Bark设备不需要填写PushMe Key字段，请清空该字段')

    # 检测wxpush设备是否错误输入了其他类型的key
    elif device_type == 'wxpush':
        bark_key = device.get('bark_key', '')
        uid = device.get('uid', '')
        pushme_key = device.get('pushme_key', '')

        # 检查是否在uid字段输入了bark_key格式的值
        if uid and len(uid) > 20 and not uid.startswith('UID_'):
            suggestions.append('微信推送UID格式可能不正确，应该是UID_开头的格式')

        # 检查是否在其他字段输入了值
        if bark_key:
            suggestions.append('微信推送设备不需要填写Bark Key字段，请清空该字段')
        if pushme_key:
            suggestions.append('微信推送设备不需要填写PushMe Key字段，请清空该字段')

    # 检测pushme设备是否错误输入了其他类型的key
    elif device_type == 'pushme':
        bark_key = device.get('bark_key', '')
        uid = device.get('uid', '')
        pushme_key = device.get('pushme_key', '')

        # 检查是否在pushme_key字段输入了其他格式的值
        if pushme_key and pushme_key.startswith('UID_'):
            suggestions.append('PushMe Key不应该是UID格式，请检查是否输入了微信推送的UID')

        # 检查是否在其他字段输入了值
        if bark_key:
            suggestions.append('PushMe设备不需要填写Bark Key字段，请清空该字段')
        if uid:
            suggestions.append('PushMe设备不需要填写UID字段，请清空该字段')

    return suggestions


def _validate_device_with_smart_detection(device):
    """使用统一验证器并提供智能错误检测"""
    # 先使用现有的验证器进行基础验证
    is_valid, error_msg = validate_device_config(device)

    if not is_valid:
        # 如果基础验证失败，检查是否是key值混淆导致的
        confusion_suggestions = _detect_key_confusion(device)

        if confusion_suggestions:
            # 如果检测到可能的混淆，提供更详细的错误信息
            detailed_error = f"{error_msg}。可能的问题：" + "；".join(confusion_suggestions)
            return False, detailed_error

        return False, error_msg

    # 基础验证通过后，检查是否有潜在的key值混淆
    confusion_suggestions = _detect_key_confusion(device)
    if confusion_suggestions:
        warning_msg = "注意：" + "；".join(confusion_suggestions)
        return False, warning_msg

    return True, ''


@devices_api.route('/devices')
@login_required
def get_devices():
    """获取设备列表"""
    try:
        config_manager = current_app.config_manager
        # 从数据库获取设备列表
        devices = config_manager.get_devices()

        # 获取查询参数
        device_type = request.args.get('type', '').strip()  # 设备类型筛选
        search_query = request.args.get('search', '').strip()  # 搜索关键词
        sort_type = request.args.get('sort', 'created_desc').strip()  # 排序方式
        page = int(request.args.get('page', 1))  # 页码，默认第1页
        per_page = int(request.args.get('per_page', 15))  # 每页数量，默认15个

        # 为每个设备添加过期状态信息
        today = datetime.now().date()
        for device in devices:
            expire_date_str = device.get('expire_date')
            if expire_date_str:
                try:
                    expire_date = datetime.strptime(expire_date_str, '%Y-%m-%d').date()
                    diff_days = (expire_date - today).days

                    if diff_days < 0:
                        device['expire_status'] = 'expired'
                        device['expire_days_left'] = diff_days
                    elif diff_days <= 3:
                        device['expire_status'] = 'expiring'
                        device['expire_days_left'] = diff_days
                    else:
                        device['expire_status'] = 'normal'
                        device['expire_days_left'] = diff_days
                except ValueError:
                    device['expire_status'] = 'normal'
                    device['expire_days_left'] = None
            else:
                device['expire_status'] = 'normal'
                device['expire_days_left'] = None

        # 筛选设备
        filtered_devices = devices

        # 按设备类型筛选
        if device_type and device_type != 'all':
            filtered_devices = [d for d in filtered_devices if d.get('type') == device_type]

        # 按搜索关键词筛选（设备名称）
        if search_query:
            filtered_devices = [d for d in filtered_devices if search_query.lower() in d.get('name', '').lower()]

        # 排序逻辑（在分页前进行）
        def sort_devices(device_list, sort_type):
            if sort_type == 'name':
                return sorted(device_list, key=lambda d: d.get('name', ''))
            elif sort_type == 'type':
                return sorted(device_list, key=lambda d: d.get('type', ''))
            elif sort_type == 'created_desc':
                return sorted(device_list, key=lambda d: d.get('created_at', 0), reverse=True)
            elif sort_type == 'created_asc':
                return sorted(device_list, key=lambda d: d.get('created_at', 0))
            elif sort_type == 'expire_asc':
                # 按过期时间升序，已过期的优先，然后是即将过期的，最后是正常的
                def expire_sort_key(d):
                    expire_date_str = d.get('expire_date')
                    if not expire_date_str:
                        return (2, datetime.max.date())  # 无过期时间的放在最后
                    try:
                        expire_date = datetime.strptime(expire_date_str, '%Y-%m-%d').date()
                        diff_days = (expire_date - today).days
                        if diff_days < 0:
                            return (0, expire_date)  # 已过期的优先显示
                        else:
                            return (1, expire_date)  # 未过期的按过期时间排序
                    except ValueError:
                        return (2, datetime.max.date())

                return sorted(device_list, key=expire_sort_key)
            else:
                return device_list

        # 对筛选后的设备进行排序
        sorted_devices = sort_devices(filtered_devices, sort_type)

        # 计算分页信息
        total_count = len(sorted_devices)
        total_pages = (total_count + per_page - 1) // per_page  # 向上取整
        start_index = (page - 1) * per_page
        end_index = start_index + per_page

        # 获取当前页的设备
        page_devices = sorted_devices[start_index:end_index]

        # 统计各类型设备数量（基于原始设备列表，不受筛选影响）
        device_stats = {
            'total': len(devices),
            'bark': len([d for d in devices if d.get('type') == 'bark']),
            'wxpush': len([d for d in devices if d.get('type') == 'wxpush']),
            'pushme': len([d for d in devices if d.get('type') == 'pushme']),
            'other': len([d for d in devices if d.get('type') not in ['bark', 'wxpush', 'pushme']])
        }

        # 过期状态统计（修复统计逻辑）
        expired_count = 0
        expiring_count = 0

        for device in devices:
            expire_status = device.get('expire_status', 'normal')
            if expire_status == 'expired':
                expired_count += 1
            elif expire_status == 'expiring':
                expiring_count += 1

        device_stats['expired'] = expired_count
        device_stats['expiring'] = expiring_count

        return jsonify({
            'devices': page_devices,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total_count': total_count,
                'total_pages': total_pages,
                'has_prev': page > 1,
                'has_next': page < total_pages
            },
            'stats': device_stats,
            'filters': {
                'type': device_type,
                'search': search_query,
                'sort': sort_type
            }
        })
    except Exception as e:
        current_app.logger.error(f"获取设备列表失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@devices_api.route('/devices/<device_id>', methods=['GET'])
@login_required
def get_device(device_id):
    """获取单个设备信息"""
    try:
        config_manager = current_app.config_manager
        # 从数据库获取设备信息
        device = config_manager.get_data_manager().get_device(device_id)

        if device:
            return jsonify({'device': device})
        else:
            return jsonify({'error': f'未找到设备ID: {device_id}'}), 404
    except Exception as e:
        current_app.logger.error(f"获取设备信息失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@devices_api.route('/devices', methods=['POST'])
@login_required
def add_device():
    """添加新设备"""
    try:
        config_manager = current_app.config_manager
        new_device = request.json

        # 使用统一验证器进行智能验证
        is_valid, error_msg = _validate_device_with_smart_detection(new_device)
        if not is_valid:
            return jsonify({'error': error_msg}), 400

        # 检查设备是否已存在
        data_manager = config_manager.get_data_manager()
        existing_device = data_manager.get_device(new_device.get('id'))
        if existing_device:
            return jsonify({'error': f'设备ID已存在: {new_device.get("id")}'}), 400

        # 保存新设备到数据库
        data_manager.save_device(new_device)
        current_app.logger.info(f"成功添加设备: {new_device.get('id')} - {new_device.get('name')}")

        # 重新加载监控配置
        _reload_monitor_config()

        return jsonify({'status': 'success', 'device': new_device})

    except Exception as e:
        current_app.logger.error(f"添加设备失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@devices_api.route('/devices/<device_id>', methods=['PUT'])
@login_required
def update_device(device_id):
    """更新设备信息"""
    try:
        config_manager = current_app.config_manager
        updated_device = request.json

        # 检查设备是否存在
        data_manager = config_manager.get_data_manager()
        existing_device = data_manager.get_device(device_id)
        if not existing_device:
            return jsonify({'error': f'未找到设备ID: {device_id}'}), 404

        # 保留原始ID，更新其他信息
        updated_device['id'] = device_id

        # 使用统一验证器进行智能验证
        is_valid, error_msg = _validate_device_with_smart_detection(updated_device)
        if not is_valid:
            return jsonify({'error': error_msg}), 400

        # 保存到数据库
        data_manager.save_device(updated_device)

        # 重新加载监控配置
        _reload_monitor_config()

        return jsonify({'status': 'success', 'device': updated_device})

    except Exception as e:
        current_app.logger.error(f"更新设备失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@devices_api.route('/devices/batch_update_expire', methods=['POST'])
@login_required
def batch_update_device_expire():
    """批量更新设备到期时间"""
    try:
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()
        request_data = request.json

        # 验证请求数据结构
        if not isinstance(request_data, dict):
            return jsonify({'error': '请求数据格式错误'}), 400

        # 验证必要的字段
        if 'device_ids' not in request_data or not isinstance(request_data['device_ids'], list):
            return jsonify({'error': '缺少有效的设备ID列表'}), 400

        if 'expire_date' not in request_data:
            return jsonify({'error': '缺少到期时间字段'}), 400

        device_ids = request_data['device_ids']
        expire_date = request_data['expire_date']

        if not device_ids:
            return jsonify({'error': '设备ID列表不能为空'}), 400

        # 验证到期时间格式
        try:
            if expire_date:
                datetime.strptime(expire_date, '%Y-%m-%d')
        except ValueError:
            return jsonify({'error': '到期时间格式错误，应为 YYYY-MM-DD'}), 400

        # 记录更新结果
        updated_devices = []
        not_found_devices = []
        error_devices = []

        # 批量更新设备
        for device_id in device_ids:
            try:
                # 查找设备
                device = data_manager.get_device(device_id)
                if not device:
                    not_found_devices.append({
                        'device_id': device_id,
                        'error': '设备不存在'
                    })
                    continue

                original_expire_date = device.get('expire_date')

                # 更新到期时间
                device['expire_date'] = expire_date

                # 更新数据库中的设备
                data_manager.save_device(device)
                updated_devices.append({
                    'device_id': device_id,
                    'original_expire_date': original_expire_date,
                    'new_expire_date': expire_date
                })

            except Exception as e:
                current_app.logger.error(f"批量更新设备 {device_id} 到期时间失败: {str(e)}")
                error_devices.append({
                    'device_id': device_id,
                    'error': str(e)
                })

        # 批量操作完成后重新加载监控配置
        if updated_devices:
            _reload_monitor_config()

        # 构建响应结果
        result = {
            'status': 'completed',
            'summary': {
                'total_updates': len(device_ids),
                'successful_updates': len(updated_devices),
                'not_found_devices': len(not_found_devices),
                'error_devices': len(error_devices)
            }
        }

        # 添加详细结果
        if updated_devices:
            result['updated_devices'] = updated_devices

        if not_found_devices:
            result['not_found_devices'] = not_found_devices

        if error_devices:
            result['error_devices'] = error_devices

        # 根据结果确定HTTP状态码
        if not updated_devices:
            if not_found_devices or error_devices:
                return jsonify(result), 400
            else:
                return jsonify(result), 400
        else:
            if not_found_devices or error_devices:
                return jsonify(result), 207  # 部分成功
            else:
                return jsonify(result), 200  # 完全成功

    except Exception as e:
        current_app.logger.error(f"批量更新设备到期时间失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@devices_api.route('/devices/<device_id>', methods=['DELETE'])
@login_required
def delete_device(device_id):
    """删除设备"""
    try:
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()

        # 检查设备是否存在
        device = data_manager.get_device(device_id)
        if not device:
            return jsonify({'error': f'未找到设备ID: {device_id}'}), 404

        # 从数据库获取监控配置
        monitor_configs = data_manager.get_monitor_configs()

        # 检查该设备是否被房源使用
        used_by_houses = []
        for house_config in monitor_configs:
            if device_id in house_config.get('device_ids', []):
                used_by_houses.append(house_config.get('name'))

        if used_by_houses:
            # 返回使用此设备的房源列表
            return jsonify({
                'error': '该设备正在被以下房源使用，无法删除',
                'houses': used_by_houses
            }), 400

        # 删除设备
        data_manager.delete_device(device_id)

        # 重新加载监控配置
        _reload_monitor_config()

        return jsonify({'status': 'success', 'removed': device})

    except Exception as e:
        current_app.logger.error(f"删除设备失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@devices_api.route('/devices/<device_id>/houses', methods=['GET'])
@login_required
def get_device_houses(device_id):
    """获取使用指定设备的房源列表"""
    try:
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()

        # 从数据库获取设备信息
        device = data_manager.get_device(device_id)
        if not device:
            return jsonify({'error': f'未找到设备ID: {device_id}'}), 404

        # 从数据库获取监控配置
        monitor_configs = data_manager.get_monitor_configs()

        # 查找使用此设备的房源
        used_by_houses = []
        for house_config in monitor_configs:
            if device_id in house_config.get('device_ids', []):
                house_info = {
                    'name': house_config.get('name', '未命名房源'),
                    'enabled': house_config.get('enabled', True)
                }
                used_by_houses.append(house_info)

        return jsonify({
            'device': {
                'id': device_id,
                'name': device.get('name', '未命名设备'),
                'type': device.get('type', 'unknown')
            },
            'houses': used_by_houses,
            'total': len(used_by_houses)
        })

    except Exception as e:
        current_app.logger.error(f"获取设备使用情况失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@devices_api.route('/houses/<house_name>/devices/<device_id>', methods=['DELETE'])
@login_required
def remove_device_from_house(house_name, device_id):
    """从房源的监控设备列表中移除设备"""
    try:
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()

        # 从数据库获取房源配置
        monitor_configs = data_manager.get_monitor_configs()
        house_config = next((c for c in monitor_configs if c.get('name') == house_name), None)
        if house_config is None:
            return jsonify({'error': f'未找到房源: {house_name}'}), 404

        # 检查该设备是否在房源的监控列表中
        device_ids = house_config.get('device_ids', [])
        if device_id not in device_ids:
            return jsonify({'error': f'设备 {device_id} 不在房源 {house_name} 的监控列表中'}), 400

        # 从监控列表中移除设备
        device_ids.remove(device_id)
        house_config['device_ids'] = device_ids

        # 更新数据库中的监控配置
        data_manager.save_monitor_config(house_config)

        # 重新加载监控配置
        _reload_monitor_config()

        return jsonify({
            'status': 'success',
            'message': f'已将设备 {device_id} 从房源 {house_name} 的监控列表中移除',
            'remaining_devices': len(device_ids)
        })

    except Exception as e:
        current_app.logger.error(f"从房源移除设备失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@devices_api.route('/houses/<house_name>/devices/<device_id>', methods=['PUT'])
@login_required
def add_device_to_house(house_name, device_id):
    """将设备添加到房源的监控设备列表中"""
    try:
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()

        # 检查设备是否存在
        device = data_manager.get_device(device_id)
        if not device:
            return jsonify({'error': f'未找到设备ID: {device_id}'}), 404

        # 从数据库获取房源配置
        monitor_configs = data_manager.get_monitor_configs()
        house_config = next((c for c in monitor_configs if c.get('name') == house_name), None)
        if house_config is None:
            return jsonify({'error': f'未找到房源: {house_name}'}), 404

        # 检查该设备是否已在房源的监控列表中
        device_ids = house_config.get('device_ids', [])
        if device_id in device_ids:
            return jsonify({'error': f'设备 {device_id} 已在房源 {house_name} 的监控列表中'}), 400

        # 添加设备到监控列表
        device_ids.append(device_id)
        house_config['device_ids'] = device_ids

        # 更新数据库中的监控配置
        data_manager.save_monitor_config(house_config)

        # 重新加载监控配置
        _reload_monitor_config()

        return jsonify({
            'status': 'success',
            'message': f'已将设备 {device_id} 添加到房源 {house_name} 的监控列表中',
            'total_devices': len(device_ids)
        })

    except Exception as e:
        current_app.logger.error(f"向房源添加设备失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@devices_api.route('/devices/batch_update', methods=['POST'])
@login_required
def batch_update_devices():
    """通用批量设备更新API"""
    try:
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()
        request_data = request.json

        # 验证请求数据结构
        if not isinstance(request_data, dict):
            return jsonify({'error': '请求数据格式错误'}), 400

        # 验证必要的字段
        if 'updates' not in request_data or not isinstance(request_data['updates'], list):
            return jsonify({'error': '缺少有效的更新列表'}), 400

        updates = request_data['updates']
        if not updates:
            return jsonify({'error': '更新列表不能为空'}), 400

        # 验证每个更新项的格式
        for i, update in enumerate(updates):
            if not isinstance(update, dict):
                return jsonify({'error': f'更新项 {i+1} 格式错误'}), 400

            if 'device_id' not in update:
                return jsonify({'error': f'更新项 {i+1} 缺少设备ID'}), 400

            if 'fields' not in update or not isinstance(update['fields'], dict):
                return jsonify({'error': f'更新项 {i+1} 缺少或格式错误的字段'}), 400

        # 记录更新结果
        updated_devices = []
        not_found_devices = []
        error_devices = []

        # 批量更新设备
        for update in updates:
            device_id = update['device_id']
            fields_to_update = update['fields']

            try:
                # 查找设备
                device = data_manager.get_device(device_id)
                if not device:
                    not_found_devices.append({
                        'device_id': device_id,
                        'error': '设备不存在'
                    })
                    continue

                original_device = device.copy()

                # 验证设备字段更新
                validation_error = _validate_device_fields(device, fields_to_update)
                if validation_error:
                    error_devices.append({
                        'device_id': device_id,
                        'error': validation_error
                    })
                    continue

                # 更新设备字段
                for field, value in fields_to_update.items():
                    if field != 'id':  # 保护设备ID不被修改
                        device[field] = value

                # 保留原始ID
                device['id'] = device_id

                # 更新数据库中的设备
                data_manager.save_device(device)
                updated_devices.append({
                    'device_id': device_id,
                    'original': original_device,
                    'updated': device,
                    'changes': fields_to_update
                })

            except Exception as e:
                current_app.logger.error(f"批量更新设备 {device_id} 失败: {str(e)}")
                error_devices.append({
                    'device_id': device_id,
                    'error': str(e)
                })

        # 批量操作完成后重新加载监控配置
        if updated_devices:
            _reload_monitor_config()

        # 构建响应结果
        result = {
            'status': 'completed',
            'summary': {
                'total_updates': len(updates),
                'successful_updates': len(updated_devices),
                'not_found_devices': len(not_found_devices),
                'error_devices': len(error_devices)
            }
        }

        # 添加详细结果
        if updated_devices:
            result['updated_devices'] = updated_devices

        if not_found_devices:
            result['not_found_devices'] = not_found_devices

        if error_devices:
            result['error_devices'] = error_devices

        # 根据结果确定HTTP状态码
        if not updated_devices:
            if not_found_devices or error_devices:
                return jsonify(result), 400
            else:
                return jsonify(result), 400
        else:
            if not_found_devices or error_devices:
                return jsonify(result), 207  # 部分成功
            else:
                return jsonify(result), 200  # 完全成功

    except Exception as e:
        current_app.logger.error(f"批量更新设备失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


def _validate_device_fields(device, fields_to_update):
    """验证设备字段更新的有效性（增强版本，包含智能检测）"""
    try:
        # 创建临时设备对象进行验证
        temp_device = device.copy()
        temp_device.update(fields_to_update)

        # 使用统一验证器进行完整验证
        is_valid, error_msg = _validate_device_with_smart_detection(temp_device)
        if not is_valid:
            return error_msg

        # 验证过期时间格式（补充验证）
        if 'expire_date' in fields_to_update:
            expire_date = fields_to_update['expire_date']
            if expire_date and expire_date != '':
                try:
                    datetime.strptime(expire_date, '%Y-%m-%d')
                except ValueError:
                    return '过期时间格式错误，应为YYYY-MM-DD格式'

        # 检测部分字段更新时的潜在问题
        device_type = temp_device.get('type', device.get('type'))

        # 如果用户只更新了key字段但没有更新类型，检查是否匹配
        if 'type' not in fields_to_update:
            # 检查新输入的key值是否与现有设备类型匹配
            confusion_suggestions = _detect_key_confusion(temp_device)
            if confusion_suggestions:
                return "字段更新检测到问题：" + "；".join(confusion_suggestions)

        return None  # 验证通过

    except Exception as e:
        return f'字段验证失败: {str(e)}'


@devices_api.route('/devices/check_expiry', methods=['POST'])
@login_required
def check_device_expiry():
    """手动检测设备过期状态并发送通知"""
    try:
        # 检查是否有设备过期检查器实例
        if hasattr(current_app, 'device_expiry_checker') and current_app.device_expiry_checker:
            # 使用DeviceExpiryChecker的manual_check_devices方法
            current_app.logger.info("使用DeviceExpiryChecker执行手动设备过期检查")
            check_result = current_app.device_expiry_checker.manual_check_devices()
        else:
            # Fallback: 使用内置的设备过期检查逻辑
            current_app.logger.info("DeviceExpiryChecker不可用，使用内置逻辑执行设备过期检查")
            check_result = _manual_check_device_expiry()

        return jsonify({
            'status': 'success',
            'message': '设备过期检查完成',
            'result': check_result
        })

    except Exception as e:
        current_app.logger.error(f"手动检测设备过期失败: {str(e)}")
        return jsonify({'error': f'检测失败: {str(e)}'}), 500


def _manual_check_device_expiry():
    """内置的设备过期检查逻辑（Fallback）"""
    from datetime import datetime, date

    config_manager = current_app.config_manager
    data_manager = config_manager.get_data_manager()

    # 获取所有设备
    device_list = data_manager.get_devices()

    # 获取当前日期
    today = date.today()

    # 统计信息
    expired_devices = []
    expiring_devices = []
    notifications_sent = []
    notification_errors = []
    processed_device_ids = set()

    for device in device_list:
        device_id = device.get('id')

        # 避免重复处理同一设备
        if device_id in processed_device_ids:
            continue

        processed_device_ids.add(device_id)
        device_name = device.get('name', device_id)
        expire_date_str = device.get('expire_date')

        # 如果没有设置过期时间，则跳过
        if not expire_date_str:
            continue

        try:
            # 解析过期日期
            expire_date = datetime.strptime(expire_date_str, "%Y-%m-%d").date()

            # 检查是否已过期
            if expire_date <= today:
                current_app.logger.warning(f"发现过期设备: {device_name} (ID: {device_id})")
                expired_devices.append(device)

                # 尝试发送过期通知
                try:
                    notification_result = _send_device_expiry_notification(device, "已过期")
                    if notification_result:
                        notifications_sent.append({
                            'device_id': device_id,
                            'device_name': device_name,
                            'type': 'expired',
                            'expire_date': expire_date_str
                        })
                except Exception as e:
                    notification_errors.append({
                        'device_id': device_id,
                        'device_name': device_name,
                        'error': str(e)
                    })
            else:
                # 计算剩余天数
                days_left = (expire_date - today).days

                # 检查是否即将过期（2天内）
                if days_left <= 2:
                    current_app.logger.info(f"发现即将过期设备: {device_name} (ID: {device_id}), 剩余 {days_left} 天")
                    expiring_devices.append((device, days_left))

                    # 尝试发送即将过期通知
                    try:
                        notification_result = _send_device_expiry_notification(device, f"剩余{days_left}天过期")
                        if notification_result:
                            notifications_sent.append({
                                'device_id': device_id,
                                'device_name': device_name,
                                'type': 'expiring',
                                'expire_date': expire_date_str,
                                'days_left': days_left
                            })
                    except Exception as e:
                        notification_errors.append({
                            'device_id': device_id,
                            'device_name': device_name,
                            'error': str(e)
                        })

        except ValueError:
            current_app.logger.error(f"设备 {device_name} 的过期日期格式无效: {expire_date_str}")

    # 构建结果
    result = {
        'total_devices_checked': len(processed_device_ids),
        'expired_count': len(expired_devices),
        'expiring_count': len(expiring_devices),
        'notifications_sent': len(notifications_sent),
        'notification_errors': len(notification_errors),
        'expired_devices': [{'id': d.get('id'), 'name': d.get('name'), 'expire_date': d.get('expire_date')} for d in expired_devices],
        'expiring_devices': [{'id': d[0].get('id'), 'name': d[0].get('name'), 'expire_date': d[0].get('expire_date'), 'days_left': d[1]} for d in expiring_devices],
        'notifications': notifications_sent,
        'errors': notification_errors
    }

    current_app.logger.info(f"内置设备过期检查完成: {len(expired_devices)} 个已过期, {len(expiring_devices)} 个即将过期, {len(notifications_sent)} 条通知已发送")

    return result


def _send_device_expiry_notification(device, status_msg):
    """发送设备过期通知"""
    try:
        # 检查是否有通知服务
        if not hasattr(current_app, 'notification_service'):
            current_app.logger.warning("通知服务不可用，跳过通知发送")
            return False

        notification_service = current_app.notification_service
        device_name = device.get('name', device.get('id'))

        # 构建通知消息
        title = "设备过期提醒"
        message = f"设备 {device_name} {status_msg}，请及时处理。"

        # 使用正确的通知服务接口发送通知
        try:
            from services.notification.client import schedule_async_task
            coro = notification_service.send(device, title, message)
            future = schedule_async_task(coro)

            # 等待结果，设置30秒超时
            result = future.result(timeout=30)

            if result:
                current_app.logger.info(f"设备过期通知已发送: {device_name} - {status_msg}")
                return True
            else:
                current_app.logger.warning(f"设备过期通知发送失败: {device_name} - {status_msg}")
                return False

        except Exception as e:
            current_app.logger.error(f"调用通知服务失败: {str(e)}")
            return False

    except Exception as e:
        current_app.logger.error(f"发送设备过期通知失败: {str(e)}")
        return False


