"""
监控服务API服务器 - 基于aiohttp的轻量级HTTP API
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional
from aiohttp import web, web_request
import json

from monitor_service.config import monitor_config


class MonitorAPIServer:
    """监控服务API服务器"""

    def __init__(self, monitor_instance=None):
        self.app = web.Application()
        self.monitor = monitor_instance
        self.logger = logging.getLogger("MonitorAPIServer")
        self._setup_routes()

    def _setup_routes(self):
        """设置API路由"""
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/status', self.get_status)
        self.app.router.add_post('/start', self.start_monitor)
        self.app.router.add_post('/stop', self.stop_monitor)
        self.app.router.add_post('/reload-config', self.reload_config)
        self.app.router.add_get('/proxy/status', self.get_proxy_status)
        self.app.router.add_post('/proxy/refresh', self.refresh_proxy)

    async def health_check(self, request: web_request.Request) -> web.Response:
        """健康检查端点"""
        return web.json_response({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'service': 'monitor-service',
            'version': '2.0.0'
        })

    async def get_status(self, request: web_request.Request) -> web.Response:
        """获取监控服务状态"""
        if not self.monitor:
            return web.json_response({
                'error': '监控实例未初始化'
            }, status=500)

        try:
            # 获取监控状态
            is_running = self.monitor.state_manager.is_running if hasattr(self.monitor, 'state_manager') else False

            # 获取代理状态
            proxy_info = {}
            if hasattr(self.monitor, 'proxy_manager'):
                proxy_info = {
                    'current_proxy': self.monitor.proxy_manager.current_proxy,
                    'last_fetch_time': self.monitor.proxy_manager.last_fetch_time,
                    'remaining_count': self.monitor.proxy_manager.remaining_count
                }

            return web.json_response({
                'is_running': is_running,
                'timestamp': datetime.now().isoformat(),
                'proxy': proxy_info,
                'service': 'monitor-service'
            })
        except Exception as e:
            self.logger.error(f"获取状态失败: {str(e)}")
            return web.json_response({
                'error': f'获取状态失败: {str(e)}'
            }, status=500)

    async def start_monitor(self, request: web_request.Request) -> web.Response:
        """启动监控"""
        if not self.monitor:
            return web.json_response({
                'error': '监控实例未初始化'
            }, status=500)

        try:
            if hasattr(self.monitor, 'state_manager') and self.monitor.state_manager.is_running:
                return web.json_response({
                    'status': 'already_running',
                    'message': '监控已在运行中'
                })

            # 启动监控（异步）
            asyncio.create_task(self._start_monitor_task())

            return web.json_response({
                'status': 'starting',
                'message': '监控正在启动',
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            self.logger.error(f"启动监控失败: {str(e)}")
            return web.json_response({
                'error': f'启动监控失败: {str(e)}'
            }, status=500)

    async def stop_monitor(self, request: web_request.Request) -> web.Response:
        """停止监控"""
        if not self.monitor:
            return web.json_response({
                'error': '监控实例未初始化'
            }, status=500)

        try:
            if hasattr(self.monitor, 'state_manager'):
                if not self.monitor.state_manager.is_running:
                    return web.json_response({
                        'status': 'not_running',
                        'message': '监控未在运行'
                    })

                # 请求停止监控
                self.monitor.state_manager.stop()

                return web.json_response({
                    'status': 'stopping',
                    'message': '监控正在停止',
                    'timestamp': datetime.now().isoformat()
                })
            else:
                return web.json_response({
                    'error': '状态管理器不可用'
                }, status=500)
        except Exception as e:
            self.logger.error(f"停止监控失败: {str(e)}")
            return web.json_response({
                'error': f'停止监控失败: {str(e)}'
            }, status=500)

    async def reload_config(self, request: web_request.Request) -> web.Response:
        """重载配置"""
        if not self.monitor:
            return web.json_response({
                'error': '监控实例未初始化'
            }, status=500)

        try:
            # 重载配置
            success = self.monitor.load_config() if hasattr(self.monitor, 'load_config') else False

            if success:
                return web.json_response({
                    'status': 'success',
                    'message': '配置重载成功',
                    'timestamp': datetime.now().isoformat()
                })
            else:
                return web.json_response({
                    'status': 'failed',
                    'message': '配置重载失败'
                }, status=500)
        except Exception as e:
            self.logger.error(f"重载配置失败: {str(e)}")
            return web.json_response({
                'error': f'重载配置失败: {str(e)}'
            }, status=500)

    async def get_proxy_status(self, request: web_request.Request) -> web.Response:
        """获取代理状态"""
        if not self.monitor or not hasattr(self.monitor, 'proxy_manager'):
            return web.json_response({
                'error': '代理管理器不可用'
            }, status=500)

        try:
            proxy_manager = self.monitor.proxy_manager
            return web.json_response({
                'current_proxy': proxy_manager.current_proxy,
                'last_fetch_time': proxy_manager.last_fetch_time,
                'remaining_count': proxy_manager.remaining_count,
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            self.logger.error(f"获取代理状态失败: {str(e)}")
            return web.json_response({
                'error': f'获取代理状态失败: {str(e)}'
            }, status=500)

    async def refresh_proxy(self, request: web_request.Request) -> web.Response:
        """刷新代理"""
        if not self.monitor or not hasattr(self.monitor, 'proxy_manager'):
            return web.json_response({
                'error': '代理管理器不可用'
            }, status=500)

        try:
            proxy_manager = self.monitor.proxy_manager
            old_proxy = proxy_manager.current_proxy

            # 异步更新代理
            success = await proxy_manager.update_proxy_async()

            new_proxy = proxy_manager.current_proxy

            return web.json_response({
                'status': 'success' if success else 'failed',
                'old_proxy': old_proxy,
                'new_proxy': new_proxy,
                'remaining_count': proxy_manager.remaining_count,
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            self.logger.error(f"刷新代理失败: {str(e)}")
            return web.json_response({
                'error': f'刷新代理失败: {str(e)}'
            }, status=500)

    async def _start_monitor_task(self):
        """启动监控任务的内部方法"""
        try:
            if hasattr(self.monitor, 'run'):
                await self.monitor.run()
        except Exception:
            import traceback
            self.logger.error("监控任务运行异常:", exc_info=True)

    def set_monitor(self, monitor_instance):
        """设置监控实例"""
        self.monitor = monitor_instance

    async def start_server(self):
        """启动API服务器"""
        runner = web.AppRunner(self.app)
        await runner.setup()

        site = web.TCPSite(runner, monitor_config.host, monitor_config.port)
        await site.start()

        self.logger.info(f"监控服务API服务器启动成功，监听 {monitor_config.host}:{monitor_config.port}")
        return runner