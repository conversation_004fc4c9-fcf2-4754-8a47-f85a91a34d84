import asyncio
import atexit
import logging
import weakref
from typing import Dict, Any, Set, Optional

# 全局记录器
logger = logging.getLogger("AppContext")

# 跟踪所有需要在程序退出时清理的资源
_cleanup_handlers = []
_aiohttp_sessions = weakref.WeakSet()
_event_loops = weakref.WeakSet()

# A simple dictionary to hold application-wide context
# to avoid circular imports and provide a central point of access.
app_context: Dict[str, Any] = {
    "background_event_loop": None,
    "notification_service": None,
    # A thread-safe way to signal the background loop to stop
    "stop_event": None,
}

def register_cleanup_handler(handler):
    """注册一个在程序退出时调用的清理函数"""
    _cleanup_handlers.append(handler)
    logger.debug(f"已注册清理处理程序: {handler.__name__ if hasattr(handler, '__name__') else 'unnamed'}")

def register_aiohttp_session(session):
    """注册一个aiohttp会话，以便在退出时检查是否已关闭"""
    _aiohttp_sessions.add(session)
    logger.debug(f"已注册aiohttp会话 (id: {id(session)})")

def register_event_loop(loop):
    """注册一个事件循环，以便在退出时关闭"""
    _event_loops.add(loop)
    logger.debug(f"已注册事件循环 (id: {id(loop)})")

def _cleanup_all():
    """在程序退出时执行所有注册的清理函数"""
    logger.info("执行程序退出清理...")

    # 执行所有注册的清理函数
    for handler in _cleanup_handlers:
        try:
            handler()
            logger.debug(f"已执行清理处理程序: {handler.__name__ if hasattr(handler, '__name__') else 'unnamed'}")
        except Exception as e:
            logger.warning(f"执行清理处理程序时出错: {e}")

    # 记录未关闭的会话数量
    unclosed_sessions = [session for session in _aiohttp_sessions if not session.closed]
    if unclosed_sessions:
        logger.warning(f"程序退出时有 {len(unclosed_sessions)} 个未关闭的aiohttp会话")

    # 记录未关闭的事件循环
    unclosed_loops = [loop for loop in _event_loops if not loop.is_closed()]
    if unclosed_loops:
        logger.warning(f"程序退出时有 {len(unclosed_loops)} 个未关闭的事件循环")

    logger.info("程序退出清理完成")

# 注册退出清理函数
atexit.register(_cleanup_all)