"""
统一日志配置模块
为所有服务提供一致的日志配置，包括Windows颜色兼容性、文件轮转、敏感数据脱敏
整合了原有多套日志系统的所有优秀特性
"""

import logging
import sys
import os
import re
import stat
import enum
import traceback
import codecs
from datetime import datetime
from typing import Optional, Dict, Any, Union
import threading
from pathlib import Path
from logging.handlers import TimedRotatingFileHandler


class LogLevel(enum.Enum):
    """日志级别枚举 - 兼容原Logger类"""
    DEBUG = 0
    INFO = 1
    WARNING = 2
    ERROR = 3
    CRITICAL = 4

    @staticmethod
    def get_level_name(level):
        """获取日志级别名称"""
        level_names = {
            LogLevel.DEBUG: "调试",
            LogLevel.INFO: "信息",
            LogLevel.WARNING: "警告",
            LogLevel.ERROR: "错误",
            LogLevel.CRITICAL: "严重"
        }
        return level_names.get(level, "未知")


class UTF8TimedRotatingFileHandler(TimedRotatingFileHandler):
    """支持UTF-8编码的定时轮转日志处理器"""

    def __init__(self, filename, **kwargs):
        filename = os.path.normpath(filename)
        kwargs['encoding'] = 'utf-8'
        TimedRotatingFileHandler.__init__(self, filename, **kwargs)

    def doRollover(self):
        """重写轮转方法，确保在Linux环境中正确创建新文件"""
        TimedRotatingFileHandler.doRollover(self)

        # 在Linux环境下设置新创建的日志文件权限
        if os.name != 'nt':
            try:
                log_dir = os.path.dirname(self.baseFilename)
                for file in os.listdir(log_dir):
                    if file.startswith(os.path.basename(self.baseFilename)) and file != os.path.basename(self.baseFilename):
                        log_file = os.path.join(log_dir, file)
                        os.chmod(log_file, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)
            except Exception as e:
                print(f"设置日志文件权限时出错: {str(e)}")


class SafeUTF8TimedRotatingFileHandler(UTF8TimedRotatingFileHandler):
    """安全的UTF-8编码日志处理器，支持emoji转换和编码错误处理"""

    def __init__(self, filename, enable_emoji_conversion=False, **kwargs):
        super().__init__(filename, **kwargs)
        self.enable_emoji_conversion = enable_emoji_conversion
        self._emoji_converter = None

    @property
    def emoji_converter(self):
        """延迟初始化emoji转换器"""
        if self._emoji_converter is None and self.enable_emoji_conversion:
            try:
                from .emoji_converter import get_emoji_converter
                self._emoji_converter = get_emoji_converter()
            except ImportError:
                self.enable_emoji_conversion = False
        return self._emoji_converter

    def emit(self, record):
        """安全的日志输出方法，减少处理失败"""
        try:
            # 格式化消息
            msg = self.format(record)

            # 确保消息是字符串类型
            if not isinstance(msg, str):
                msg = str(msg)

            # 如果启用了emoji转换，则转换消息
            if self.enable_emoji_conversion and self.emoji_converter:
                try:
                    msg = self.emoji_converter.make_console_safe(msg)
                except Exception:
                    # emoji转换失败，继续使用原消息
                    pass

            # 强制确保消息是UTF-8安全的
            try:
                # 测试UTF-8编码
                msg.encode('utf-8')
            except UnicodeEncodeError:
                # 如果编码失败，进行字符级别的安全处理
                safe_chars = []
                for char in msg:
                    try:
                        char.encode('utf-8')
                        safe_chars.append(char)
                    except UnicodeEncodeError:
                        # 对于无法编码的字符，使用Unicode转义序列
                        safe_chars.append(f'\\u{ord(char):04x}')
                msg = ''.join(safe_chars)

            # 写入文件
            stream = self.stream
            stream.write(msg + self.terminator)
            self.flush()

        except Exception:
            # 如果格式化失败，使用简化的安全输出
            try:
                # 构建简化的安全消息
                level = getattr(record, 'levelname', 'INFO')
                timestamp = getattr(record, 'created', 0)
                name = getattr(record, 'name', 'Unknown')

                # 尝试获取原始消息
                try:
                    raw_msg = getattr(record, 'msg', 'Unknown')
                    if hasattr(record, 'args') and record.args:
                        try:
                            raw_msg = raw_msg % record.args
                        except:
                            raw_msg = f"{raw_msg} {record.args}"
                except:
                    raw_msg = "消息格式化失败"

                # 构建安全消息
                import time
                time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
                safe_msg = f"{time_str} - {name} - {level} - {str(raw_msg)[:500]}"

                # 确保UTF-8安全
                try:
                    safe_msg.encode('utf-8')
                except UnicodeEncodeError:
                    # 字符级别安全处理
                    safe_chars = []
                    for char in safe_msg:
                        try:
                            char.encode('utf-8')
                            safe_chars.append(char)
                        except UnicodeEncodeError:
                            safe_chars.append(f'\\u{ord(char):04x}')
                    safe_msg = ''.join(safe_chars)

                stream = self.stream
                stream.write(safe_msg + self.terminator)
                self.flush()
            except:
                # 最后的回退：完全忽略这条日志记录以避免程序崩溃
                pass


class WindowsColorHandler(logging.StreamHandler):
    """Windows兼容的彩色日志处理器，支持UTF-8编码和emoji安全处理"""

    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[94m',    # 蓝色
        'INFO': '\033[92m',     # 绿色
        'WARNING': '\033[93m',  # 黄色
        'ERROR': '\033[91m',    # 红色
        'CRITICAL': '\033[95m', # 紫色
        'RESET': '\033[0m'      # 重置
    }

    def __init__(self, use_colors: bool = True, enable_emoji_conversion: bool = True):
        # 在Windows环境下使用UTF-8编码的流
        if sys.platform == 'win32':
            try:
                # 创建UTF-8编码的错误输出流
                utf8_stream = codecs.getwriter('utf-8')(sys.stderr.buffer, errors='replace')
                super().__init__(utf8_stream)
            except Exception:
                # 如果失败，使用默认流但禁用颜色
                super().__init__()
                use_colors = False
        else:
            super().__init__()

        self.use_colors = use_colors and self._supports_color()
        self.enable_emoji_conversion = enable_emoji_conversion
        self._emoji_converter = None

        # 在Windows上启用ANSI转义序列支持
        if sys.platform == 'win32' and self.use_colors:
            self._enable_windows_ansi()

    @property
    def emoji_converter(self):
        """延迟初始化emoji转换器"""
        if self._emoji_converter is None and self.enable_emoji_conversion:
            try:
                from .emoji_converter import get_emoji_converter
                self._emoji_converter = get_emoji_converter()
            except ImportError:
                self.enable_emoji_conversion = False
        return self._emoji_converter

    def _supports_color(self) -> bool:
        """检查终端是否支持颜色"""
        # 检查是否在IDE中运行
        if 'PYCHARM_HOSTED' in os.environ or 'VSCODE_PID' in os.environ:
            return True

        # 检查终端类型
        if sys.platform == 'win32':
            # Windows 10版本1607及以上支持ANSI
            try:
                import platform
                version = platform.version()
                # 简单检查：如果是Windows 10+，通常支持颜色
                return 'Windows-10' in version or int(version.split('.')[0]) >= 10
            except:
                return False

        # Unix系统通常支持颜色
        return hasattr(sys.stdout, 'isatty') and sys.stdout.isatty()

    def _enable_windows_ansi(self):
        """在Windows上启用ANSI转义序列支持"""
        try:
            import ctypes
            kernel32 = ctypes.windll.kernel32
            kernel32.SetConsoleMode(kernel32.GetStdHandle(-11), 7)
        except:
            # 如果失败，禁用颜色
            self.use_colors = False

    def _make_message_safe(self, msg: str) -> str:
        """确保消息对当前环境安全，采用宽松策略减少处理失败"""
        if not msg:
            return msg

        try:
            # 首先确保消息是字符串类型
            if not isinstance(msg, str):
                msg = str(msg)

            # 在Windows环境下，如果启用了emoji转换，先转换emoji
            if sys.platform == 'win32' and self.enable_emoji_conversion and self.emoji_converter:
                try:
                    msg = self.emoji_converter.make_console_safe(msg)
                except Exception:
                    # emoji转换失败，继续处理原消息
                    pass

            # 强制使用UTF-8编码，确保中文字符正确处理
            try:
                # 先尝试UTF-8编码测试
                msg.encode('utf-8')
                return msg  # UTF-8编码成功，直接返回
            except UnicodeEncodeError:
                # UTF-8编码失败，进行字符级别的安全处理
                safe_chars = []
                for char in msg:
                    try:
                        char.encode('utf-8')
                        safe_chars.append(char)
                    except UnicodeEncodeError:
                        # 对于无法编码的字符，使用Unicode转义序列
                        safe_chars.append(f'\\u{ord(char):04x}')
                return ''.join(safe_chars)

        except Exception:
            # 如果所有处理都失败，尝试最基本的字符串转换
            try:
                return str(msg)[:1000]  # 限制长度避免过长消息
            except Exception:
                # 最后的回退，返回长度信息而不是失败标记
                return f"[消息长度:{len(str(msg)) if msg else 0}]"

    def format(self, record):
        """格式化日志记录，添加颜色并确保编码安全"""
        try:
            formatted = super().format(record)

            # 确保消息安全
            formatted = self._make_message_safe(formatted)

            if self.use_colors:
                color = self.COLORS.get(record.levelname, '')
                reset = self.COLORS['RESET']
                return f"{color}{formatted}{reset}"

            return formatted
        except Exception:
            # 格式化失败时的回退
            safe_msg = f"[格式化失败] {record.levelname}: {getattr(record, 'msg', 'Unknown')}"
            return self._make_message_safe(safe_msg)

    def emit(self, record):
        """重写emit方法，实现多层次的安全输出"""
        try:
            # 第一层：正常格式化和输出
            msg = self.format(record)
            stream = self.stream
            stream.write(msg + self.terminator)
            self.flush()
            return

        except UnicodeEncodeError:
            # 第二层：Unicode编码错误处理
            try:
                # 重新处理消息，强制转换为安全格式
                raw_msg = getattr(record, 'msg', 'Unknown message')
                if hasattr(record, 'args') and record.args:
                    try:
                        raw_msg = raw_msg % record.args
                    except:
                        raw_msg = str(raw_msg) + " " + str(record.args)

                safe_msg = self._make_message_safe(str(raw_msg))
                timestamp = record.created
                level = record.levelname

                # 构建简化的安全消息
                simple_msg = f"[{level}] {safe_msg}"

                stream = self.stream
                stream.write(simple_msg + self.terminator)
                self.flush()
                return

            except Exception:
                pass

        except Exception:
            # 第三层：其他异常处理
            pass

        # 第四层：最后的安全回退，完全避免调用handleError防止无限循环
        try:
            fallback_msg = f"[LOG_ERROR_{record.levelname}]"
            stream = self.stream if hasattr(self, 'stream') and self.stream else sys.stderr
            stream.write(fallback_msg + self.terminator)
            stream.flush()
        except:
            # 如果连这个都失败了，完全静默，避免无限循环
            pass


class UnifiedLogger:
    """统一日志管理器 - 整合所有日志系统功能"""

    _instances = {}
    _lock = threading.Lock()

    def __init__(self, name: str, config: Optional[dict] = None):
        self.name = name

        # 获取默认配置并与传入的配置合并
        self.config = self._get_default_config()
        if config:
            self.config.update(config)

        # 根据环境自动调整配置
        if sys.platform == 'win32':
            # Windows环境启用emoji转换，减少编码问题
            self.config['enable_emoji_conversion'] = True

        self.logger = self._setup_logger()

        # 兼容原Logger类的统计功能
        self.push_success_count = 0
        self.push_fail_count = 0
        self.last_status_summary_time = datetime.now().timestamp()
        self.status_summary_interval = 1800

        # 敏感数据脱敏器
        self._masker = None
        self.enable_masking = self.config.get('enable_masking', True)

    @classmethod
    def get_logger(cls, name: str, config: Optional[dict] = None) -> 'UnifiedLogger':
        """获取或创建日志器实例（单例模式）"""
        with cls._lock:
            if name not in cls._instances:
                cls._instances[name] = cls(name, config)
            return cls._instances[name]

    @property
    def masker(self):
        """延迟初始化脱敏器"""
        if self._masker is None and self.enable_masking:
            try:
                from .sensitive_data_masker import SensitiveDataMasker
                self._masker = SensitiveDataMasker()
            except ImportError:
                self.enable_masking = False
        return self._masker

    def _get_default_config(self) -> dict:
        """获取默认日志配置，优先使用统一配置管理器的配置"""
        log_dir = Path(__file__).parent.parent.parent / 'log'
        log_dir.mkdir(exist_ok=True)

        # 尝试从统一配置管理器获取日志配置
        try:
            from config.unified_config import get_config
            unified_config = get_config()
            logging_config = unified_config.get_logging_config()

            if logging_config:
                # 使用统一配置管理器的配置
                config = {
                    'level': logging_config.get('level', 'INFO'),
                    'format': logging_config.get('format', '%(asctime)s,%(msecs)03d - %(name)s - %(levelname)s - %(message)s'),
                    'date_format': logging_config.get('date_format', '%Y-%m-%d %H:%M:%S'),
                    'use_colors': logging_config.get('use_colors', True),
                    'enable_masking': logging_config.get('enable_masking', True),
                    'log_file': str(log_dir / 'monitor.log'),
                    'max_bytes': 10 * 1024 * 1024,  # 10MB
                    'backup_count': 5,
                    'when': 'midnight',
                    'interval': 1,
                    'backup_count_timed': 30,
                    'enable_emoji_conversion': True,  # 启用emoji转换，减少编码问题
                    'encoding': 'utf-8'
                }
                return config
        except ImportError:
            # 如果统一配置管理器不可用，使用默认配置
            pass

        # 默认配置（包含毫秒格式）
        return {
            'level': 'INFO',
            'format': '%(asctime)s,%(msecs)03d - %(name)s - %(levelname)s - %(message)s',
            'date_format': '%Y-%m-%d %H:%M:%S',
            'use_colors': True,
            'enable_masking': True,
            'log_file': str(log_dir / 'monitor.log'),
            'max_bytes': 10 * 1024 * 1024,  # 10MB
            'backup_count': 5,
            'when': 'midnight',
            'interval': 1,
            'backup_count_timed': 30,
            'enable_emoji_conversion': True,  # 启用emoji转换，减少编码问题
            'encoding': 'utf-8'
        }

    def _setup_logger(self) -> logging.Logger:
        """设置日志器"""
        logger = logging.getLogger(self.name)

        # 避免重复添加处理器
        if logger.handlers:
            return logger

        # 设置日志级别
        level = getattr(logging, self.config['level'].upper(), logging.INFO)
        logger.setLevel(level)

        # 创建格式器
        formatter = logging.Formatter(
            fmt=self.config['format'],
            datefmt=self.config['date_format']
        )

        # 创建控制台处理器，在Windows环境下启用emoji转换
        console_emoji_conversion = self.config.get('enable_emoji_conversion', True)
        if sys.platform != 'win32':
            # 非Windows环境下通常不需要emoji转换
            console_emoji_conversion = False

        console_handler = WindowsColorHandler(
            use_colors=self.config['use_colors'],
            enable_emoji_conversion=console_emoji_conversion
        )
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)

        # 添加处理器
        logger.addHandler(console_handler)

        # 文件处理器
        if self.config.get('log_file'):
            self._setup_file_handler(logger, formatter)

        # 防止日志传播到根日志器
        logger.propagate = False

        return logger

    def _setup_file_handler(self, logger: logging.Logger, formatter: logging.Formatter):
        """设置文件日志处理器"""
        log_file_path = Path(self.config['log_file'])
        log_dir = log_file_path.parent
        log_dir.mkdir(exist_ok=True)

        # 在Linux环境下设置权限
        if os.name != 'nt':
            try:
                os.chmod(log_dir, stat.S_IRWXU | stat.S_IRGRP | stat.S_IXGRP | stat.S_IROTH | stat.S_IXOTH)
            except Exception:
                pass

        # 创建安全的文件处理器，启用emoji转换减少编码问题
        file_handler = SafeUTF8TimedRotatingFileHandler(
            str(log_file_path),
            when=self.config.get('when', 'midnight'),
            interval=self.config.get('interval', 1),
            backupCount=self.config.get('backup_count_timed', 30),
            enable_emoji_conversion=self.config.get('enable_emoji_conversion', True)
        )

        file_handler.suffix = "%Y-%m-%d.log"
        file_handler.extMatch = re.compile(r"^\d{4}-\d{2}-\d{2}\.log$")

        file_level = getattr(logging, self.config.get('level', 'INFO').upper(), logging.INFO)
        file_handler.setLevel(file_level)
        file_handler.setFormatter(formatter)

        logger.addHandler(file_handler)

    def _format_time(self, dt: datetime) -> str:
        """格式化时间，包含毫秒"""
        base_time = dt.strftime("%Y-%m-%d %H:%M:%S")
        milliseconds = dt.microsecond // 1000
        return f"{base_time},{milliseconds:03d}"

    def _safe_format_message(self, message: str) -> str:
        """安全格式化消息，自动脱敏敏感信息"""
        if not self.enable_masking or not self.masker:
            return message

        try:
            return self.masker.mask_patterns_in_string(message)
        except Exception:
            return message

    def _safe_format_context(self, context: Optional[Union[Dict, Exception]]) -> str:
        """安全格式化上下文信息"""
        if not context:
            return ""

        try:
            if isinstance(context, dict):
                if self.enable_masking and self.masker:
                    masked_context = self.masker.mask_dict(context)
                    context_str = ", ".join([f"{k}={v}" for k, v in masked_context.items()])
                else:
                    context_str = ", ".join([f"{k}={v}" for k, v in context.items()])
                return f" | 上下文: {context_str}"
            else:
                context_str = str(context)
                if self.enable_masking and self.masker:
                    context_str = self.masker.mask_patterns_in_string(context_str)
                return f" | 上下文: {context_str}"
        except Exception:
            return f" | 上下文: <格式化失败>"

    # 原Logger类兼容方法
    def log(self, message: str, level: LogLevel = LogLevel.INFO,
            house_name: Optional[str] = None, context: Optional[Union[Dict, Exception]] = None):
        """多级别日志输出方法，兼容原Logger类接口"""
        safe_message = self._safe_format_message(message)

        if house_name:
            safe_message = f"[{house_name}] {safe_message}"

        if context and level.value >= LogLevel.WARNING.value:
            context_str = self._safe_format_context(context)
            safe_message += context_str

        # 转换自定义级别到标准级别
        level_mapping = {
            LogLevel.DEBUG: logging.DEBUG,
            LogLevel.INFO: logging.INFO,
            LogLevel.WARNING: logging.WARNING,
            LogLevel.ERROR: logging.ERROR,
            LogLevel.CRITICAL: logging.CRITICAL
        }

        std_level = level_mapping.get(level, logging.INFO)
        self.logger.log(std_level, safe_message)

        # 如果是错误或严重级别，且有异常信息，添加堆栈跟踪
        if level.value >= LogLevel.ERROR.value and context and isinstance(context, Exception):
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def log_with_data(self, message: str, data: Any, level: LogLevel = LogLevel.INFO,
                     house_name: Optional[str] = None):
        """记录包含数据的日志，自动脱敏敏感数据"""
        if self.enable_masking and self.masker:
            safe_data = self.masker.mask_any(data)
        else:
            safe_data = data

        if isinstance(safe_data, (dict, list)):
            import json
            try:
                data_str = json.dumps(safe_data, ensure_ascii=False, indent=2)
                full_message = f"{message}\n数据: {data_str}"
            except (TypeError, ValueError):
                full_message = f"{message}\n数据: {str(safe_data)}"
        else:
            full_message = f"{message}\n数据: {safe_data}"

        self.log(full_message, level, house_name)

    # 便捷方法 - 兼容原Logger类
    def debug(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """调试级别日志"""
        self.log(message, LogLevel.DEBUG, house_name, context)

    def info(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """信息级别日志"""
        self.log(message, LogLevel.INFO, house_name, context)

    def warning(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """警告级别日志"""
        self.log(message, LogLevel.WARNING, house_name, context)

    def error(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """错误级别日志"""
        self.log(message, LogLevel.ERROR, house_name, context)

    def critical(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """严重错误级别日志"""
        self.log(message, LogLevel.CRITICAL, house_name, context)

    def debug_data(self, message: str, data: Any, house_name: Optional[str] = None):
        """调试级别数据日志"""
        self.log_with_data(message, data, LogLevel.DEBUG, house_name)

    def info_data(self, message: str, data: Any, house_name: Optional[str] = None):
        """信息级别数据日志"""
        self.log_with_data(message, data, LogLevel.INFO, house_name)

    def warning_data(self, message: str, data: Any, house_name: Optional[str] = None):
        """警告级别数据日志"""
        self.log_with_data(message, data, LogLevel.WARNING, house_name)

    def error_data(self, message: str, data: Any, house_name: Optional[str] = None):
        """错误级别数据日志"""
        self.log_with_data(message, data, LogLevel.ERROR, house_name)


def setup_unified_logging(name: str, config: Optional[dict] = None) -> logging.Logger:
    """
    设置统一日志配置的便捷函数

    Args:
        name: 日志器名称
        config: 可选的日志配置字典

    Returns:
        logging.Logger: 配置好的标准日志器（保持向后兼容）
    """
    unified_logger = UnifiedLogger.get_logger(name, config)
    return unified_logger.logger


def get_unified_logger(name: str, config: Optional[dict] = None) -> UnifiedLogger:
    """
    获取统一日志器实例（支持所有增强功能）

    Args:
        name: 日志器名称
        config: 可选的日志配置字典

    Returns:
        UnifiedLogger: 增强的日志器实例
    """
    return UnifiedLogger.get_logger(name, config)


def log_with_context(logger: logging.Logger, level: str, message: str,
                    service_name: Optional[str] = None, **kwargs):
    """
    带上下文的日志记录函数

    Args:
        logger: 日志器实例
        level: 日志级别
        message: 日志消息
        service_name: 可选的服务名称
        **kwargs: 额外的上下文信息
    """
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]

    # 构建消息前缀
    prefix_parts = [f"[{timestamp}]"]
    if service_name:
        prefix_parts.append(f"[{service_name}]")

    prefix = " ".join(prefix_parts)
    full_message = f"{prefix} {message}"

    # 添加额外的上下文信息
    if kwargs:
        context_parts = [f"{k}={v}" for k, v in kwargs.items()]
        full_message += f" ({', '.join(context_parts)})"

    # 记录日志
    log_method = getattr(logger, level.lower(), logger.info)
    log_method(full_message)


# 兼容性别名 - 用于替换原core.utils.logger.Logger
Logger = UnifiedLogger