import asyncio
import aiohttp
import time
from datetime import datetime
import logging
from typing import List, Optional, Dict, Any
import weakref

from core.context import app_context
from core.exceptions import (
    handle_exception, is_ignorable_ssl_exception,
    safe_async_cleanup, NotificationException, NetworkException
)
from core.ssl_manager import get_ssl_manager

# 改进的通知服务注册器
import threading
from typing import Set, List

class NotificationServiceRegistry:
    """通知服务注册器，管理所有活跃的NotificationService实例"""
    def __init__(self):
        self._services: Set[weakref.ref] = set()
        self._lock = threading.Lock()
        self._cleanup_counter = 0

    def register(self, service: 'NotificationService'):
        """注册服务实例"""
        with self._lock:
            # 创建弱引用，带有回调函数
            ref = weakref.ref(service, self._on_service_deleted)
            self._services.add(ref)

            # 定期清理无效引用
            self._cleanup_counter += 1
            if self._cleanup_counter % 10 == 0:
                self._cleanup_dead_refs()

    def _on_service_deleted(self, ref):
        """服务被删除时的回调"""
        with self._lock:
            self._services.discard(ref)

    def _cleanup_dead_refs(self):
        """清理已死亡的引用"""
        dead_refs = {ref for ref in self._services if ref() is None}
        self._services -= dead_refs

    def unregister(self, service: 'NotificationService'):
        """注销服务实例"""
        with self._lock:
            # 找到对应的弱引用并移除
            to_remove = None
            for ref in self._services:
                if ref() is service:
                    to_remove = ref
                    break
            if to_remove:
                self._services.remove(to_remove)

    def get_all(self) -> List['NotificationService']:
        """获取所有活跃的服务实例"""
        with self._lock:
            return [ref() for ref in self._services if ref() is not None]

    async def close_all(self):
        """关闭所有服务"""
        services = self.get_all()
        for service in services:
            try:
                await service.close()
            except Exception:
                pass

# 全局注册器
_service_registry = NotificationServiceRegistry()

class NotificationService:
    def __init__(self, config_manager, logger=None):
        self.config_manager = config_manager
        self.logger = logger if logger else logging.getLogger(__name__)
        self._session: Optional[aiohttp.ClientSession] = None
        self._session_lock: Optional[asyncio.Lock] = None

        # SSL配置管理器
        try:
            self.ssl_manager = get_ssl_manager()
        except Exception as e:
            self.logger.warning(f"SSL管理器初始化失败，使用默认SSL设置: {e}")
            self.ssl_manager = None

        # 将自身添加到全局会话跟踪器
        _service_registry.register(self)
        if logger:
            logger.debug("NotificationService实例已注册到全局跟踪器")

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取或创建HTTP会话，确保会话与当前事件循环匹配"""
        # 从上下文中获取后台事件循环
        current_loop = app_context.get('background_event_loop')
        if not current_loop:
            raise RuntimeError("后台事件循环未初始化。")

        # 确保锁也绑定到当前事件循环
        if self._session_lock is None:
            self._session_lock = asyncio.Lock()

        if (self._session is None or
            self._session.closed or
            getattr(self._session, '_loop', None) != current_loop):

            async with self._session_lock:
                # 再次检查，避免竞态条件
                if (self._session is None or
                    self._session.closed or
                    getattr(self._session, '_loop', None) != current_loop):

                    # 如果存在旧会话且未关闭，先关闭它
                    if self._session and not self._session.closed:
                        try:
                            await self._session.close()
                        except Exception:
                            pass  # 忽略关闭异常

                    # 创建连接器，配置连接池和SSL
                    ssl_context = None
                    if self.ssl_manager:
                        # 通知服务通常需要连接多个不同的域名，使用通用SSL配置
                        ssl_context = self.ssl_manager.get_aiohttp_ssl_param('notifications', 'generic')

                    connector = aiohttp.TCPConnector(
                        limit=100,  # 总连接池大小
                        limit_per_host=30,  # 每个主机的连接数限制
                        ttl_dns_cache=300,  # DNS缓存时间
                        use_dns_cache=True,
                        loop=current_loop,  # 显式指定事件循环
                        enable_cleanup_closed=True,  # 启用已关闭连接的清理
                        ssl=ssl_context  # 使用SSL配置
                    )

                    # 设置超时
                    timeout = aiohttp.ClientTimeout(
                        total=30,  # 总超时时间
                        connect=10,  # 连接超时
                        sock_read=15  # 读取超时
                    )

                    self._session = aiohttp.ClientSession(
                        connector=connector,
                        timeout=timeout,
                        headers={'User-Agent': 'DaiMaBB-Monitor/2.0'},
                        loop=current_loop  # 显式指定事件循环
                    )

                    # 注册到全局会话跟踪器
                    try:
                        from core.context import register_aiohttp_session
                        register_aiohttp_session(self._session)
                    except (ImportError, AttributeError) as e:
                        # 如果无法导入或注册函数不存在，则静默处理
                        if hasattr(self, 'logger') and self.logger:
                            self.logger.debug(f"无法注册会话到全局跟踪器: {e}")

                    if hasattr(self, 'logger') and self.logger:
                        self.logger.debug("创建了新的aiohttp ClientSession")
        return self._session

    async def close(self):
        """关闭HTTP会话"""
        if hasattr(self, 'logger') and self.logger:
            self.logger.debug("开始关闭NotificationService的HTTP会话...")

        if self._session and not self._session.closed:
            try:
                # 1. 获取连接器引用
                connector = getattr(self._session, '_connector', None)

                                # 2. 强制关闭所有活跃连接
                if connector and not connector.closed:
                    try:
                        # 尝试访问连接器的内部连接池进行强制清理
                        if hasattr(connector, '_conns'):
                            for connections in connector._conns.values():
                                while connections:
                                    try:
                                        conn, _ = connections.popleft()
                                        if conn and hasattr(conn, 'close') and not getattr(conn, 'closed', True):
                                            conn.close()
                                    except Exception:
                                        break

                        # 尝试调用连接器的清理方法
                        cleanup_methods = ['_cleanup_closed', '_cleanup', 'cleanup']
                        for method_name in cleanup_methods:
                            if hasattr(connector, method_name):
                                try:
                                    method = getattr(connector, method_name)
                                    if callable(method):
                                        if asyncio.iscoroutinefunction(method):
                                            await method()
                                        else:
                                            method()
                                    break
                                except Exception:
                                    continue

                    except Exception:
                        pass  # 忽略强制清理时的异常

                # 3. 等待当前请求完成
                await asyncio.sleep(0.2)

                # 4. 关闭会话
                await self._session.close()

                # 5. 等待连接器完全关闭
                if connector and not connector.closed:
                    try:
                        await connector.close()
                        # 给SSL连接更多时间完成关闭
                        await asyncio.sleep(0.3)
                    except Exception as connector_e:
                        # 使用统一异常处理，但对SSL异常使用debug级别
                        if is_ignorable_ssl_exception(connector_e):
                            if hasattr(self, 'logger'):
                                self.logger.debug(f"连接器关闭时出现可忽略的异常: {connector_e}")
                        else:
                            handled_e = handle_exception(
                                connector_e,
                                context="关闭HTTP连接器",
                                logger=self.logger
                            )
                            if hasattr(self, 'logger'):
                                self.logger.debug(f"连接器关闭时出现异常: {handled_e}")

                # 6. 最后等待，确保所有SSL清理完成
                await asyncio.sleep(0.2)

                if hasattr(self, 'logger'):
                    self.logger.info("NotificationService 的 aiohttp 会话已成功关闭")

            except Exception as e:
                # 使用统一异常处理
                if is_ignorable_ssl_exception(e):
                    if hasattr(self, 'logger'):
                        self.logger.debug(f"关闭HTTP会话时出现可忽略的异常: {e}")
                else:
                    handled_exception = handle_exception(
                        e,
                        context="关闭NotificationService会话",
                        logger=self.logger
                    )
                    if hasattr(self, 'logger'):
                        self.logger.warning(f"关闭HTTP会话时出现异常: {handled_exception}")

        # 重置会话引用
        self._session = None

        # 从全局跟踪器中移除自己
        try:
            _service_registry.unregister(self)
            if hasattr(self, 'logger') and self.logger:
                self.logger.debug("NotificationService实例已从全局跟踪器移除")
        except Exception as e:
            # 对于注册器相关错误，只记录debug信息
            if hasattr(self, 'logger') and self.logger:
                self.logger.debug(f"从全局跟踪器移除实例时出错: {e}")

    def _get_wxpush_app_token(self):
        """从配置中获取WxPush的app_token，如果未配置则使用默认值"""
        return self.config_manager.get("wxpush_app_token", "AT_SEGaKHPlPgjziZNkZVJwISroZvFcxQDZ")

    async def send(self, device: Dict[str, Any], title: str, content: str, subtitle: Optional[str] = None) -> bool:
        """
        发送通知到指定设备（异步版本）

        :param device: 从配置中获取的设备对象
        :param title: 通知标题
        :param content: 通知内容
        :param subtitle: (可选) 通知副标题，主要用于Bark
        :return: 布尔值，表示是否发送成功
        """
        device_type = device.get('type')
        device_name = device.get('name', device.get('id', 'Unknown'))
        device_id = device.get('id')

        self.logger.info(f"正在向设备 {device_name} (类型: {device_type}) 发送通知")

        try:
            if device_type == "wxpush":
                return await self._send_wxpush_async(device, title, content)
            elif device_type == "bark":
                return await self._send_bark_async(device, title, content, subtitle)
            elif device_type == "pushme":
                return await self._send_pushme_async(device, title, content)
            else:
                raise NotificationException(
                    f"不支持的设备类型: {device_type}",
                    device_id=device_id,
                    notification_type=device_type,
                    code="UNSUPPORTED_DEVICE_TYPE"
                )
        except NotificationException:
            # 直接重新抛出应用异常
            raise
        except Exception as e:
            # 转换为NotificationException
            handled_exception = handle_exception(
                e,
                context=f"发送通知到设备{device_name}",
                logger=self.logger
            )
            raise NotificationException(
                f"向设备 {device_name} 发送通知失败",
                device_id=device_id,
                notification_type=device_type,
                details={'original_error': str(handled_exception)},
                cause=e
            )

    async def _send_wxpush_async(self, device: Dict[str, Any], title: str, content: str) -> bool:
        """发送单条WxPush通知（异步版本）"""
        uid = device.get("uid")
        if not uid:
            self.logger.error(f"设备 {device.get('name')} 缺少微信推送UID")
            return False

        # 使用批量发送接口，即使只有一个用户
        result = await self.send_wxpush_batch_async([uid], title, content)
        return result > 0

    async def send_wxpush_batch_async(self, uids: List[str], title: str, content: str) -> int:
        """分页发送WxPush消息，每批最多100个UID（异步版本）"""
        if not uids:
            return 0

        app_token = self._get_wxpush_app_token()
        batch_size = 100
        success_count = 0

        session = await self._get_session()

        for i in range(0, len(uids), batch_size):
            batch_uids = uids[i:i+batch_size]
            wxpush_data = {
                "appToken": app_token,
                "content": f"<b>{title}</b><br/>" + content.replace('\n', '<br/>'),
                "contentType": 2,  # 2代表HTML
                "uids": batch_uids,
            }

            # 重试机制，最多重试3次
            for attempt in range(3):
                try:
                    async with session.post(
                        "https://wxpusher.zjiecode.com/api/send/message",
                        json=wxpush_data,
                        timeout=aiohttp.ClientTimeout(total=15)
                    ) as response:
                        if response.status == 200:
                            resp_json = await response.json()
                            if resp_json.get("success"):
                                success_count += len(batch_uids)
                                self.logger.info(f"成功发送WxPush批量通知到 {len(batch_uids)} 个UID")
                                break
                            else:
                                error_msg = resp_json.get("msg", "")
                                if "QPS" in error_msg or "访问超过" in error_msg:
                                    # QPS限制，指数退避重试
                                    wait_time = (2 ** attempt) + (time.time() % 1)  # 添加随机抖动
                                    self.logger.warning(f"WxPush遇到QPS限制，将在 {wait_time:.1f} 秒后重试")
                                    await asyncio.sleep(wait_time)
                                    continue
                                else:
                                    self.logger.error(f"WxPush批量推送失败: {error_msg}")
                                    break
                        else:
                            self.logger.error(f"WxPush批量推送失败: HTTP {response.status}")
                            break

                except asyncio.TimeoutError:
                    self.logger.warning(f"WxPush批量推送超时，尝试重试 ({attempt + 1}/3)")
                    if attempt == 2:  # 最后一次重试
                        self.logger.error("WxPush批量推送最终超时失败")
                except aiohttp.ClientError as e:
                    self.logger.error(f"WxPush批量推送网络异常: {str(e)}")
                    break
                except Exception as e:
                    self.logger.error(f"WxPush批量推送异常: {str(e)}")
                    break

        return success_count

    async def _send_bark_async(self, device: Dict[str, Any], title: str, content: str, subtitle: Optional[str] = None) -> bool:
        """发送Bark通知（异步版本）"""
        bark_key = device.get("bark_key")
        if not bark_key:
            self.logger.error(f"设备 {device.get('name')} 缺少Bark Key")
            return False

        # URL编码标题和内容，防止特殊字符干扰
        from urllib.parse import quote
        safe_title = quote(title)
        safe_content = quote(content)

        base_url = f"https://api.day.app/{bark_key}/{safe_title}/{safe_content}"

        params = device.get("params", {}).copy()
        if subtitle:
            params['subtitle'] = subtitle
        params["_t"] = int(time.time())

        session = await self._get_session()

        # 重试机制，最多重试3次
        for attempt in range(3):
            try:
                async with session.get(
                    base_url,
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        self.logger.info(f"成功发送Bark通知到 {device.get('name')}")
                        return True
                    else:
                        self.logger.error(f"Bark通知发送失败: HTTP {response.status}")
                        break

            except asyncio.TimeoutError:
                self.logger.warning(f"Bark通知发送超时，尝试重试 ({attempt + 1}/3)")
                if attempt == 2:
                    self.logger.error(f"向 {device.get('name')} 发送Bark通知最终超时失败")
            except Exception as e:
                handled_exception = handle_exception(
                    e,
                    context=f"发送Bark通知到{device.get('name')}",
                    logger=self.logger
                )

                # 对于网络异常，记录并继续重试
                if isinstance(handled_exception, NetworkException):
                    self.logger.warning(f"向 {device.get('name')} 发送Bark通知网络异常: {handled_exception}")
                    # 继续重试循环
                else:
                    self.logger.error(f"向 {device.get('name')} 发送Bark通知异常: {handled_exception}")
                    break

            # 重试前等待
            await asyncio.sleep(1 * (attempt + 1))

        return False

    async def _send_pushme_async(self, device: Dict[str, Any], title: str, content: str) -> bool:
        """发送PushMe通知（异步版本）"""
        push_key = device.get("pushme_key")
        if not push_key:
            self.logger.error(f"设备 {device.get('name')} 缺少PushMe Key")
            return False

        current_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        pushme_data = {
            "push_key": push_key,
            "title": title,
            "content": content,
            "type": "text",
            "date": current_time_str,
        }

        session = await self._get_session()

        # 重试机制，最多重试3次
        for attempt in range(3):
            try:
                async with session.post(
                    "https://push.i-i.me",
                    data=pushme_data,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        self.logger.info(f"成功发送PushMe通知到 {device.get('name')}")
                        return True
                    else:
                        self.logger.error(f"PushMe通知发送失败: HTTP {response.status}")
                        break

            except asyncio.TimeoutError:
                self.logger.warning(f"PushMe通知发送超时，尝试重试 ({attempt + 1}/3)")
                if attempt == 2:
                    self.logger.error(f"向 {device.get('name')} 发送PushMe通知最终超时失败")
            except aiohttp.ClientError as e:
                self.logger.error(f"向 {device.get('name')} 发送PushMe通知网络异常: {str(e)}")
                break
            except Exception as e:
                self.logger.error(f"向 {device.get('name')} 发送PushMe通知异常: {str(e)}")
                break

            # 重试前等待
            await asyncio.sleep(1 * (attempt + 1))

        return False

    # 保持向后兼容的同步方法
    def send_wxpush_batch(self, uids: List[str], title: str, content: str) -> int:
        """
        同步版本的批量发送（为了向后兼容和在非Web环境中使用）。
        此方法现在将任务调度到后台事件循环。
        """
        coro = self.send_wxpush_batch_async(uids, title, content)
        future = schedule_async_task(coro)
        try:
            # 设置超时以避免永久阻塞
            return future.result(timeout=60)
        except Exception as e:
            self.logger.error(f"同步发送WxPush批量通知时出错: {e}")
            return 0

    def __del__(self):
        """析构函数，确保会话被正确关闭"""
        # 不在析构函数中关闭会话，因为这可能导致事件循环问题
        # 会话应该通过显式调用close()方法来关闭
        pass

def schedule_async_task(coro):
    """
    安全的、线程安全的调度器，用于从同步代码调用异步代码。
    它将协程提交到在后台线程中运行的共享事件循环。

    Args:
        coro: 要执行的协程对象。

    Returns:
        concurrent.futures.Future: 一个可以用于获取结果的future对象。
    """
    import concurrent.futures

    loop = app_context.get('background_event_loop')
    if not loop or not loop.is_running():
        # 可以在这里记录一个严重的错误，因为后台循环应该总是在运行
        # 为了健壮性，返回一个已完成但带有异常的future
        future = concurrent.futures.Future()
        future.set_exception(RuntimeError("后台事件循环不可用。"))
        return future

    return asyncio.run_coroutine_threadsafe(coro, loop)

def run_async_in_sync(coro):
    """
    通用的异步方法同步包装器

    解决在同步环境中调用异步方法的问题，使用统一异常处理

    Args:
        coro: 协程对象

    Returns:
        异步方法的返回值
    """
    import concurrent.futures
    import threading

    def run_in_new_thread():
        """在新线程中运行协程，总是创建新的事件循环"""
        new_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(new_loop)
        try:
            # 将协程包装为任务
            task = new_loop.create_task(coro)
            result = new_loop.run_until_complete(task)

            # 清理通知服务连接
            cleanup_coro = safe_async_cleanup(
                close_all_notification_services(),
                timeout=3.0
            )
            cleanup_task = new_loop.create_task(cleanup_coro)
            new_loop.run_until_complete(cleanup_task)

            return result
        except Exception as e:
            # 使用统一异常处理
            handled_exception = handle_exception(e, context="协程执行")
            raise handled_exception
        finally:
            # 使用安全的清理方式
            try:
                # 取消所有未完成的任务
                pending_tasks = [task for task in asyncio.all_tasks(new_loop) if not task.done()]
                if pending_tasks:
                    for task in pending_tasks:
                        task.cancel()
                    try:
                        new_loop.run_until_complete(
                            asyncio.wait(pending_tasks, timeout=2.0, return_when=asyncio.ALL_COMPLETED)
                        )
                    except Exception:
                        pass

                # 优雅关闭事件循环组件
                try:
                    new_loop.run_until_complete(new_loop.shutdown_asyncgens())
                except Exception:
                    pass

                try:
                    new_loop.run_until_complete(new_loop.shutdown_default_executor())
                except Exception:
                    pass

                # 关闭事件循环
                if not new_loop.is_closed():
                    new_loop.close()

            except Exception as cleanup_e:
                # 对于清理过程中的异常，只有非SSL异常才记录警告
                if not is_ignorable_ssl_exception(cleanup_e):
                    try:
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.debug(f"清理事件循环时出现异常: {cleanup_e}")
                    except:
                        pass

    try:
        # 首先尝试最简单的方式
        return asyncio.run(coro)
    except RuntimeError as e:
        # 对于运行时错误，使用新线程方式
        handled_exception = handle_exception(e, context="asyncio.run")

        try:
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(run_in_new_thread)
                return future.result(timeout=30)  # 30秒超时
        except concurrent.futures.TimeoutError:
            raise RuntimeError("异步操作超时") from e
        except Exception as thread_e:
            thread_handled = handle_exception(thread_e, context="线程执行异步操作")
            raise RuntimeError(f"线程执行异步操作失败: {thread_handled}") from e
    except Exception as e:
        # 处理其他类型的异常
        handled_exception = handle_exception(e, context="run_async_in_sync")
        raise handled_exception

# 添加全局函数，用于关闭所有NotificationService实例
async def close_all_notification_services():
    """
    关闭所有已注册的NotificationService实例的会话。
    这是一个优雅关闭过程的一部分。
    """
    # 此函数已废弃，其功能由Application.shutdown统一处理
    pass

# 修改cleanup_notification_service函数，使用全局函数
async def cleanup_notification_service():
    """清理通知服务中的连接"""
    # 使用全局清理函数
    await close_all_notification_services()