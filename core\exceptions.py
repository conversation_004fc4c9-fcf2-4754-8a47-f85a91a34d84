"""
统一异常处理框架
"""

import traceback
from typing import Optional, Dict, Any, Union
from datetime import datetime
import logging


class AppException(Exception):
    """应用基础异常"""

    def __init__(self,
                 message: str,
                 code: str = None,
                 details: Dict[str, Any] = None,
                 cause: Exception = None):
        self.message = message
        self.code = code or self.__class__.__name__
        self.details = details or {}
        self.cause = cause
        self.timestamp = datetime.now()
        super().__init__(self.message)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于API返回"""
        result = {
            'error': self.message,
            'code': self.code,
            'timestamp': self.timestamp.isoformat(),
            'details': self.details
        }

        if self.cause:
            result['cause'] = str(self.cause)

        return result

    def __str__(self):
        return f"[{self.code}] {self.message}"


class NetworkException(AppException):
    """网络相关异常"""

    def __init__(self, message: str, url: str = None, status_code: int = None, **kwargs):
        details = kwargs.get('details', {})
        if url:
            details['url'] = url
        if status_code:
            details['status_code'] = status_code
        kwargs['details'] = details
        super().__init__(message, **kwargs)


class DatabaseException(AppException):
    """数据库相关异常"""

    def __init__(self, message: str, query: str = None, **kwargs):
        details = kwargs.get('details', {})
        if query:
            details['query'] = query[:500]  # 限制查询长度
        kwargs['details'] = details
        super().__init__(message, **kwargs)


class ConfigurationException(AppException):
    """配置相关异常"""

    def __init__(self, message: str, config_key: str = None, **kwargs):
        details = kwargs.get('details', {})
        if config_key:
            details['config_key'] = config_key
        kwargs['details'] = details
        super().__init__(message, **kwargs)


class ProxyException(NetworkException):
    """代理相关异常"""

    def __init__(self, message: str, proxy_url: str = None, **kwargs):
        details = kwargs.get('details', {})
        if proxy_url:
            details['proxy_url'] = proxy_url
        kwargs['details'] = details
        super().__init__(message, **kwargs)


class MonitorException(AppException):
    """监控相关异常"""

    def __init__(self, message: str, house_name: str = None, **kwargs):
        details = kwargs.get('details', {})
        if house_name:
            details['house_name'] = house_name
        kwargs['details'] = details
        super().__init__(message, **kwargs)


class NotificationException(AppException):
    """通知相关异常"""

    def __init__(self, message: str, device_id: str = None, notification_type: str = None, **kwargs):
        details = kwargs.get('details', {})
        if device_id:
            details['device_id'] = device_id
        if notification_type:
            details['notification_type'] = notification_type
        kwargs['details'] = details
        super().__init__(message, **kwargs)


class ValidationException(AppException):
    """数据验证异常"""

    def __init__(self, message: str, field: str = None, value: Any = None, **kwargs):
        details = kwargs.get('details', {})
        if field:
            details['field'] = field
        if value is not None:
            details['value'] = str(value)[:100]  # 限制值长度
        kwargs['details'] = details
        super().__init__(message, **kwargs)


class SSLConnectionException(NetworkException):
    """SSL连接相关异常"""

    def __init__(self, message: str, **kwargs):
        super().__init__(message, code="SSL_CONNECTION_ERROR", **kwargs)


class EventLoopException(AppException):
    """事件循环相关异常"""

    def __init__(self, message: str, loop_status: str = None, **kwargs):
        details = kwargs.get('details', {})
        if loop_status:
            details['loop_status'] = loop_status
        kwargs['details'] = details
        super().__init__(message, code="EVENT_LOOP_ERROR", **kwargs)


class ExceptionHandler:
    """统一异常处理器"""

    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)

    def handle_exception(self,
                        exception: Exception,
                        context: str = None,
                        suppress_traceback: bool = False) -> AppException:
        """
        处理异常，将普通异常转换为AppException

        Args:
            exception: 原始异常
            context: 异常发生的上下文
            suppress_traceback: 是否抑制堆栈跟踪

        Returns:
            AppException: 标准化的异常对象
        """

        # 如果已经是AppException，直接返回
        if isinstance(exception, AppException):
            if not suppress_traceback:
                self._log_exception(exception, context)
            return exception

        # 根据异常类型转换为相应的AppException
        app_exception = self._convert_to_app_exception(exception, context)

        if not suppress_traceback:
            self._log_exception(app_exception, context, original_exception=exception)

        return app_exception

    def _convert_to_app_exception(self, exception: Exception, context: str = None) -> AppException:
        """将标准异常转换为AppException"""

        exception_type = type(exception).__name__
        message = str(exception)

        # SSL相关异常
        if self._is_ssl_related_exception(exception):
            return SSLConnectionException(
                message=f"SSL连接错误: {message}",
                details={'original_type': exception_type, 'context': context},
                cause=exception
            )

        # 事件循环相关异常
        if self._is_event_loop_exception(exception):
            return EventLoopException(
                message=f"事件循环错误: {message}",
                details={'original_type': exception_type, 'context': context},
                cause=exception
            )

        # 网络相关异常
        if self._is_network_exception(exception):
            return NetworkException(
                message=f"网络错误: {message}",
                details={'original_type': exception_type, 'context': context},
                cause=exception
            )

        # 数据库相关异常
        if self._is_database_exception(exception):
            return DatabaseException(
                message=f"数据库错误: {message}",
                details={'original_type': exception_type, 'context': context},
                cause=exception
            )

        # 默认AppException
        return AppException(
            message=f"未知错误: {message}",
            code=exception_type,
            details={'context': context},
            cause=exception
        )

    def _is_ssl_related_exception(self, exception: Exception) -> bool:
        """判断是否为SSL相关异常"""
        exception_str = str(exception).lower()
        exception_type = type(exception).__name__.lower()

        ssl_keywords = [
            'ssl', 'certificate', 'handshake', 'tls',
            'ssl transport', 'ssl connection', 'ssl context'
        ]

        return any(keyword in exception_str or keyword in exception_type
                  for keyword in ssl_keywords)

    def _is_event_loop_exception(self, exception: Exception) -> bool:
        """判断是否为事件循环相关异常"""
        exception_str = str(exception).lower()
        exception_type = type(exception).__name__.lower()

        loop_keywords = [
            'event loop', 'loop is closed', 'loop is running',
            'runtime error', 'future', 'task'
        ]

        return any(keyword in exception_str or keyword in exception_type
                  for keyword in loop_keywords)

    def _is_network_exception(self, exception: Exception) -> bool:
        """判断是否为网络相关异常"""
        import aiohttp
        import asyncio

        # 检查异常类型
        network_exception_types = (
            ConnectionError,
            TimeoutError,
            asyncio.TimeoutError,
        )

        # 检查aiohttp异常
        if hasattr(aiohttp, 'ClientError'):
            network_exception_types += (aiohttp.ClientError,)

        if isinstance(exception, network_exception_types):
            return True

        # 检查异常消息
        exception_str = str(exception).lower()
        network_keywords = [
            'connection', 'timeout', 'network', 'proxy',
            'host', 'socket', 'dns', 'http'
        ]

        return any(keyword in exception_str for keyword in network_keywords)

    def _is_database_exception(self, exception: Exception) -> bool:
        """判断是否为数据库相关异常"""
        import sqlite3

        # SQLite异常
        if isinstance(exception, sqlite3.Error):
            return True

        # 检查异常消息
        exception_str = str(exception).lower()
        db_keywords = [
            'database', 'sqlite', 'sql', 'table',
            'column', 'constraint', 'transaction'
        ]

        return any(keyword in exception_str for keyword in db_keywords)

    def _log_exception(self,
                      exception: Union[Exception, AppException],
                      context: str = None,
                      original_exception: Exception = None):
        """记录异常日志"""

        if isinstance(exception, AppException):
            # 对于应用异常，使用结构化日志
            log_data = {
                'exception_code': exception.code,
                'message': exception.message,
                'context': context,
                'details': exception.details
            }

            # 对于SSL和事件循环异常，使用debug级别
            if isinstance(exception, (SSLConnectionException, EventLoopException)):
                self.logger.debug(f"异常处理: {log_data}")
            else:
                self.logger.error(f"异常处理: {log_data}")
        else:
            # 对于普通异常，记录完整信息
            self.logger.error(f"未处理的异常: {exception}", exc_info=True)

        # 记录原始异常的堆栈跟踪（如果存在）
        if original_exception and not isinstance(exception, (SSLConnectionException, EventLoopException)):
            self.logger.debug(f"原始异常堆栈: {traceback.format_exc()}")


# 全局异常处理器实例
global_exception_handler = ExceptionHandler()


def handle_exception(exception: Exception,
                    context: str = None,
                    logger: Optional[logging.Logger] = None) -> AppException:
    """
    全局异常处理函数

    Args:
        exception: 要处理的异常
        context: 异常发生的上下文
        logger: 可选的日志记录器

    Returns:
        AppException: 标准化的异常对象
    """
    if logger:
        handler = ExceptionHandler(logger)
        return handler.handle_exception(exception, context)
    else:
        return global_exception_handler.handle_exception(exception, context)


def is_ignorable_ssl_exception(exception: Exception) -> bool:
    """
    判断是否为可忽略的SSL异常（通常在程序退出时发生）

    Args:
        exception: 要检查的异常

    Returns:
        bool: 是否为可忽略的SSL异常
    """
    exception_str = str(exception).lower()

    ignorable_keywords = [
        'ssl transport',
        'connection lost',
        'connection closed',
        'bad file descriptor',
        'transport is closing',
        'event loop is closed',
        'future was destroyed but it is pending'
    ]

    return any(keyword in exception_str for keyword in ignorable_keywords)


def safe_async_cleanup(coro, logger: Optional[logging.Logger] = None, timeout: float = 5.0):
    """
    安全的异步清理包装器，用于在程序退出时清理资源

    Args:
        coro: 要执行的清理协程
        logger: 日志记录器
        timeout: 超时时间
    """
    import asyncio

    async def _safe_cleanup():
        try:
            await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            if logger:
                logger.debug(f"清理操作超时({timeout}秒)")
        except Exception as e:
            if is_ignorable_ssl_exception(e):
                if logger:
                    logger.debug(f"清理时出现可忽略的异常: {e}")
            else:
                if logger:
                    logger.warning(f"清理时出现异常: {e}")

    return _safe_cleanup()


# 存储原始的stderr write函数
_original_stderr_write = None

def suppress_ssl_warnings():
    """
    抑制SSL和asyncio相关的警告和错误输出
    """
    import warnings
    import logging
    import sys
    global _original_stderr_write

    # 保存原始的stderr write函数
    if _original_stderr_write is None:
        _original_stderr_write = sys.stderr.write

    # 抑制SSL相关警告
    warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*SSL.*')
    warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*transport.*')
    warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*connection.*')
    warnings.filterwarnings('ignore', category=ResourceWarning, message='.*unclosed.*')

    # 抑制asyncio相关警告
    warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*event loop.*')
    warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*task.*')
    warnings.filterwarnings('ignore', category=RuntimeWarning, message='.*future.*')

    # 为特定的日志记录器设置更高的级别
    ssl_loggers = [
        'asyncio',
        'aiohttp.connector',
        'aiohttp.client',
        'aiohttp.client_proto',
        'ssl',
    ]

    for logger_name in ssl_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.CRITICAL)

    def filtered_stderr_write(text):
        """过滤SSL和连接相关的错误信息"""
        if text and isinstance(text, str):
            text_lower = text.lower()

            # 需要过滤的错误关键词
            filter_keywords = [
                'unclosed client session',
                'unclosed connector',
                'fatal error on ssl transport',
                'ssl transport',
                '_sslprotocoltransport.__del__',
                'event loop is closed',
                'attribute error',
                "'nonetype' object has no attribute 'send'",
                'connection lost',
                'transport is closing',
                'bad file descriptor',
                'exception ignored in',
                'traceback (most recent call last)',
                'future was destroyed but it is pending',
                'ssl.py',
                'sslproto.py',
                'proactor_events.py'
            ]

            # 如果包含这些关键词，就不输出
            if any(keyword in text_lower for keyword in filter_keywords):
                return

        # 其他错误正常输出
        _original_stderr_write(text)

    sys.stderr.write = filtered_stderr_write


def restore_stderr():
    """恢复原始的stderr输出"""
    import sys
    global _original_stderr_write
    if _original_stderr_write is not None:
        sys.stderr.write = _original_stderr_write


# 全局变量来跟踪是否已经抑制
_ssl_suppression_active = False


def activate_ssl_suppression():
    """激活SSL错误抑制"""
    global _ssl_suppression_active
    if not _ssl_suppression_active:
        suppress_ssl_warnings()
        _ssl_suppression_active = True


def deactivate_ssl_suppression():
    """停用SSL错误抑制"""
    global _ssl_suppression_active
    if _ssl_suppression_active:
        restore_stderr()
        _ssl_suppression_active = False