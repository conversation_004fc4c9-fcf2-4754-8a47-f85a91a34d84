"""通知器基类定义"""

import asyncio
from abc import ABC, abstractmethod
from functools import wraps
from typing import Dict, Any, Optional
import aiohttp

from core.data.models import Device


def notification_retry(max_retries: int = 3, delay_seconds: float = 1.0):
    """通知重试装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(self, *args, **kwargs):
            last_exception = None

            for attempt in range(max_retries):
                try:
                    return await func(self, *args, **kwargs)
                except (aiohttp.ClientError, asyncio.TimeoutError, ConnectionError) as e:
                    last_exception = e
                    if hasattr(self, 'http_client') and hasattr(self.http_client, 'logger'):
                        logger = self.http_client.logger
                    else:
                        logger = None

                    if attempt < max_retries - 1:
                        wait_time = delay_seconds * (2 ** attempt)  # 指数退避
                        if logger:
                            logger.warning(f"{self.platform_name} 发送失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}, {wait_time:.1f}秒后重试")
                        await asyncio.sleep(wait_time)
                    else:
                        if logger:
                            logger.error(f"{self.platform_name} 发送最终失败 (已重试 {max_retries} 次): {str(e)}")
                except Exception as e:
                    # 对于非网络错误，不进行重试
                    if hasattr(self, 'http_client') and hasattr(self.http_client, 'logger'):
                        logger = self.http_client.logger
                        logger.error(f"{self.platform_name} 发送时出现非网络错误: {str(e)}")
                    raise

            # 如果所有重试都失败了，抛出最后一个异常
            if last_exception:
                raise last_exception

            return False
        return wrapper
    return decorator


class BaseNotifier(ABC):
    """通知器基类"""

    def __init__(self, http_client):
        self.http_client = http_client

    @abstractmethod
    async def send(self, device: Device, title: str, content: str) -> bool:
        """发送通知

        Args:
            device: 设备对象
            title: 通知标题
            content: 通知内容

        Returns:
            bool: 是否发送成功
        """
        pass

    @property
    @abstractmethod
    def platform_name(self) -> str:
        """获取平台名称"""
        pass

    def _log_network_error(self, error: Exception, context: str = "") -> None:
        """记录网络错误"""
        if hasattr(self.http_client, 'logger'):
            logger = self.http_client.logger
            error_type = type(error).__name__
            error_msg = str(error)
            full_context = f"{self.platform_name} {context}".strip()
            logger.error(f"[{full_context}] 网络错误 ({error_type}): {error_msg}")

    def _is_network_error(self, error: Exception) -> bool:
        """判断是否为网络相关错误"""
        return isinstance(error, (
            aiohttp.ClientError,
            aiohttp.ServerDisconnectedError,
            aiohttp.ClientConnectorError,
            asyncio.TimeoutError,
            ConnectionError,
            OSError
        ))

    async def close(self) -> None:
        """关闭通知器使用的资源，默认实现不做任何操作"""
        # 基类中的默认实现不执行任何操作
        # 子类可以重写此方法以关闭特定资源
        pass