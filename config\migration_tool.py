"""
配置迁移工具
用于将数据库中存储的配置迁移到统一配置文件中
更新版本 - 不再依赖DatabaseConfigManager
"""

import os
import sys
import json
import shutil
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

# 添加项目根目录到Python路径以支持绝对导入
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

# 使用绝对导入
from config.unified_config import get_config


class ConfigMigrationTool:
    """配置迁移工具"""

    def __init__(self):
        self.unified_config = get_config()
        self.project_root = Path(__file__).parent.parent
        self.backup_dir = self.project_root / "config_backups"

        # 需要迁移的敏感配置项
        self.sensitive_config_keys = [
            'SECRET_KEY',
            'USERNAME',
            'PASSWORD_HASH'
        ]

    def create_backup(self) -> str:
        """创建当前配置的备份"""
        # 确保备份目录存在
        self.backup_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = self.backup_dir / f"config_backup_{timestamp}.json"

        try:
            # 备份当前的用户配置文件（如果存在）
            user_config_file = self.project_root / "user_config.json"
            if user_config_file.exists():
                backup_user_config = self.backup_dir / f"user_config_backup_{timestamp}.json"
                shutil.copy2(user_config_file, backup_user_config)

            # 备份数据库配置
            db_config = self._extract_database_config_direct()

            backup_data = {
                "timestamp": timestamp,
                "database_config": db_config,
                "unified_config": self.unified_config.get_all_config()
            }

            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)

            print(f"配置备份已创建: {backup_path}")
            return str(backup_path)

        except Exception as e:
            print(f"创建备份失败: {e}")
            raise

    def _extract_database_config_direct(self) -> Dict[str, Any]:
        """直接从数据库中提取配置数据"""
        try:
            from core.data.manager import get_data_manager
            data_manager = get_data_manager()

            # 获取所有数据库配置
            db_config = data_manager.get_all_config()

            print(f"从数据库提取了 {len(db_config)} 项配置")
            return db_config

        except Exception as e:
            print(f"从数据库提取配置失败: {e}")
            return {}

    def extract_database_config(self) -> Dict[str, Any]:
        """从数据库中提取需要迁移的配置"""
        try:
            db_config = self._extract_database_config_direct()

            # 只提取敏感配置
            extracted_config = {}
            for key in self.sensitive_config_keys:
                if key in db_config:
                    extracted_config[key] = db_config[key]

            print(f"从数据库提取了 {len(extracted_config)} 项需要迁移的配置")
            return extracted_config

        except Exception as e:
            print(f"从数据库提取配置失败: {e}")
            return {}

    def migrate_to_unified_config(self, extracted_config: Dict[str, Any]) -> bool:
        """将提取的配置迁移到统一配置系统"""
        try:
            if not extracted_config:
                print("没有需要迁移的配置")
                return True

            # 准备迁移的配置结构
            migration_config = {
                "web_auth": {
                    "username": extracted_config.get('USERNAME'),
                    "password_hash": extracted_config.get('PASSWORD_HASH'),
                    "secret_key": extracted_config.get('SECRET_KEY')
                }
            }

            # 移除空值
            migration_config["web_auth"] = {
                k: v for k, v in migration_config["web_auth"].items() if v is not None
            }

            if not migration_config["web_auth"]:
                print("没有有效的配置需要迁移")
                return True

            # 保存到用户配置文件
            self.unified_config.save_user_config(migration_config)

            print(f"成功迁移 {len(migration_config['web_auth'])} 项配置到统一配置系统")
            print("迁移的配置项:")
            for key in migration_config["web_auth"].keys():
                print(f"  - web_auth.{key}")

            return True

        except Exception as e:
            print(f"迁移配置失败: {e}")
            return False

    def verify_migration(self, original_config: Dict[str, Any]) -> bool:
        """验证迁移是否成功"""
        try:
            # 重新加载统一配置
            self.unified_config.reload()

            # 验证敏感配置是否正确迁移
            for key in self.sensitive_config_keys:
                if key in original_config:
                    if key == 'SECRET_KEY':
                        migrated_value = self.unified_config.get('web_auth.secret_key')
                    elif key == 'USERNAME':
                        migrated_value = self.unified_config.get('web_auth.username')
                    elif key == 'PASSWORD_HASH':
                        migrated_value = self.unified_config.get('web_auth.password_hash')
                    else:
                        continue

                    if migrated_value != original_config[key]:
                        print(f"验证失败: {key} 值不匹配")
                        return False

            print("配置迁移验证成功")
            return True

        except Exception as e:
            print(f"验证迁移失败: {e}")
            return False

    def run_migration(self, create_backup: bool = True) -> bool:
        """运行完整的配置迁移流程"""
        print("开始配置迁移...")

        try:
            # 1. 创建备份
            if create_backup:
                backup_path = self.create_backup()
                print(f"备份创建完成: {backup_path}")

            # 2. 从数据库提取配置
            extracted_config = self.extract_database_config()

            if not extracted_config:
                print("数据库中没有需要迁移的配置")
                return True

            # 3. 迁移到统一配置
            if not self.migrate_to_unified_config(extracted_config):
                print("配置迁移失败")
                return False

            # 4. 验证迁移
            if not self.verify_migration(extracted_config):
                print("配置迁移验证失败")
                return False

            print("配置迁移成功完成!")
            print("\n注意事项:")
            print("1. 旧的数据库配置仍然保留，可以在确认迁移无误后手动清理")
            print("2. 配置文件已备份，如有问题可以恢复")
            print("3. 重启应用后新配置将生效")

            return True

        except Exception as e:
            print(f"配置迁移过程中出现错误: {e}")
            return False

    def rollback_migration(self, backup_file: str) -> bool:
        """回滚迁移（从备份恢复）"""
        try:
            backup_path = Path(backup_file)
            if not backup_path.exists():
                print(f"备份文件不存在: {backup_file}")
                return False

            with open(backup_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)

            # 恢复用户配置文件
            user_config_file = self.project_root / "user_config.json"
            if user_config_file.exists():
                user_config_file.unlink()  # 删除当前的用户配置

            print("配置已回滚到迁移前状态")
            return True

        except Exception as e:
            print(f"回滚失败: {e}")
            return False


def main():
    """主函数，用于直接运行迁移工具"""
    migration_tool = ConfigMigrationTool()

    print("配置迁移工具")
    print("=" * 50)

    choice = input("请选择操作:\n1. 运行迁移\n2. 查看当前配置\n请输入选择 (1/2): ").strip()

    if choice == "1":
        confirm = input("确定要开始配置迁移吗？(y/N): ").strip().lower()
        if confirm == 'y':
            success = migration_tool.run_migration()
            if success:
                print("\n迁移完成！")
            else:
                print("\n迁移失败！")
        else:
            print("取消迁移")

    elif choice == "2":
        # 显示当前配置信息
        try:
            extracted = migration_tool.extract_database_config()
            print("\n当前数据库配置:")
            for key, value in extracted.items():
                if key == 'SECRET_KEY':
                    print(f"  {key}: {'*' * len(value) if value else 'None'}")
                else:
                    print(f"  {key}: {value}")
        except Exception as e:
            print(f"读取配置失败: {e}")

    else:
        print("无效选择")


if __name__ == "__main__":
    main()