#!/usr/bin/env python3
"""
青城住房监控系统 - 简化版一键启动程序（集成预上传缓存）
轻量级启动脚本，快速启动监控服务和Web控制台，并自动初始化预上传缓存
"""

import os
import sys
import time
import asyncio
import subprocess
import signal
import threading
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.unified_config import get_config


class SimpleStarter:
    """简化启动器（集成预上传缓存）"""

    def __init__(self):
        self.processes = []
        self.shutdown_requested = False
        self.cache_manager = None
        self.preupload_thread = None

        # 获取统一配置
        self.unified_config = get_config()
        self.monitor_config = self.unified_config.get_monitor_service_config()
        self.web_config = self.unified_config.get_web_service_config()

    def log(self, message):
        """简单日志输出"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]
        print(f"[{timestamp}] {message}")

    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.log("收到停止信号，正在关闭所有服务...")
            self.shutdown_requested = True
            self.stop_all()
            sys.exit(0)

        signal.signal(signal.SIGINT, signal_handler)
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, signal_handler)

    def start_service(self, script_name, service_name):
        """启动单个服务"""
        self.log(f"正在启动 {service_name}...")

        script_path = Path(__file__).parent / script_name
        if not script_path.exists():
            self.log(f"错误: 启动脚本 {script_name} 不存在")
            return None

        try:
            process = subprocess.Popen([sys.executable, str(script_path)])
            self.processes.append((process, service_name))
            self.log(f"{service_name} 启动成功，PID: {process.pid}")
            return process
        except Exception as e:
            self.log(f"启动 {service_name} 失败: {str(e)}")
            return None

    def stop_all(self):
        """停止所有服务"""
        self.log("正在停止所有服务...")

        for process, service_name in self.processes:
            try:
                if process.poll() is None:  # 进程还在运行
                    self.log(f"正在停止 {service_name}...")
                    process.terminate()
                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        self.log(f"强制结束 {service_name}")
                        process.kill()
            except Exception as e:
                self.log(f"停止 {service_name} 时出错: {str(e)}")

        self.log("所有服务已停止")

    async def initialize_preupload_cache(self):
        """初始化预上传缓存系统"""
        try:
            self.log("🚀 初始化预上传缓存系统...")

            # 导入缓存相关模块
            from cache.upload_cache import FileUploadCache
            from services.preupload_service import PreUploadService

            # 创建缓存管理器
            self.cache_manager = FileUploadCache("cache/uploads")
            self.log("✅ 缓存管理器初始化完成")

            # 创建预上传服务
            preupload_service = PreUploadService(self.cache_manager)
            self.log("✅ 预上传服务初始化完成")

            # 获取所有用户配置
            user_profiles = preupload_service.get_all_user_profiles()
            if not user_profiles:
                self.log(" 没有发现用户配置，跳过预上传")
                return False

            self.log(f"📋 发现 {len(user_profiles)} 个用户配置")

            # 清理过期缓存
            self.cache_manager.clear_expired_cache()
            self.log("🧹 过期缓存清理完成")

            # 创建登录客户端工厂函数（从数据库获取登录信息）
            async def login_client_factory(user_id: str):
                """从数据库创建登录客户端（支持代理配置）"""
                try:
                    from core.data.manager import get_data_manager
                    from ZDQF.login import HuhhothomeLogin

                    data_manager = get_data_manager()

                    # 根据用户名查找抢房设备信息
                    grab_device = data_manager.grab_device_model.get_by_username(user_id)
                    if not grab_device or not grab_device.get('enabled', True):
                        return None

                    # 检查登录信息
                    cookie = grab_device.get('cookie')
                    access_token = grab_device.get('access_token')

                    if not cookie or not access_token:
                        return None

                    # 获取代理配置（与正常抢房流程保持一致）
                    proxy_config = None
                    proxy_manager = None

                    try:
                        # 尝试获取代理管理器
                        from core.network.proxy_manager import ProxyManager

                        # 尝试从多个来源获取代理配置
                        proxy_api_url = None
                        try:
                            # 方法1: 从环境变量获取
                            import os
                            proxy_api_url = os.environ.get('PROXY_API_URL')

                            # 方法2: 从配置文件获取
                            if not proxy_api_url:
                                # 优先查找 user_config.json，然后是 config.json
                                config_files = ['user_config.json', 'config.json']
                                for config_file in config_files:
                                    if os.path.exists(config_file):
                                        import json
                                        with open(config_file, 'r', encoding='utf-8') as f:
                                            config_data = json.load(f)
                                            proxy_api_url = config_data.get('proxy_api_url')
                                            if proxy_api_url:
                                                break
                        except Exception:
                            pass

                        if proxy_api_url:
                            self.log(f"🌐 预上传: 为用户 {user_id} 初始化代理管理器...")
                            proxy_manager = ProxyManager(proxy_api_url)
                            # 同步获取代理
                            if proxy_manager.update_proxy_sync():
                                proxy_config = proxy_manager.get_proxy_dict()
                                self.log(f"🔗 预上传: 用户 {user_id} 将使用代理 {proxy_manager.current_proxy}")
                            else:
                                self.log(f" 预上传: 用户 {user_id} 代理获取失败，将使用直连")
                                proxy_manager = None
                        else:
                            # 尝试从配置文件获取静态代理配置
                            try:
                                # 优先查找 user_config.json，然后是 config.json
                                config_files = ['user_config.json', 'config.json']
                                config_found = False

                                for config_file in config_files:
                                    if os.path.exists(config_file):
                                        import json
                                        with open(config_file, 'r', encoding='utf-8') as f:
                                            config_data = json.load(f)
                                            proxy_config = config_data.get('proxy_config')
                                            if proxy_config:
                                                self.log(f"🔗 预上传: 用户 {user_id} 使用配置文件代理 ({config_file})")
                                                config_found = True
                                                break
                                            else:
                                                self.log(f" 预上传: 用户 {user_id} 未配置代理，使用直连模式 ({config_file})")
                                                config_found = True
                                                break

                                if not config_found:
                                    self.log(f" 预上传: 用户 {user_id} 配置文件不存在，使用直连模式")
                            except Exception:
                                self.log(f" 预上传: 用户 {user_id} 读取配置失败，使用直连模式")

                    except Exception as proxy_error:
                        self.log(f" 预上传: 用户 {user_id} 代理初始化失败: {proxy_error}")
                        proxy_config = None
                        proxy_manager = None

                    # 创建登录客户端（与正常抢房流程相同的逻辑）
                    if proxy_manager and proxy_manager.current_proxy:
                        login_client = HuhhothomeLogin(proxy_config=proxy_config, proxy_manager=proxy_manager)
                    elif proxy_config:
                        login_client = HuhhothomeLogin(proxy_config=proxy_config)
                    else:
                        login_client = HuhhothomeLogin()

                    # 设置Cookie
                    if cookie:
                        cookie_pairs = cookie.split(';')
                        for pair in cookie_pairs:
                            if '=' in pair:
                                name, value = pair.strip().split('=', 1)
                                login_client.session.cookies.set(name, value)

                    # 设置Access Token
                    if access_token:
                        login_client.access_token = access_token
                        login_client.headers["Membertoken"] = access_token

                    return login_client

                except Exception as e:
                    self.log(f"为用户 {user_id} 创建登录客户端失败: {e}")
                    return None

            # 执行批量预上传（限制并发数为2，避免启动时过载）
            self.log("🔄 开始批量预上传...")
            start_time = time.time()

            result = await preupload_service.batch_preupload_all_users(
                login_client_factory,
                max_concurrent=2
            )

            preupload_time = time.time() - start_time

            # 显示结果
            if 'error' in result:
                self.log(f" 预上传部分失败: {result['error']}")
            else:
                total_success = result.get('total_success', 0)
                total_cached = result.get('total_cached', 0)
                total_files = result.get('total_files', 0)

                self.log(f"📊 预上传完成: 成功{total_success}, 缓存{total_cached}, 总计{total_files}, 耗时{preupload_time:.1f}s")

                if total_success > 0 or total_cached > 0:
                    self.log("🎉 预上传缓存系统初始化成功！")
                    # 设置环境变量，通知监控服务使用缓存
                    os.environ['PREUPLOAD_CACHE_ENABLED'] = 'true'
                    return True
                else:
                    self.log(" 没有成功预上传任何文件，可能需要用户先登录")

            return False

        except ImportError:
            self.log(" 预上传缓存模块不可用，将使用传统模式")
            return False
        except Exception as e:
            self.log(f"❌ 预上传缓存初始化失败: {e}")
            return False

    def run_preupload_in_background(self):
        """在后台线程中运行预上传初始化"""
        def preupload_worker():
            try:
                # 在Windows上设置事件循环策略
                if sys.platform == 'win32':
                    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # 运行预上传初始化
                loop.run_until_complete(self.initialize_preupload_cache())
                loop.close()

            except Exception as e:
                self.log(f"后台预上传初始化异常: {e}")

        # 启动后台线程
        self.preupload_thread = threading.Thread(target=preupload_worker, daemon=True)
        self.preupload_thread.start()
        self.log("🔄 预上传缓存初始化已在后台启动...")

    def run(self):
        """运行启动器"""
        self.setup_signal_handlers()

        print("="*60)
        print("🏠 青城住房监控系统 - 简化版一键启动（集成预上传缓存）")
        print("="*60)

        # 启动监控服务
        monitor_process = self.start_service('start_monitor_service.py', '监控服务')
        if not monitor_process:
            self.log("监控服务启动失败，退出")
            return

        # 等待监控服务启动
        monitor_startup_delay = self.monitor_config.get('startup_delay', 3)
        self.log(f"等待监控服务启动（{monitor_startup_delay}秒）...")
        time.sleep(monitor_startup_delay)

        # 启动预上传缓存初始化（后台运行，不阻塞主流程）
        self.run_preupload_in_background()

        # 启动Web控制台
        web_process = self.start_service('web/app.py', 'Web控制台')
        if not web_process:
            self.log("Web控制台启动失败，但监控服务将继续运行")

        print("="*60)
        print("🎉 启动完成！")
        print(f"📊 监控服务: http://{self.monitor_config['host']}:{self.monitor_config['port']}")
        print(f"🌐 Web控制台: http://{self.web_config['host']}:{self.web_config['port']}")
        print("🚀 预上传缓存: 后台初始化中，将大幅提升抢房速度")
        print("💡 按 Ctrl+C 停止所有服务")
        print("="*60)

        # 等待用户中断
        try:
            while not self.shutdown_requested:
                # 检查进程状态
                for process, service_name in self.processes:
                    if process.poll() is not None:
                        self.log(f"警告: {service_name} 意外退出")

                # 使用配置的检查间隔
                check_interval = self.unified_config.get('service_management.check_interval', 5)
                time.sleep(check_interval)
        except KeyboardInterrupt:
            self.log("收到中断信号")
        finally:
            self.stop_all()


def main():
    """主函数"""
    starter = SimpleStarter()
    starter.run()


if __name__ == "__main__":
    main()