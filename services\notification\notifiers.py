"""各种通知器的实现"""

import asyncio
import time
from typing import List

from .base import BaseNotifier, notification_retry
from core.data.models import Device
from core.network.http_client import HttpClient
from core.utils.unified_logging import Logger


class BarkNotifier(BaseNotifier):
    """Bark通知器"""

    @property
    def platform_name(self) -> str:
        return "Bark"

    @notification_retry(max_retries=3, delay_seconds=1.0)
    async def send(self, device: Device, title: str, content: str) -> bool:
        bark_key = device.config.get("bark_key")
        if not bark_key:
            return False

        url = f"https://api.day.app/{bark_key}/{title}/{content}"
        params = {**device.config.get("params", {}), "_t": int(time.time())}

        # 禁用代理，使用直连方式
        async with await self.http_client.get_async(url, use_proxy=False, params=params) as response:
            return response.status == 200


class WxPushNotifier(BaseNotifier):
    """WxPush通知器"""

    def __init__(self, http_client: HttpClient, logger: Logger, default_app_token: str = ""):
        super().__init__(http_client)
        self.logger = logger
        self.default_app_token = default_app_token

    @property
    def platform_name(self) -> str:
        return "WxPush"

    async def send(self, device: Device, title: str, content: str, uids: List[str] = None) -> bool:
        app_token = device.config.get("app_token") or self.default_app_token
        if not app_token:
            return False

        if uids is None:
            uid = device.config.get("uid")
            if not uid:
                return False
            uids = [uid]

        url = "https://wxpusher.zjiecode.com/api/send/message"
        payload = {
            "appToken": app_token,
            "content": f"<b>{title}</b><br/>" + content.replace('\n', '<br/>'),
            "contentType": 2,  # HTML
            "uids": uids
        }

        # WxPush有QPS限制，需要重试机制
        for attempt in range(5):
            try:
                # 禁用代理，使用直连方式
                async with await self.http_client.post_async(url, use_proxy=False, json=payload) as response:
                    if response.status == 200:
                        resp_json = await response.json()
                        if resp_json.get("success"):
                            return True
                        else:
                            error_msg = resp_json.get("msg", "")
                            if "QPS" in error_msg or "访问超过" in error_msg:
                                self.logger.warning(f"WxPush遇到QPS限制，将在 {2 ** attempt} 秒后重试")
                                await asyncio.sleep(2 ** attempt)
                                continue
                            else:
                                self.logger.error(f"WxPush发送失败: {error_msg}")
                                return False
                    else:
                        self.logger.error(f"WxPush发送失败: HTTP {response.status}")
                        return False
            except Exception as e:
                self.logger.error(f"WxPush发送异常: {e}")
                return False

        return False


class PushMeNotifier(BaseNotifier):
    """PushMe通知器"""

    @property
    def platform_name(self) -> str:
        return "PushMe"

    @notification_retry(max_retries=3, delay_seconds=1.0)
    async def send(self, device: Device, title: str, content: str) -> bool:
        push_key = device.config.get("pushme_key")
        if not push_key:
            return False

        url = "https://push.i-i.me"
        payload = {
            "push_key": push_key,
            "title": title,
            "content": content
        }

        # 禁用代理，使用直连方式
        async with await self.http_client.post_async(url, use_proxy=False, data=payload) as response:
            return response.status == 200