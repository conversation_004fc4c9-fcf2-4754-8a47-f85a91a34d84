"""历史数据管理器 - 从原代码中提取和改进的历史数据管理功能"""

import json
import os
from typing import Dict, Set, Optional, List
from datetime import datetime

from .models import HistoryData, HouseDetail
from ..utils.unified_logging import Logger


class HistoryManager:
    """历史数据管理器"""

    def __init__(self, history_file: str, logger: Optional[Logger] = None):
        self.history_file = history_file
        self.logger = logger or Logger("HistoryManager")
        self.data = HistoryData()
        self._load_from_file()

    def _load_from_file(self) -> bool:
        """从文件加载历史数据"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    file_data = json.load(f)

                # 兼容旧格式的历史数据
                if 'house_counts' in file_data:
                    # 新格式
                    self.data = HistoryData.from_dict(file_data)
                else:
                    # 旧格式兼容处理
                    self.data = HistoryData(
                        house_counts=file_data.get('history', {}),
                        estate_ids=file_data.get('estate_ids', {}),
                        known_houses={k: set(v) if isinstance(v, list) else set()
                                    for k, v in file_data.get('known_houses', {}).items()}
                    )

                self.logger.info(f"从历史记录文件加载成功，包含 {len(self.data.house_counts)} 个房源历史")
                return True
            else:
                self.logger.info("历史记录文件不存在，使用空的历史数据")
                return False

        except Exception as e:
            self.logger.error(f"从历史记录文件加载失败: {str(e)}", context=e)
            self.data = HistoryData()  # 重置为空数据
            return False

    def save_to_file(self) -> bool:
        """保存历史数据到文件"""
        try:
            # 更新最后修改时间
            self.data.last_update = datetime.now()

            # 创建历史文件的目录（如果不存在）
            history_dir = os.path.dirname(self.history_file)
            if history_dir and not os.path.exists(history_dir):
                os.makedirs(history_dir)

            # 先写入临时文件，然后安全地替换，确保原子性写入
            temp_file = self.history_file + ".tmp"
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(self.data.to_dict(), f, ensure_ascii=False, indent=4)
                f.flush()  # 确保数据写入磁盘
                os.fsync(f.fileno())  # 强制操作系统将数据写入物理存储

            # 原子替换文件
            os.replace(temp_file, self.history_file)

            self.logger.debug(f"历史记录已保存到文件: {self.history_file}")
            return True

        except Exception as e:
            self.logger.error(f"保存历史记录到文件失败: {str(e)}", context=e)
            return False

    def get_house_count(self, name: str) -> Optional[int]:
        """获取指定房源的历史数量"""
        return self.data.house_counts.get(name)

    def set_house_count(self, name: str, count: int) -> None:
        """设置指定房源的数量"""
        self.data.house_counts[name] = count

    def get_estate_id(self, name: str) -> Optional[str]:
        """获取小区ID"""
        return self.data.estate_ids.get(name)

    def set_estate_id(self, name: str, estate_id: str) -> None:
        """设置小区ID"""
        self.data.estate_ids[name] = estate_id

    def get_known_houses(self, name: str) -> Set[str]:
        """获取已知房源ID集合"""
        return self.data.known_houses.get(name, set())

    def add_known_house(self, name: str, house_id: str) -> None:
        """添加已知房源ID"""
        if name not in self.data.known_houses:
            self.data.known_houses[name] = set()
        self.data.known_houses[name].add(house_id)

    def add_known_houses(self, name: str, house_ids: Set[str]) -> None:
        """批量添加已知房源ID"""
        if name not in self.data.known_houses:
            self.data.known_houses[name] = set()
        self.data.known_houses[name].update(house_ids)

    def set_known_houses(self, name: str, house_ids: Set[str]) -> None:
        """设置已知房源ID集合（覆盖原有数据）"""
        self.data.known_houses[name] = house_ids.copy()

    def get_house_detail(self, name: str, house_id: str) -> Optional[HouseDetail]:
        """获取指定房源的详细信息"""
        return self.data.house_details.get(name, {}).get(house_id)

    def set_house_detail(self, name: str, house_id: str, detail: HouseDetail) -> None:
        """设置指定房源的详细信息"""
        if name not in self.data.house_details:
            self.data.house_details[name] = {}
        self.data.house_details[name][house_id] = detail

    def get_all_house_details(self, name: str) -> Dict[str, HouseDetail]:
        """获取指定房源名称下的所有房源详细信息"""
        return self.data.house_details.get(name, {}).copy()

    def set_house_details(self, name: str, details: Dict[str, HouseDetail]) -> None:
        """设置指定房源名称下的所有房源详细信息（覆盖原有数据）"""
        self.data.house_details[name] = details.copy()

    def update_house_details_from_list(self, name: str, details: List[HouseDetail]) -> None:
        """从房源详细信息列表更新历史记录"""
        if name not in self.data.house_details:
            self.data.house_details[name] = {}

        # 将列表转换为字典并更新
        details_dict = {detail.id: detail for detail in details if detail.id}
        self.data.house_details[name].update(details_dict)

        self.logger.debug(f"为房源 [{name}] 更新了 {len(details_dict)} 个房源详细信息")

    def remove_house_detail(self, name: str, house_id: str) -> bool:
        """移除指定房源的详细信息"""
        if name in self.data.house_details and house_id in self.data.house_details[name]:
            del self.data.house_details[name][house_id]
            return True
        return False

    def reset_house_details(self, name: str, details: List[HouseDetail]) -> None:
        """完全重置指定房源的详细信息（先清空再设置）"""
        # 先清空该房源的所有详情
        if name in self.data.house_details:
            del self.data.house_details[name]

        # 重新设置详情
        if details:
            self.data.house_details[name] = {detail.id: detail for detail in details if detail.id}
            self.logger.debug(f"为房源 [{name}] 完全重置了 {len(self.data.house_details[name])} 个房源详细信息")
        else:
            self.logger.debug(f"为房源 [{name}] 清空了所有详细信息")

    def get_statistics(self) -> Dict[str, int]:
        """获取历史数据统计信息"""
        total_details = sum(len(details) for details in self.data.house_details.values())
        return {
            "房源数量记录": len(self.data.house_counts),
            "小区ID记录": len(self.data.estate_ids),
            "房源跟踪记录": len(self.data.known_houses),
            "总已知房源数": sum(len(house_ids) for house_ids in self.data.known_houses.values()),
            "房源详细信息记录": len(self.data.house_details),
            "总房源详细信息数": total_details
        }