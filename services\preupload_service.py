#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预上传服务
用于在系统启动时预上传所有用户的文件，优化抢房性能
"""

import os
import time
import asyncio
import random
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

from cache.upload_cache import FileUploadCache


class PreUploadService:
    """预上传服务"""

    def __init__(self, cache_manager: FileUploadCache):
        """
        初始化预上传服务

        Args:
            cache_manager: 缓存管理器
        """
        self.cache = cache_manager
        self.logger = logging.getLogger(__name__)
        self.profiles_dir = Path("ZDQF/profiles")

    def get_all_user_profiles(self) -> List[str]:
        """获取所有用户配置目录"""
        try:
            if not self.profiles_dir.exists():
                self.logger.warning(f"用户配置目录不存在: {self.profiles_dir}")
                return []

            user_dirs = []
            for item in self.profiles_dir.iterdir():
                if item.is_dir() and (item / "config.json").exists():
                    user_dirs.append(item.name)

            self.logger.info(f"发现 {len(user_dirs)} 个用户配置: {user_dirs}")
            return user_dirs

        except Exception as e:
            self.logger.error(f"获取用户配置失败: {e}")
            return []

    def get_user_files_to_upload(self, user_id: str) -> List[str]:
        """
        获取用户需要上传的文件列表

        Args:
            user_id: 用户ID

        Returns:
            需要上传的文件路径列表
        """
        try:
            import json
            from ZDQF.login import find_flexible_file

            user_dir = self.profiles_dir / user_id
            config_file = user_dir / "config.json"

            if not config_file.exists():
                self.logger.warning(f"用户配置文件不存在: {config_file}")
                return []

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            files_to_upload = []
            application_data = config.get("application_data", {})

            # 收集附件文件
            attachment_keys = ["work", "nohouse", "educationattachment", "approvefile",
                             "household", "rewardattmts", "achievementattmts", "personnel"]

            for key in attachment_keys:
                if key in application_data.get("itemmap", {}) and isinstance(application_data["itemmap"][key], list):
                    for filename in application_data["itemmap"][key]:
                        if filename:  # 跳过空文件名
                            # 使用灵活文件查找
                            actual_filename = find_flexible_file(str(user_dir), filename, self.logger)
                            actual_file_path = user_dir / actual_filename

                            if actual_file_path.exists():
                                files_to_upload.append(str(actual_file_path))
                            else:
                                self.logger.warning(f"文件不存在: {actual_file_path}")

            # 收集家庭成员身份证文件
            for member in application_data.get("familys", []):
                for card_key in ["card0", "card1"]:
                    if card_key in member and isinstance(member[card_key], list):
                        for filename in member[card_key]:
                            if filename:
                                actual_filename = find_flexible_file(str(user_dir), filename, self.logger)
                                actual_file_path = user_dir / actual_filename

                                if actual_file_path.exists():
                                    files_to_upload.append(str(actual_file_path))
                                else:
                                    self.logger.warning(f"身份证文件不存在: {actual_file_path}")

            # 去重
            files_to_upload = sorted(list(set(files_to_upload)))

            self.logger.info(f"用户 {user_id} 需要上传 {len(files_to_upload)} 个文件")
            return files_to_upload

        except Exception as e:
            self.logger.error(f"获取用户文件列表失败 {user_id}: {e}")
            return []

    def preupload_user_files(self, user_id: str, login_client, force_refresh: bool = False) -> Dict[str, Any]:
        """
        为单个用户预上传所有文件

        Args:
            user_id: 用户ID
            login_client: 登录客户端
            force_refresh: 是否强制刷新缓存（忽略现有缓存，重新上传所有文件）

        Returns:
            上传结果统计
        """
        try:
            start_time = datetime.now()
            self.logger.info(f"开始为用户 {user_id} 预上传文件...")

            files_to_upload = self.get_user_files_to_upload(user_id)
            if not files_to_upload:
                self.logger.info(f"用户 {user_id} 没有需要上传的文件")
                return {'success': 0, 'failed': 0, 'cached': 0, 'total': 0}

            success_count = 0
            failed_count = 0
            cached_count = 0

            for file_path in files_to_upload:
                try:
                    # 检查是否已有有效缓存（仅在非强制刷新模式下）
                    if not force_refresh:
                        cached_result = self.cache.get_cached_upload(user_id, file_path)
                        if cached_result:
                            cached_count += 1
                            self.logger.debug(f"文件已缓存，跳过上传: {os.path.basename(file_path)}")
                            continue
                    else:
                        # 强制刷新模式：清除现有缓存
                        self.logger.info(f"强制刷新模式，清除现有缓存: {os.path.basename(file_path)}")
                        self.cache.clear_file_cache(user_id, file_path)

                    # 添加随机延迟，避免被检测
                    delay = random.uniform(0.5, 2.0)
                    time.sleep(delay)

                    # 上传文件
                    self.logger.info(f"预上传文件: {os.path.basename(file_path)}")
                    upload_result = login_client.upload_file(file_path)

                    if upload_result:
                        # 缓存上传结果
                        self.cache.cache_upload_result(user_id, file_path, upload_result)
                        success_count += 1
                        self.logger.info(f"预上传成功: {os.path.basename(file_path)} -> {upload_result['name']}")
                    else:
                        failed_count += 1
                        self.logger.warning(f"预上传失败: {os.path.basename(file_path)}")

                except Exception as e:
                    failed_count += 1
                    self.logger.error(f"预上传文件异常 {os.path.basename(file_path)}: {e}")

            duration = (datetime.now() - start_time).total_seconds()
            result = {
                'success': success_count,
                'failed': failed_count,
                'cached': cached_count,
                'total': len(files_to_upload),
                'duration': duration
            }

            self.logger.info(f"用户 {user_id} 预上传完成: 成功{success_count}, 失败{failed_count}, "
                           f"缓存{cached_count}, 总计{len(files_to_upload)}, 耗时{duration:.2f}秒")

            return result

        except Exception as e:
            self.logger.error(f"用户预上传失败 {user_id}: {e}")
            return {'success': 0, 'failed': 0, 'cached': 0, 'total': 0, 'error': str(e)}

    async def batch_preupload_all_users(self, login_client_factory, max_concurrent: int = 3) -> Dict[str, Any]:
        """
        批量为所有用户预上传文件

        Args:
            login_client_factory: 登录客户端工厂函数
            max_concurrent: 最大并发数

        Returns:
            批量上传结果统计
        """
        try:
            start_time = datetime.now()
            self.logger.info("开始批量预上传所有用户文件...")

            user_profiles = self.get_all_user_profiles()
            if not user_profiles:
                self.logger.warning("没有发现用户配置，跳过预上传")
                return {'total_users': 0, 'results': {}}

            # 清理过期缓存
            self.cache.clear_expired_cache()

            results = {}
            semaphore = asyncio.Semaphore(max_concurrent)

            async def upload_user_files(user_id: str):
                async with semaphore:
                    try:
                        # 为每个用户创建独立的登录客户端
                        login_client = await login_client_factory(user_id)
                        if not login_client:
                            self.logger.error(f"无法为用户 {user_id} 创建登录客户端")
                            return user_id, {'error': '无法创建登录客户端'}

                        # 添加随机延迟，分散请求时间
                        delay = random.uniform(10, 60)  # 10-60秒随机延迟
                        self.logger.info(f"用户 {user_id} 将在 {delay:.1f} 秒后开始预上传")
                        await asyncio.sleep(delay)

                        result = self.preupload_user_files(user_id, login_client)
                        return user_id, result

                    except Exception as e:
                        self.logger.error(f"用户 {user_id} 预上传异常: {e}")
                        return user_id, {'error': str(e)}

            # 并发执行所有用户的预上传
            tasks = [upload_user_files(user_id) for user_id in user_profiles]
            completed_tasks = await asyncio.gather(*tasks, return_exceptions=True)

            # 收集结果
            for task_result in completed_tasks:
                if isinstance(task_result, Exception):
                    self.logger.error(f"预上传任务异常: {task_result}")
                    continue

                user_id, result = task_result
                results[user_id] = result

            # 统计总体结果
            total_duration = (datetime.now() - start_time).total_seconds()
            total_success = sum(r.get('success', 0) for r in results.values())
            total_failed = sum(r.get('failed', 0) for r in results.values())
            total_cached = sum(r.get('cached', 0) for r in results.values())
            total_files = sum(r.get('total', 0) for r in results.values())

            summary = {
                'total_users': len(user_profiles),
                'processed_users': len(results),
                'total_files': total_files,
                'total_success': total_success,
                'total_failed': total_failed,
                'total_cached': total_cached,
                'total_duration': total_duration,
                'results': results
            }

            self.logger.info(f"批量预上传完成: 用户{len(results)}/{len(user_profiles)}, "
                           f"文件成功{total_success}, 失败{total_failed}, 缓存{total_cached}, "
                           f"总计{total_files}, 耗时{total_duration:.2f}秒")

            return summary

        except Exception as e:
            self.logger.error(f"批量预上传失败: {e}")
            return {'error': str(e)}

    async def validate_server_files(self, user_id: str, login_client) -> Dict[str, bool]:
        """
        验证服务器文件是否仍然有效

        Args:
            user_id: 用户ID
            login_client: 登录客户端

        Returns:
            文件有效性字典 {filename: is_valid}
        """
        try:
            cached_files = self.cache.get_user_cached_files(user_id)
            if not cached_files:
                return {}

            validation_results = {}

            for filename, cached_result in cached_files.items():
                try:
                    # 这里可以添加服务器文件验证逻辑
                    # 例如：发送HEAD请求检查文件是否存在
                    # 目前简单检查缓存是否过期
                    is_valid = self.cache.is_cache_valid(cached_result)
                    validation_results[filename] = is_valid

                    if not is_valid:
                        self.logger.info(f"服务器文件失效: {user_id}/{filename}")

                except Exception as e:
                    self.logger.error(f"验证服务器文件失败 {user_id}/{filename}: {e}")
                    validation_results[filename] = False

            return validation_results

        except Exception as e:
            self.logger.error(f"验证服务器文件失败 {user_id}: {e}")
            return {}

    def get_preupload_stats(self) -> Dict[str, Any]:
        """获取预上传统计信息"""
        try:
            cache_stats = self.cache.get_cache_stats()

            # 添加用户文件统计
            user_profiles = self.get_all_user_profiles()
            users_with_cache = len([user for user in user_profiles
                                  if self.cache.get_user_cached_files(user)])

            stats = {
                'total_configured_users': len(user_profiles),
                'users_with_cache': users_with_cache,
                'cache_coverage': users_with_cache / len(user_profiles) if user_profiles else 0,
                **cache_stats
            }

            return stats

        except Exception as e:
            self.logger.error(f"获取预上传统计失败: {e}")
            return {}
