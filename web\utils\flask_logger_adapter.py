"""
Flask Logger适配器 - 提供与自定义Logger兼容的接口
"""

from typing import Optional, Dict, Any, Union
import traceback


class FlaskLoggerAdapter:
    """Flask Logger适配器，提供与自定义Logger相同的接口"""

    def __init__(self, flask_logger):
        """
        初始化适配器

        Args:
            flask_logger: Flask应用的logger实例
        """
        self.flask_logger = flask_logger
        self.name = getattr(flask_logger, 'name', 'FlaskLogger')

        # 兼容性属性
        self.push_success_count = 0
        self.push_fail_count = 0

    def _format_message(self, message: str, house_name: Optional[str] = None) -> str:
        """格式化消息，添加房源名称前缀"""
        if house_name:
            return f"[{house_name}] {message}"
        return message

    def _handle_context(self, context: Optional[Union[Dict, Exception]]) -> None:
        """处理上下文信息，记录额外的错误详情"""
        if not context:
            return

        if isinstance(context, Exception):
            # 记录异常详情
            self.flask_logger.error(f"错误详情: {str(context)}")
            self.flask_logger.error(f"堆栈跟踪: {traceback.format_exc()}")
        elif isinstance(context, dict):
            # 记录字典上下文
            context_str = ", ".join([f"{k}={v}" for k, v in context.items()])
            self.flask_logger.info(f"上下文信息: {context_str}")

    def debug(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """调试级别日志"""
        formatted_message = self._format_message(message, house_name)
        self.flask_logger.debug(formatted_message)
        if context:
            self._handle_context(context)

    def info(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """信息级别日志"""
        formatted_message = self._format_message(message, house_name)
        self.flask_logger.info(formatted_message)
        if context:
            self._handle_context(context)

    def warning(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """警告级别日志"""
        formatted_message = self._format_message(message, house_name)
        self.flask_logger.warning(formatted_message)
        if context:
            self._handle_context(context)

    def error(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None, exc_info: bool = False):
        """错误级别日志"""
        formatted_message = self._format_message(message, house_name)
        self.flask_logger.error(formatted_message, exc_info=exc_info)
        if context:
            self._handle_context(context)

    def critical(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """严重错误级别日志"""
        formatted_message = self._format_message(message, house_name)
        self.flask_logger.critical(formatted_message)
        if context:
            self._handle_context(context)

    # 兼容性方法 - 支持直接调用Flask logger的方法
    def __getattr__(self, name):
        """代理所有其他方法到Flask logger"""
        return getattr(self.flask_logger, name)