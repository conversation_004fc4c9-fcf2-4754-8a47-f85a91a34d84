from flask import Blueprint, request, jsonify, current_app
from web.utils.decorators import login_required
import json
import os

# 创建抢房设备蓝图
grab_devices_bp = Blueprint('grab_devices', __name__)

@grab_devices_bp.route('/ocr_status', methods=['GET'])
def check_ocr_status():
    """检查OCR功能状态的API端点"""
    try:
        from .login_helper import TESSERACT_AVAILABLE, PIL_AVAILABLE

        # 获取详细状态信息
        status_info = {
            'tesseract_available': TESSERACT_AVAILABLE,
            'pil_available': PIL_AVAILABLE,
            'ocr_fully_functional': TESSERACT_AVAILABLE and PIL_AVAILABLE
        }

        # 尝试导入相关模块获取更多信息
        try:
            import pytesseract
            status_info['pytesseract_version'] = str(pytesseract.get_tesseract_version())
        except Exception as e:
            status_info['pytesseract_error'] = str(e)

        try:
            from PIL import Image
            status_info['pil_version'] = Image.__version__ if hasattr(Image, '__version__') else 'available'
        except Exception as e:
            status_info['pil_error'] = str(e)

        # 检查Tesseract命令路径
        try:
            from .login_helper import find_tesseract_cmd
            tesseract_cmd = find_tesseract_cmd()
            status_info['tesseract_path'] = tesseract_cmd

            # 验证路径是否存在
            import os
            status_info['tesseract_path_exists'] = os.path.exists(tesseract_cmd)
        except Exception as e:
            status_info['tesseract_path_error'] = str(e)

        return jsonify({
            'status': 'success',
            'ocr_available': status_info['ocr_fully_functional'],
            'details': status_info
        })

    except Exception as e:
        current_app.logger.error(f"OCR状态检查失败: {e}")
        return jsonify({
            'status': 'error',
            'error': f'OCR状态检查失败: {str(e)}'
        }), 500

# 获取设备列表的功能已移至 device_routes.py，避免路由冲突