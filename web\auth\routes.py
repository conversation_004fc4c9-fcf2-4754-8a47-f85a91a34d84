"""
认证路由模块 - 处理用户登录和登出
"""

import hashlib
from flask import Blueprint, render_template, request, redirect, url_for, session, flash, current_app


auth_bp = Blueprint('auth', __name__)


@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        # 从统一配置获取用户名和密码哈希
        unified_config = current_app.unified_config
        config_username = unified_config.get('web_auth.username')
        config_password_hash = unified_config.get('web_auth.password_hash')

        # 计算提交的密码哈希
        password_hash = hashlib.sha256(password.encode()).hexdigest()

        if username == config_username and password_hash == config_password_hash:
            session['logged_in'] = True
            flash('登录成功', 'success')
            return redirect(url_for('views.index'))
        else:
            flash('用户名或密码错误', 'error')

    return render_template('login.html')


@auth_bp.route('/logout')
def logout():
    """用户登出"""
    session.pop('logged_in', None)
    flash('已退出登录', 'info')
    return redirect(url_for('auth.login'))


@auth_bp.route('/first-setup', methods=['GET', 'POST'])
def first_setup():
    """首次设置管理员账户"""
    unified_config = current_app.unified_config

    # 如果已经配置了认证信息，重定向到登录页面
    if unified_config.has_web_auth_configured():
        flash('管理员账户已设置，请登录', 'info')
        return redirect(url_for('auth.login'))

    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        confirm_password = request.form['confirm_password']

        # 验证输入
        if not username or not password:
            flash('用户名和密码不能为空', 'error')
            return render_template('first_setup.html')

        if password != confirm_password:
            flash('两次输入的密码不一致', 'error')
            return render_template('first_setup.html')

        if len(password) < 6:
            flash('密码长度至少为6位', 'error')
            return render_template('first_setup.html')

        try:
            # 计算密码哈希
            password_hash = hashlib.sha256(password.encode()).hexdigest()

            # 保存到统一配置
            web_auth_config = {
                'web_auth': {
                    'username': username,
                    'password_hash': password_hash
                }
            }
            unified_config.save_user_config(web_auth_config)

            current_app.logger.info(f"管理员账户设置成功: {username}")
            flash('管理员账户设置成功，请登录', 'success')
            return redirect(url_for('auth.login'))

        except Exception as e:
            current_app.logger.error(f"设置管理员账户失败: {e}")
            flash('设置失败，请重试', 'error')

    return render_template('first_setup.html')