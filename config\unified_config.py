"""
统一配置管理器
整合默认配置、用户配置文件和环境变量，提供统一的配置访问接口
支持环境分离配置和敏感配置管理
优化版本 - 增强错误处理、配置验证和性能
"""

import os
import json
import yaml
import logging
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path
from threading import RLock

from .default_config import DEFAULT_CONFIG
from .env_config import ENV_CONFIG


class ConfigValidationError(Exception):
    """配置验证错误"""
    pass


class UnifiedConfigManager:
    """
    统一配置管理器

    配置加载优先级（后者覆盖前者）：
    1. 默认配置 (default_config.py)
    2. 环境特定配置 (config_dev.yaml / config_prod.yaml) - 如果存在
    3. 用户配置文件 (user_config.json) - 如果存在
    4. 环境变量 (env_config.py)
    """

    # 类级别标志位，控制重复日志输出
    _config_loaded_logged = False
    _env_config_logged = False
    _user_config_logged = False

    def __init__(self, user_config_file: str = "user_config.json"):
        """
        初始化配置管理器

        Args:
            user_config_file: 用户自定义配置文件路径
        """
        self.project_root = Path(__file__).parent.parent
        self.user_config_file = self.project_root / user_config_file
        self.app_env = os.getenv('APP_ENV', 'dev').lower()
        self._config = {}
        self._config_lock = RLock()  # 线程安全锁
        self._change_listeners: List[Callable[[str, Any, Any], None]] = []
        self._logger = self._setup_logger()

        # 必需的配置项验证规则
        self._validation_rules = {
            'monitor_service.host': self._validate_host,
            'monitor_service.port': self._validate_port,
            'web_service.host': self._validate_host,
            'web_service.port': self._validate_port,
            'database.file': self._validate_file_path,
        }

        self._load_all_configs()

    def _setup_logger(self) -> logging.Logger:
        """设置内部日志记录器"""
        logger = logging.getLogger('UnifiedConfigManager')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _validate_host(self, value: Any) -> bool:
        """验证主机地址"""
        if not isinstance(value, str):
            return False
        return bool(value.strip())

    def _validate_port(self, value: Any) -> bool:
        """验证端口号"""
        if isinstance(value, str):
            try:
                value = int(value)
            except ValueError:
                return False
        if not isinstance(value, int):
            return False
        return 1 <= value <= 65535

    def _validate_file_path(self, value: Any) -> bool:
        """验证文件路径"""
        if not isinstance(value, str):
            return False
        # 对于数据库文件，只要路径格式合理即可，不要求文件必须存在
        return bool(value.strip())

    def _validate_config(self) -> List[str]:
        """验证配置完整性，返回错误列表"""
        errors = []

        for key, validator in self._validation_rules.items():
            value = self.get(key)
            if value is None:
                errors.append(f"缺少必需的配置项: {key}")
            elif not validator(value):
                errors.append(f"配置项 {key} 的值无效: {value}")

        return errors

    def _load_all_configs(self):
        """按优先级加载所有配置"""
        with self._config_lock:
            try:
                # 1. 加载默认配置
                self._config.update(DEFAULT_CONFIG)
                self._logger.debug("已加载默认配置")

                # 2. 加载环境特定配置（如果存在）
                self._load_environment_config()

                # 3. 加载用户配置文件（如果存在）
                if self.user_config_file.exists():
                    try:
                        with open(self.user_config_file, 'r', encoding='utf-8') as f:
                            user_config = json.load(f)
                            self._deep_update(self._config, user_config)
                        # 只在第一次加载时输出日志
                        if not UnifiedConfigManager._user_config_logged:
                            self._logger.info(f"已加载用户配置文件: {self.user_config_file}")
                            UnifiedConfigManager._user_config_logged = True
                        else:
                            self._logger.debug(f"已加载用户配置文件: {self.user_config_file}")
                    except json.JSONDecodeError as e:
                        self._logger.error(f"用户配置文件格式错误: {e}")
                        raise ConfigValidationError(f"用户配置文件JSON格式错误: {e}")
                    except Exception as e:
                        self._logger.warning(f"加载用户配置文件失败: {e}")

                # 4. 应用环境变量配置
                self._deep_update(self._config, ENV_CONFIG)
                self._logger.debug("已应用环境变量配置")

                # 5. 应用敏感配置的环境变量覆盖
                self._apply_sensitive_env_overrides()

                # 6. 验证配置完整性
                validation_errors = self._validate_config()
                if validation_errors:
                    for error in validation_errors:
                        self._logger.warning(f"配置验证警告: {error}")

                # 只在第一次加载时输出完成日志
                if not UnifiedConfigManager._config_loaded_logged:
                    self._logger.info("配置加载完成")
                    UnifiedConfigManager._config_loaded_logged = True
                else:
                    self._logger.debug("配置加载完成")

            except Exception as e:
                self._logger.error(f"加载配置时出现严重错误: {e}")
                raise

    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典，支持嵌套字典的合并"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value

    def _load_environment_config(self):
        """加载环境特定配置文件"""
        env_config_file = self.project_root / f"config_{self.app_env}.yaml"

        if env_config_file.exists():
            try:
                with open(env_config_file, 'r', encoding='utf-8') as f:
                    env_config = yaml.safe_load(f) or {}
                    self._deep_update(self._config, env_config)
                # 只在第一次加载时输出日志
                if not UnifiedConfigManager._env_config_logged:
                    self._logger.info(f"已加载 {self.app_env} 环境配置: {env_config_file}")
                    UnifiedConfigManager._env_config_logged = True
                else:
                    self._logger.debug(f"已加载 {self.app_env} 环境配置: {env_config_file}")
            except yaml.YAMLError as e:
                self._logger.error(f"环境配置文件YAML格式错误: {e}")
                raise ConfigValidationError(f"环境配置文件YAML格式错误: {e}")
            except Exception as e:
                self._logger.warning(f"加载环境配置文件失败: {e}")

    def _apply_sensitive_env_overrides(self):
        """应用敏感配置的环境变量覆盖"""
        # 支持的敏感配置环境变量映射
        sensitive_env_mappings = {
            'WEB_AUTH_SECRET_KEY': 'web_auth.secret_key',
            'WEB_AUTH_USERNAME': 'web_auth.username',
            'WEB_AUTH_PASSWORD_HASH': 'web_auth.password_hash',
            'DATABASE_FILE': 'database.file'
        }

        for env_key, config_path in sensitive_env_mappings.items():
            env_value = os.getenv(env_key)
            if env_value:
                self.set(config_path, env_value)
                self._logger.info(f"已通过环境变量 {env_key} 覆盖配置 {config_path}")

    def add_change_listener(self, listener: Callable[[str, Any, Any], None]):
        """
        添加配置变更监听器

        Args:
            listener: 监听器函数，参数为 (key, old_value, new_value)
        """
        self._change_listeners.append(listener)

    def remove_change_listener(self, listener: Callable[[str, Any, Any], None]):
        """移除配置变更监听器"""
        if listener in self._change_listeners:
            self._change_listeners.remove(listener)

    def _notify_change(self, key: str, old_value: Any, new_value: Any):
        """通知配置变更"""
        for listener in self._change_listeners:
            try:
                listener(key, old_value, new_value)
            except Exception as e:
                self._logger.error(f"配置变更通知失败: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项

        Args:
            key: 配置项键名，支持点分隔的嵌套键 (如 'database.file')
            default: 默认值

        Returns:
            配置项值
        """
        with self._config_lock:
            keys = key.split('.')
            value = self._config

            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default

            return value

    def set(self, key: str, value: Any, notify: bool = True):
        """
        设置配置项（仅在内存中，不持久化）

        Args:
            key: 配置项键名，支持点分隔的嵌套键
            value: 配置项值
            notify: 是否触发变更通知
        """
        with self._config_lock:
            old_value = self.get(key)

            keys = key.split('.')
            config_ref = self._config

            # 导航到嵌套字典的正确位置
            for k in keys[:-1]:
                if k not in config_ref:
                    config_ref[k] = {}
                config_ref = config_ref[k]

            # 设置值
            config_ref[keys[-1]] = value

            if notify and old_value != value:
                self._notify_change(key, old_value, value)

    def save_user_config(self, config_updates: Dict[str, Any]):
        """
        保存用户配置到文件

        Args:
            config_updates: 要保存的配置更新
        """
        with self._config_lock:
            try:
                # 加载现有的用户配置
                user_config = {}
                if self.user_config_file.exists():
                    try:
                        with open(self.user_config_file, 'r', encoding='utf-8') as f:
                            user_config = json.load(f)
                    except Exception:
                        self._logger.warning("现有用户配置文件损坏，将创建新的配置文件")

                # 更新配置
                self._deep_update(user_config, config_updates)

                # 保存到文件
                os.makedirs(self.user_config_file.parent, exist_ok=True)
                with open(self.user_config_file, 'w', encoding='utf-8') as f:
                    json.dump(user_config, f, indent=2, ensure_ascii=False)

                self._logger.info("用户配置已保存")

                # 重新加载配置
                self._load_all_configs()

            except Exception as e:
                self._logger.error(f"保存用户配置失败: {e}")
                raise Exception(f"保存用户配置失败: {e}")

    def reload(self):
        """重新加载所有配置"""
        self._logger.info("正在重新加载配置...")
        # 临时重置标志位，确保重新加载时能看到相关日志
        old_config_logged = UnifiedConfigManager._config_loaded_logged
        old_env_logged = UnifiedConfigManager._env_config_logged
        old_user_logged = UnifiedConfigManager._user_config_logged

        UnifiedConfigManager._config_loaded_logged = False
        UnifiedConfigManager._env_config_logged = False
        UnifiedConfigManager._user_config_logged = False

        try:
            self._load_all_configs()
        finally:
            # 恢复标志位状态，避免影响其他实例
            UnifiedConfigManager._config_loaded_logged = old_config_logged
            UnifiedConfigManager._env_config_logged = old_env_logged
            UnifiedConfigManager._user_config_logged = old_user_logged

    def get_all_config(self) -> Dict[str, Any]:
        """获取完整的配置字典"""
        with self._config_lock:
            return self._config.copy()

    def get_monitor_service_config(self) -> Dict[str, Any]:
        """获取监控服务相关配置"""
        config = self.get('monitor_service', {}).copy()

        # 向后兼容：如果没有health_url但有health_path，则动态构建health_url
        if 'health_url' not in config and 'health_path' in config:
            health_url = self.get_service_health_url('monitor')
            if health_url:
                config['health_url'] = health_url

        return config

    def get_web_service_config(self) -> Dict[str, Any]:
        """获取Web服务相关配置"""
        config = self.get('web_service', {}).copy()

        # 向后兼容：如果没有health_url但有health_path，则动态构建health_url
        if 'health_url' not in config and 'health_path' in config:
            health_url = self.get_service_health_url('web')
            if health_url:
                config['health_url'] = health_url

        return config

    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志相关配置"""
        return self.get('logging', {})

    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库相关配置"""
        return self.get('database', {})

    def get_web_auth_config(self) -> Dict[str, Any]:
        """获取Web认证相关配置"""
        return self.get('web_auth', {})

    def get_web_auth_username(self) -> Optional[str]:
        """获取Web认证用户名"""
        return self.get('web_auth.username')

    def get_web_auth_password_hash(self) -> Optional[str]:
        """获取Web认证密码哈希"""
        return self.get('web_auth.password_hash')

    def get_web_auth_secret_key(self) -> Optional[str]:
        """获取Web认证密钥"""
        return self.get('web_auth.secret_key')

    def has_web_auth_configured(self) -> bool:
        """检查是否已配置Web认证"""
        return bool(self.get_web_auth_username() and self.get_web_auth_password_hash())

    def get_app_environment(self) -> str:
        """获取当前应用环境"""
        return self.app_env

    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要信息（不包含敏感信息）"""
        summary = {
            'environment': self.app_env,
            'user_config_exists': self.user_config_file.exists(),
            'web_auth_configured': self.has_web_auth_configured(),
            'monitor_service': {
                'host': self.get('monitor_service.host'),
                'port': self.get('monitor_service.port')
            },
            'web_service': {
                'host': self.get('web_service.host'),
                'port': self.get('web_service.port')
            },
            'database_file': self.get('database.file'),
            'validation_errors': self._validate_config()
        }
        return summary

    def get_service_health_url(self, service_name: str) -> Optional[str]:
        """
        动态构建服务健康检查URL

        Args:
            service_name: 服务名称 ('monitor' 或 'web')

        Returns:
            构建的健康检查URL，如果配置不完整则返回None
        """
        if service_name not in ['monitor', 'web']:
            self._logger.error(f"未知的服务名称: {service_name}")
            return None

        service_config = self.get(f'{service_name}_service', {})

        if not service_config:
            return None

        host = service_config.get('host')
        port = service_config.get('port')
        health_path = service_config.get('health_path')

        if not all([host, port, health_path]):
            self._logger.warning(f"服务 {service_name} 的健康检查配置不完整")
            return None

        # 对于Web服务，如果host是0.0.0.0，健康检查时使用127.0.0.1
        if service_name == 'web' and host == '0.0.0.0':
            host = '127.0.0.1'

        return f"http://{host}:{port}{health_path}"


# 全局配置实例
unified_config = UnifiedConfigManager()


def get_config() -> UnifiedConfigManager:
    """获取全局配置实例"""
    return unified_config


def get_config_summary() -> Dict[str, Any]:
    """获取配置摘要信息的便捷函数"""
    return unified_config.get_config_summary()