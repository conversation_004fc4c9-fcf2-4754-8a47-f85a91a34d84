"""
抢房设备API模块 - 重构版本
从子模块中导入所有功能，确保向后兼容性
"""

# 导入主要的蓝图和函数
from .grab_devices import (
    grab_devices_api,
    get_auto_login_manager,
    initialize_auto_login_manager
)

# 导入各个模块的类和函数
from .grab_devices.auto_login_manager import AutoLoginManager
from .grab_devices.login_helper import HuhhothomeLoginHelper
from .grab_devices.cookie_tester import CookieTester, _check_profile_exists, _get_profile_data, _save_profile_data

# 全部导入，确保向后兼容
__all__ = [
    'grab_devices_api',
    'AutoLoginManager',
    'HuhhothomeLoginHelper',
    'CookieTester',
    'get_auto_login_manager',
    'initialize_auto_login_manager',
    '_check_profile_exists',
    '_get_profile_data',
    '_save_profile_data'
]