"""
数据管理器
提供统一的数据访问接口，支持从数据库或JSON文件读取数据
"""

import os
from typing import Dict, List, Any, Optional
from core.data.database import (
    DatabaseManager,
    ConfigModel,
    DeviceModel,
    MonitorConfigModel,
    EstateModel,
    HouseDetailModel,
    GrabDeviceModel
)
from datetime import datetime, date


class DataManager:
    """
    统一数据管理器
    使用SQLite数据库进行数据持久化
    """

    def __init__(self, db_path: str = "monitoring.db"):
        """初始化数据管理器，使用数据库模式"""
        self.db_path = db_path

        # 导入数据库相关模块
        from core.data.database import DatabaseManager, ConfigModel, DeviceModel, MonitorConfigModel, EstateModel, HouseDetailModel, GrabDeviceModel

        print(f"使用数据库模式: {db_path}")
        self.db_manager = DatabaseManager(db_path)
        self.config_model = ConfigModel(self.db_manager)
        self.device_model = DeviceModel(self.db_manager)
        self.monitor_model = MonitorConfigModel(self.db_manager)
        self.estate_model = EstateModel(self.db_manager)
        self.house_detail_model = HouseDetailModel(self.db_manager)
        self.grab_device_model = GrabDeviceModel(self.db_manager)

    # ========== 配置管理 ==========

    def get_config(self, key: str, default=None):
        """获取配置值"""
        return self.config_model.get(key, default)

    def set_config(self, key: str, value: Any):
        """设置配置值"""
        self.config_model.set(key, value)

    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.config_model.get_all()

    # ========== 设备管理 ==========

    def get_devices(self) -> List[Dict[str, Any]]:
        """获取所有设备"""
        return self.device_model.get_all()

    def get_device(self, device_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取设备"""
        return self.device_model.get_by_id(device_id)

    def save_device(self, device_data: Dict[str, Any]):
        """保存设备"""
        self.device_model.create_or_update(device_data)

    def delete_device(self, device_id: str):
        """删除设备"""
        self.device_model.delete(device_id)

    # ========== 监控配置管理 ==========

    def get_monitor_configs(self) -> List[Dict[str, Any]]:
        """获取所有监控配置"""
        return self.monitor_model.get_all()

    def save_monitor_config(self, config_data: Dict[str, Any]):
        """保存监控配置"""
        self.monitor_model.create_or_update(config_data)

    # ========== 房产历史管理 ==========

    def get_history(self) -> Dict[str, int]:
        """获取房产历史计数"""
        return self.estate_model.get_all_history()

    def update_history(self, name: str, count: int):
        """更新房产历史计数"""
        self.estate_model.update_history(name, count)

    def get_estates_history(self) -> Dict[str, int]:
        """获取房源历史记录（别名方法）"""
        return self.get_history()

    def update_estate_history(self, name: str, count: int):
        """更新房源历史记录（别名方法）"""
        self.update_history(name, count)

    def get_estate_ids(self) -> Dict[str, str]:
        """获取房产ID映射"""
        return self.estate_model.get_all_estate_ids()

    def set_estate_id(self, name: str, estate_id: str):
        """设置房产ID"""
        self.estate_model.set_estate_id(name, estate_id)

    def get_known_houses(self) -> Dict[str, List[str]]:
        """获取已知房屋映射"""
        return self.estate_model.get_known_houses()

    def add_known_house(self, estate_name: str, house_id: str):
        """添加一个已知的房屋ID"""
        self.estate_model.add_known_house(estate_name, house_id)

    def set_known_houses(self, estate_name: str, house_ids: List[str]):
        """设置一个房产的已知房屋ID列表"""
        self.estate_model.set_known_houses(estate_name, house_ids)

    def get_last_update(self) -> Optional[str]:
        """获取最后更新时间"""
        return self.estate_model.get_last_update()

    def update_last_update(self, timestamp: str = None):
        """更新最后更新时间"""
        self.estate_model.update_last_update()

    # ========== 房源详情管理 ==========

    def save_house_detail(self, estate_name: str, house_detail: Dict[str, Any]):
        """保存单个房源详情"""
        self.house_detail_model.save_house_detail(estate_name, house_detail)

    def save_house_details(self, estate_name: str, house_details: List[Dict[str, Any]]):
        """批量保存房源详情"""
        self.house_detail_model.save_house_details(estate_name, house_details)

    def get_house_details(self, estate_name: str) -> List[Dict[str, Any]]:
        """获取指定房源的所有详情"""
        return self.house_detail_model.get_house_details(estate_name)

    def get_house_detail(self, estate_name: str, house_id: str) -> Optional[Dict[str, Any]]:
        """获取指定房源的详情"""
        return self.house_detail_model.get_house_detail(estate_name, house_id)

    def clear_house_details(self, estate_name: str):
        """清除指定房源的所有详情"""
        self.house_detail_model.delete_house_details(estate_name)

    # ========== 高级CRUD操作 ==========

    def delete_config(self, key: str) -> bool:
        """删除配置项"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM config WHERE key = ?", (key,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception:
            return False

    def list_configs(self) -> List[str]:
        """列出所有配置键"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT key FROM config ORDER BY key")
                return [row[0] for row in cursor.fetchall()]
        except Exception:
            return []

    def search_devices(self, **filters) -> List[Dict[str, Any]]:
        """按条件搜索设备"""
        devices = self.get_devices()

        filtered_devices = []
        for device in devices:
            match = True
            for key, value in filters.items():
                if key not in device or device[key] != value:
                    match = False
                    break
            if match:
                filtered_devices.append(device)

        return filtered_devices

    def batch_update_devices(self, device_updates: List[Dict[str, Any]]) -> int:
        """批量更新设备"""
        count = 0
        for device_data in device_updates:
            try:
                self.save_device(device_data)
                count += 1
            except Exception as e:
                print(f"更新设备 {device_data.get('id')} 失败: {e}")
        return count

    def delete_monitor_config(self, config_name: str) -> bool:
        """删除监控配置"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                # 先获取配置ID
                cursor.execute("SELECT id FROM monitor_configs WHERE name = ?", (config_name,))
                result = cursor.fetchone()
                if result:
                    config_id = result[0]
                    # 删除关联的设备
                    cursor.execute("DELETE FROM monitor_config_devices WHERE monitor_config_id = ?", (config_id,))
                    # 删除配置
                    cursor.execute("DELETE FROM monitor_configs WHERE id = ?", (config_id,))
                    conn.commit()
                    return True
            return False
        except Exception:
            return False

    def delete_house_completely(self, house_name: str) -> bool:
        """完全删除房源（包括所有相关数据），确保所有操作在单个事务中完成。"""
        try:
            with self.db_manager.transaction() as conn:
                cursor = conn.cursor()

                # 1. 查找 monitor_config 的 ID
                cursor.execute("SELECT id FROM monitor_configs WHERE name = ?", (house_name,))
                result = cursor.fetchone()

                if result:
                    config_id = result[0]
                    # 2. 删除设备关联关系
                    cursor.execute("DELETE FROM monitor_config_devices WHERE monitor_config_id = ?", (config_id,))
                    # 3. 删除监控配置本身
                    cursor.execute("DELETE FROM monitor_configs WHERE id = ?", (config_id,))
                else:
                    # 如果 monitor_configs 中没有，也尝试按名称删除，以处理不一致的数据
                    cursor.execute("DELETE FROM monitor_configs WHERE name = ?", (house_name,))

                # 4. 删除 known_houses 表中的相关数据
                cursor.execute("DELETE FROM known_houses WHERE estate_name = ?", (house_name,))

                # 5. 删除 house_details 表中的相关数据
                cursor.execute("DELETE FROM house_details WHERE estate_name = ?", (house_name,))

                # 6. 最后删除 estates 表中的房源数据
                cursor.execute("DELETE FROM estates WHERE name = ?", (house_name,))

            # 事务成功提交后，再次验证是否删除干净
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT id FROM monitor_configs WHERE name = ?", (house_name,))
                if cursor.fetchone():
                    return False  # 如果还能找到，说明删除失败

                cursor.execute("SELECT name FROM estates WHERE name = ?", (house_name,))
                if cursor.fetchone():
                    return False # 如果还能找到，说明删除失败

            return True

        except Exception as e:
            print(f"ERROR: 完全删除房源失败 {house_name}: {e}")
            import traceback
            traceback.print_exc()
            return False

    def clear_history(self) -> bool:
        """清空历史记录"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM estates")
                cursor.execute("DELETE FROM known_houses")
                cursor.execute("DELETE FROM update_history")
                conn.commit()
            return True
        except Exception:
            return False

    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()

                stats = {}

                # 配置数量
                cursor.execute("SELECT COUNT(*) FROM config")
                stats['config_count'] = cursor.fetchone()[0]

                # 设备数量
                cursor.execute("SELECT COUNT(*) FROM devices")
                stats['device_count'] = cursor.fetchone()[0]

                # 监控配置数量
                cursor.execute("SELECT COUNT(*) FROM monitor_configs")
                stats['monitor_config_count'] = cursor.fetchone()[0]

                # 房产数量
                cursor.execute("SELECT COUNT(*) FROM estates")
                stats['estate_count'] = cursor.fetchone()[0]

                # 已知房屋数量
                cursor.execute("SELECT COUNT(*) FROM known_houses")
                stats['known_house_count'] = cursor.fetchone()[0]

                # 数据库文件大小
                stats['db_file_size'] = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0

                return stats
        except Exception as e:
            return {"error": str(e)}

    def export_to_json(self, export_path: str = None) -> str:
        """导出数据到JSON文件"""
        if export_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_path = f"data_export_{timestamp}.json"

        export_data = {
            'config': self.get_all_config(),
            'devices': self.get_devices(),
            'monitor_configs': self.get_monitor_configs(),
            'history': self.get_history(),
            'estate_ids': self.get_estate_ids(),
            'known_houses': self.get_known_houses(),
            'last_update': self.get_last_update(),
            'export_timestamp': datetime.now().isoformat(),
            'database_mode': True
        }

        import json
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)

        return export_path

    # ========== 便捷方法 ==========

    def is_using_database(self) -> bool:
        """是否使用数据库模式（始终返回True）"""
        return True

    def get_enabled_monitor_configs(self) -> List[Dict[str, Any]]:
        """获取启用的监控配置"""
        configs = self.get_monitor_configs()
        return [config for config in configs if config.get('enabled', True)]

    def get_devices_by_ids(self, device_ids: List[str]) -> List[Dict[str, Any]]:
        """根据ID列表获取设备"""
        return [self.device_model.get_by_id(device_id)
                for device_id in device_ids
                if self.device_model.get_by_id(device_id)]

    def get_active_devices(self) -> List[Dict[str, Any]]:
        """获取未过期的设备"""
        devices = self.get_devices()
        active_devices = []

        for device in devices:
            expire_date = device.get('expire_date')
            if expire_date is None:
                # 没有过期时间，视为永久有效
                active_devices.append(device)
            else:
                try:
                    # 尝试解析过期时间
                    if isinstance(expire_date, str):
                        expire_dt = datetime.strptime(expire_date, "%Y-%m-%d").date()
                    else:
                        expire_dt = expire_date

                    if expire_dt >= date.today():
                        active_devices.append(device)
                except (ValueError, TypeError):
                    # 解析失败，视为永久有效
                    active_devices.append(device)

        return active_devices


# 全局数据管理器实例
_data_manager = None

def get_data_manager() -> DataManager:
    """获取全局数据管理器实例"""
    global _data_manager
    if _data_manager is None:
        _data_manager = DataManager()
    return _data_manager

def init_data_manager(db_path: str = "monitoring.db") -> DataManager:
    """初始化数据管理器，使用数据库模式"""
    global _data_manager
    _data_manager = DataManager(db_path)
    return _data_manager