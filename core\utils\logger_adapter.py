"""
日志适配器模块
为现有日志系统提供兼容性适配，确保平滑迁移到统一日志系统
"""

import os
import logging
from typing import Optional, Dict, Any
from datetime import datetime
from .unified_logging import get_unified_logger, UnifiedLogger, LogLevel


class LoggerAdapter:
    """
    原Logger类的兼容适配器
    保持与原core.utils.logger.Logger类完全相同的接口
    """

    def __init__(self, name: str = "系统", level: LogLevel = LogLevel.INFO, enable_masking: bool = True):
        # 创建配置字典
        config = {
            'level': level.name,
            'enable_masking': enable_masking,
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'date_format': '%Y-%m-%d %H:%M:%S',
            'use_colors': True
        }

        # 使用统一日志器
        self._unified_logger = get_unified_logger(name, config)

        # 兼容属性
        self.name = name
        self.level = level
        self.enable_masking = enable_masking
        self.push_success_count = 0
        self.push_fail_count = 0
        self.last_status_summary_time = datetime.now().timestamp()
        self.status_summary_interval = 1800

    @property
    def masker(self):
        """延迟初始化脱敏器 - 兼容原接口"""
        return self._unified_logger.masker

    def _format_time(self, dt: datetime) -> str:
        """格式化时间，包含毫秒 - 兼容原接口"""
        return self._unified_logger._format_time(dt)

    def _safe_format_message(self, message: str) -> str:
        """安全格式化消息，自动脱敏敏感信息 - 兼容原接口"""
        return self._unified_logger._safe_format_message(message)

    def _safe_format_context(self, context: Optional[Any]) -> str:
        """安全格式化上下文信息 - 兼容原接口"""
        return self._unified_logger._safe_format_context(context)

    def log(self, message: str, level: LogLevel = LogLevel.INFO,
            house_name: Optional[str] = None, context: Optional[Any] = None):
        """多级别日志输出方法，完全兼容原Logger类接口"""
        self._unified_logger.log(message, level, house_name, context)

    def log_with_data(self, message: str, data: Any, level: LogLevel = LogLevel.INFO,
                     house_name: Optional[str] = None):
        """记录包含数据的日志，完全兼容原Logger类接口"""
        self._unified_logger.log_with_data(message, data, level, house_name)

    def debug(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """调试级别日志 - 兼容原接口"""
        self._unified_logger.debug(message, house_name, context)

    def info(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """信息级别日志 - 兼容原接口"""
        self._unified_logger.info(message, house_name, context)

    def warning(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """警告级别日志 - 兼容原接口"""
        self._unified_logger.warning(message, house_name, context)

    def error(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """错误级别日志 - 兼容原接口"""
        self._unified_logger.error(message, house_name, context)

    def critical(self, message: str, house_name: Optional[str] = None, context: Optional[Any] = None):
        """严重错误级别日志 - 兼容原接口"""
        self._unified_logger.critical(message, house_name, context)

    def debug_data(self, message: str, data: Any, house_name: Optional[str] = None):
        """调试级别数据日志 - 兼容原接口"""
        self._unified_logger.debug_data(message, data, house_name)

    def info_data(self, message: str, data: Any, house_name: Optional[str] = None):
        """信息级别数据日志 - 兼容原接口"""
        self._unified_logger.info_data(message, data, house_name)

    def warning_data(self, message: str, data: Any, house_name: Optional[str] = None):
        """警告级别数据日志 - 兼容原接口"""
        self._unified_logger.warning_data(message, data, house_name)

    def error_data(self, message: str, data: Any, house_name: Optional[str] = None):
        """错误级别数据日志 - 兼容原接口"""
        self._unified_logger.error_data(message, data, house_name)


def setup_logging_replacement(app):
    """
    替代web.utils.logging_setup.setup_logging的函数

    Args:
        app: Flask应用实例

    Returns:
        tuple: (log_dir, log_file_base) 日志目录和文件基础路径
    """
    import sys

    # 创建log目录
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'log')
    log_dir = os.path.normpath(log_dir)

    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file_base = os.path.join(log_dir, 'monitor')

    # 清除Flask logger的现有处理器
    app.logger.handlers.clear()

    # 直接使用我们修复的WindowsColorHandler为Flask创建UTF-8编码的控制台处理器
    from .unified_logging import WindowsColorHandler, SafeUTF8TimedRotatingFileHandler
    import sys

    # 创建控制台处理器（启用emoji转换和UTF-8编码修复）
    enable_emoji_conversion = sys.platform == 'win32'  # Windows环境下启用emoji转换
    console_handler = WindowsColorHandler(
        use_colors=True,
        enable_emoji_conversion=enable_emoji_conversion
    )
    console_handler.setLevel(logging.INFO)

    # 设置包含毫秒的格式器
    formatter = logging.Formatter(
        '%(asctime)s,%(msecs)03d - %(name)s - %(levelname)s - %(message)s',
        '%Y-%m-%d %H:%M:%S'
    )
    console_handler.setFormatter(formatter)

    # 添加控制台处理器到Flask logger
    app.logger.addHandler(console_handler)

    # 创建文件处理器（使用安全的UTF-8处理器）
    try:
        file_handler = SafeUTF8TimedRotatingFileHandler(
            os.path.join(log_dir, 'monitor.log'),
            when='midnight',
            interval=1,
            backupCount=30,
            enable_emoji_conversion=False  # 文件日志保持emoji原样
        )
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        app.logger.addHandler(file_handler)
    except Exception as e:
        print(f"警告: 无法创建文件日志处理器: {str(e)}")

    # 设置Flask logger级别和传播控制
    app.logger.setLevel(logging.INFO)
    app.logger.propagate = False  # 防止传播到根logger造成编码冲突

    # 测试写入权限
    try:
        test_log_path = os.path.join(log_dir, "test_write.tmp")
        with open(test_log_path, 'w', encoding='utf-8') as f:
            f.write("测试写入权限")
        os.remove(test_log_path)
        app.logger.info(f"日志目录 {log_dir} 写入权限正常")
    except Exception as e:
        print(f"警告: 日志目录 {log_dir} 可能没有写入权限: {str(e)}")

    return log_dir, log_file_base


def get_current_log_file_replacement():
    """
    替代web.utils.logging_setup.get_current_log_file的函数

    Returns:
        str: 当前日志文件的完整路径
    """
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'log')
    log_dir = os.path.normpath(log_dir)
    log_file_base = os.path.join(log_dir, 'monitor')

    today = datetime.now().strftime("%Y-%m-%d")
    log_path = f"{log_file_base}.log.{today}.log"

    if not os.path.exists(log_path):
        return f"{log_file_base}.log"

    return os.path.normpath(log_path)


def log_callback_replacement(app_logger, message, level=None, house_name=None, context=None):
    """
    替代web.utils.logging_setup.log_callback的函数

    Args:
        app_logger: Flask应用的logger实例
        message: 日志消息
        level: 日志级别
        house_name: 房源名称
        context: 上下文信息（如异常对象）
    """
    try:
        # 确保消息是UTF-8编码
        if isinstance(message, str):
            message = message.encode('utf-8').decode('utf-8')

        # 处理房源名称前缀
        if house_name:
            message = f"[{house_name}] {message}"

        # 记录消息
        app_logger.info(message)

        # 如果有异常上下文，也记录异常详情
        if context and isinstance(context, Exception):
            app_logger.error(f"错误详情: {str(context)}")
            import traceback
            app_logger.error(f"堆栈跟踪: {traceback.format_exc()}")

        # 强制刷新所有处理器，确保实时写入到文件
        for handler in app_logger.handlers:
            if hasattr(handler, 'flush'):
                handler.flush()
    except Exception as e:
        app_logger.error(f"日志记录错误: {str(e)}")


# 兼容性别名
Logger = LoggerAdapter