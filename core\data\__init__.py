"""数据模块 - 模型定义和数据管理"""

# 修复import *污染，改为显式导入
from .models import (
    Device,
    House,
    HouseDetail,
    HouseDetailBaseline,
    MonitorResult,
    HistoryData,
    BaselineDatabase,
    DeviceType,
    ChangeType
)
from .history_manager import HistoryManager

# 保持向后兼容性
__all__ = [
    # 数据模型
    'Device',
    'House',
    'HouseDetail',
    'HouseDetailBaseline',
    'MonitorResult',
    'HistoryData',
    'BaselineDatabase',
    # 枚举类型
    'DeviceType',
    'ChangeType',
    # 管理器
    'HistoryManager'
]