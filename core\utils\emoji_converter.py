"""
Emoji转换器模块
用于将Unicode emoji字符转换为Windows控制台安全的文本替代
"""

import re
from typing import Dict, Pattern


class EmojiConverter:
    """Emoji到文本的转换器"""

    # Unicode emoji到文本的映射表
    EMOJI_MAP: Dict[str, str] = {
        # 成功/完成类
        '✅': '[成功]',
        '🎉': '[庆祝]',
        '🏆': '[成功]',
        '💯': '[完美]',

        # 警告/注意类
        '': '[警告]',
        '⚡': '[注意]',
        '🔔': '[提醒]',

        # 错误/失败类
        '❌': '[失败]',
        '🚫': '[禁止]',
        '💔': '[失败]',
        '💥': '[异常]',
        '🔥': '[错误]',

        # 信息/状态类
        '🔍': '[分析]',
        '📊': '[统计]',
        '📋': '[详情]',
        '📝': '[记录]',
        '📤': '[输出]',
        '🔄': '[更新]',
        '💾': '[保存]',
        '📁': '[目录]',
        '📂': '[文件]',

        # 流程/步骤类
        '🎯': '[执行]',
        '🌐': '[网络]',
        '🔗': '[连接]',
        '📡': '[代理]',
        '🍪': '[Cookie]',
        '🔑': '[Token]',
        '⚙️': '[配置]',
        '🔧': '[设置]',

        # 用户/身份类
        '👤': '[用户]',
        '🆔': '[ID]',
        '🏘️': '[小区]',

        # 时间/状态类
        '⏱️': '[耗时]',
        '🕒': '[时间]',
        '⏰': '[定时]',

        # 特殊符号类
        '→': '->',
        '←': '<-',
        '↑': '^',
        '↓': 'v',
        '⭐': '[星]',
        '🌟': '[亮星]',

        # 分隔符类
        '=': '=',
        '-': '-',
        '|': '|',
    }

    def __init__(self):
        """初始化转换器，编译正则表达式模式"""
        # 创建正则表达式模式，用于快速匹配emoji
        emoji_chars = ''.join(self.EMOJI_MAP.keys())
        # 使用Unicode字符类匹配所有可能的emoji，包括变体选择器
        self.emoji_pattern: Pattern = re.compile(
            f'[{re.escape(emoji_chars)}][\uFE0F\uFE0E]?|'  # 映射表中的emoji（可能带变体选择器）
            r'[\U0001F600-\U0001F64F][\uFE0F\uFE0E]?|'     # 表情符号
            r'[\U0001F300-\U0001F5FF][\uFE0F\uFE0E]?|'     # 杂项符号
            r'[\U0001F680-\U0001F6FF][\uFE0F\uFE0E]?|'     # 交通运输符号
            r'[\U0001F1E0-\U0001F1FF][\uFE0F\uFE0E]?|'     # 区域指示符号
            r'[\U00002600-\U000027BF][\uFE0F\uFE0E]?|'     # 杂项符号
            r'[\U0001F900-\U0001F9FF][\uFE0F\uFE0E]?|'     # 补充符号
            r'[\U00002700-\U000027BF][\uFE0F\uFE0E]?'      # 装饰符号
        )

    def convert_text(self, text: str) -> str:
        """
        将文本中的emoji转换为安全的文本替代

        Args:
            text: 包含emoji的原始文本

        Returns:
            转换后的安全文本
        """
        if not text:
            return text

        def replace_emoji(match):
            emoji = match.group(0)
            return self.EMOJI_MAP.get(emoji, f'[{ord(emoji):04X}]')

        # 使用正则表达式替换所有emoji
        return self.emoji_pattern.sub(replace_emoji, text)

    def is_safe_for_console(self, text: str) -> bool:
        """
        检查文本是否对控制台输出安全

        Args:
            text: 要检查的文本

        Returns:
            如果文本安全则返回True，否则返回False
        """
        if not text:
            return True

        try:
            # 尝试编码为GBK，如果成功则认为安全
            text.encode('gbk')
            return True
        except UnicodeEncodeError:
            return False

    def make_console_safe(self, text: str) -> str:
        """
        确保文本对控制台输出安全

        Args:
            text: 原始文本

        Returns:
            控制台安全的文本
        """
        if self.is_safe_for_console(text):
            return text

        # 转换emoji
        safe_text = self.convert_text(text)

        # 如果转换后仍不安全，进行进一步处理
        if not self.is_safe_for_console(safe_text):
            # 移除或替换不安全的字符
            safe_chars = []
            for char in safe_text:
                try:
                    char.encode('gbk')
                    safe_chars.append(char)
                except UnicodeEncodeError:
                    # 替换为问号或其他安全字符
                    safe_chars.append('?')
            safe_text = ''.join(safe_chars)

        return safe_text

    def add_emoji_mapping(self, emoji: str, replacement: str):
        """
        添加新的emoji映射

        Args:
            emoji: emoji字符
            replacement: 替代文本
        """
        self.EMOJI_MAP[emoji] = replacement
        # 重新编译正则表达式
        emoji_chars = ''.join(self.EMOJI_MAP.keys())
        self.emoji_pattern = re.compile(
            f'[{re.escape(emoji_chars)}]|'
            r'[\U0001F600-\U0001F64F]|'
            r'[\U0001F300-\U0001F5FF]|'
            r'[\U0001F680-\U0001F6FF]|'
            r'[\U0001F1E0-\U0001F1FF]|'
            r'[\U00002600-\U000027BF]|'
            r'[\U0001F900-\U0001F9FF]|'
            r'[\U00002700-\U000027BF]'
        )


# 创建全局转换器实例
_converter = None

def get_emoji_converter() -> EmojiConverter:
    """获取全局emoji转换器实例"""
    global _converter
    if _converter is None:
        _converter = EmojiConverter()
    return _converter

def convert_emoji_to_text(text: str) -> str:
    """便捷函数：将emoji转换为文本"""
    return get_emoji_converter().convert_text(text)

def make_console_safe(text: str) -> str:
    """便捷函数：确保文本对控制台安全"""
    return get_emoji_converter().make_console_safe(text)