"""
413231通知API模块 - 处理通知测试和自定义推送相关的API接口
"""

import requests
import time
from datetime import datetime
from urllib.parse import quote
from flask import Blueprint, jsonify, request, current_app
from web.utils.decorators import login_required

# 导入DeviceModel类 - 使用相对导入
from core.data.database import DeviceModel, DatabaseManager

notification_api = Blueprint('notification_api', __name__)

# 获取数据库管理器和设备模型实例的函数
def get_device_model():
    db_path = current_app.config.get('DATABASE_PATH', 'monitoring.db')
    db_manager = DatabaseManager(db_path)
    return DeviceModel(db_manager)


def send_test_notification_sync(device, title, content):
    """
    同步发送测试通知到指定设备
    专门为测试通知设计的简单同步函数，避免复杂的异步包装器

    :param device: 设备配置字典
    :param title: 通知标题
    :param content: 通知内容
    :return: 布尔值，表示是否发送成功
    """
    device_type = device.get('type')
    device_name = device.get('name', device.get('id', 'Unknown'))

    try:
        if device_type == "bark":
            return _send_bark_sync(device, title, content)
        elif device_type == "wxpush":
            return _send_wxpush_sync(device, title, content)
        elif device_type == "pushme":
            return _send_pushme_sync(device, title, content)
        else:
            current_app.logger.error(f"不支持的设备类型: {device_type}")
            return False
    except Exception as e:
        current_app.logger.error(f"向设备 {device_name} 发送测试通知失败: {str(e)}")
        return False


def _send_bark_sync(device, title, content):
    """发送Bark通知（同步版本）"""
    bark_key = device.get("bark_key")
    if not bark_key:
        current_app.logger.error(f"设备 {device.get('name')} 缺少Bark Key")
        return False

    # URL编码标题和内容，防止特殊字符干扰
    safe_title = quote(title)
    safe_content = quote(content)

    url = f"https://api.day.app/{bark_key}/{safe_title}/{safe_content}"

    # 添加设备参数
    params = device.get("params", {}).copy()
    params["_t"] = int(time.time())  # 防止缓存

    try:
        response = requests.get(url, params=params, timeout=10)
        if response.status_code == 200:
            current_app.logger.info(f"成功发送Bark测试通知到 {device.get('name')}")
            return True
        else:
            current_app.logger.error(f"Bark测试通知发送失败: HTTP {response.status_code}")
            return False
    except requests.exceptions.Timeout:
        current_app.logger.error(f"向 {device.get('name')} 发送Bark测试通知超时")
        return False
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"向 {device.get('name')} 发送Bark测试通知网络异常: {str(e)}")
        return False


def _send_wxpush_sync(device, title, content):
    """
    发送WxPush通知（同步版本）
    现在使用批量发送API，即使是单个设备，避免QPS限制问题
    """
    uid = device.get("uid")
    if not uid:
        current_app.logger.error(f"设备 {device.get('name')} 缺少微信推送UID")
        return False

    # 尝试使用notification_service进行批量发送
    if hasattr(current_app, 'notification_service') and current_app.notification_service:
        try:
            # 使用notification_service的批量发送方法
            success_count = current_app.notification_service.send_wxpush_batch([uid], title, content)
            if success_count > 0:
                current_app.logger.info(f"成功通过批量API发送WxPush测试通知到 {device.get('name')}")
                return True
            else:
                current_app.logger.error(f"通过批量API发送WxPush测试通知到 {device.get('name')} 失败")
                return False
        except Exception as e:
            current_app.logger.error(f"使用批量API发送WxPush测试通知异常: {str(e)}")
            # 发生异常时，回退到直接API调用，但仍使用批量格式
            current_app.logger.info("回退到直接批量API调用")

    # 如果notification_service不可用或发生异常，使用直接API调用批量发送
    # 获取app_token
    unified_config = current_app.unified_config
    app_token = unified_config.get("wxpush_app_token", "AT_SEGaKHPlPgjziZNkZVJwISroZvFcxQDZ")

    data = {
        "appToken": app_token,
        "content": f"<b>{title}</b><br/>" + content.replace('\n', '<br/>'),
        "contentType": 2,  # 2代表HTML
        "uids": [uid],  # 仍使用批量格式，但只包含一个UID
    }

    try:
        # 增加重试机制，最多重试3次
        for attempt in range(3):
            try:
                response = requests.post(
                    "https://wxpusher.zjiecode.com/api/send/message",
                    json=data,
                    timeout=15
                )
                if response.status_code == 200:
                    resp_json = response.json()
                    if resp_json.get("success"):
                        current_app.logger.info(f"成功发送WxPush测试通知到 {device.get('name')}")
                        return True
                    else:
                        error_msg = resp_json.get("msg", "")
                        if "QPS" in error_msg or "访问超过" in error_msg:
                            # QPS限制，指数退避重试
                            wait_time = (2 ** attempt) + (time.time() % 1)  # 添加随机抖动
                            current_app.logger.warning(f"WxPush遇到QPS限制，将在 {wait_time:.1f} 秒后重试")
                            time.sleep(wait_time)
                            continue
                        else:
                            current_app.logger.error(f"WxPush测试通知发送失败: {error_msg}")
                            return False
                else:
                    current_app.logger.error(f"WxPush测试通知发送失败: HTTP {response.status_code}")
                    return False

            except requests.exceptions.Timeout:
                current_app.logger.warning(f"WxPush测试通知超时，尝试重试 ({attempt + 1}/3)")
                if attempt == 2:  # 最后一次重试
                    current_app.logger.error("WxPush测试通知最终超时失败")
                    return False
                time.sleep(1 * (attempt + 1))

    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"向 {device.get('name')} 发送WxPush测试通知网络异常: {str(e)}")
        return False


def _send_pushme_sync(device, title, content):
    """发送PushMe通知（同步版本）"""
    push_key = device.get("pushme_key")
    if not push_key:
        current_app.logger.error(f"设备 {device.get('name')} 缺少PushMe Key")
        return False

    current_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    data = {
        "push_key": push_key,
        "title": title,
        "content": content,
        "type": "text",
        "date": current_time_str,
    }

    try:
        response = requests.post(
            "https://push.i-i.me",
            data=data,
            timeout=10
        )
        if response.status_code == 200:
            current_app.logger.info(f"成功发送PushMe测试通知到 {device.get('name')}")
            return True
        else:
            current_app.logger.error(f"PushMe测试通知发送失败: HTTP {response.status_code}")
            return False
    except requests.exceptions.Timeout:
        current_app.logger.error(f"向 {device.get('name')} 发送PushMe测试通知超时")
        return False
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"向 {device.get('name')} 发送PushMe测试通知网络异常: {str(e)}")
        return False


@notification_api.route('/test_notification', methods=['POST'])
@login_required
def test_notification():
    """测试推送"""
    try:
        data = request.json
        house_name = data.get('house_name', '测试房源')
        device_id = data.get('device_id')

        if not device_id:
            return jsonify({'error': '缺少设备ID'}), 400

        # 从数据库获取设备信息而非配置文件
        current_app.logger.info("正在从数据库获取设备信息...")
        # 获取设备模型
        device_model = get_device_model()
        # 获取指定ID的设备
        device = device_model.get_by_id(device_id)

        if not device:
            current_app.logger.error(f"在数据库中未找到设备ID '{device_id}'")
            return jsonify({'error': f'未找到设备ID: {device_id}'}), 404

        title = f"{house_name} 青城住房监控测试推送"
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        content = f"这是一条青城住房监控测试推送\n时间：{current_time}"
        device_name = device.get("name", device_id)

        # 使用简单的同步通知函数进行测试
        success = send_test_notification_sync(device, title, content)

        if success:
            current_app.logger.info(f"[{house_name}] 测试推送到 {device_name} 成功")
            result = {
                'status': 'success',
                'message': f'已向设备 {device_name} 发送测试推送成功',
                'device': device_name
            }

            # 如果是Bark设备，返回推送参数信息
            if device.get('type') == 'bark' and device.get('params'):
                result['params'] = device.get('params')

            return jsonify(result)
        else:
            current_app.logger.error(f"[{house_name}] 测试推送到 {device_name} 失败，请检查日志获取详情")
            return jsonify({'error': f'推送失败，请检查日志获取详情'}), 500

    except Exception as e:
        current_app.logger.error(f"测试推送失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@notification_api.route('/custom_push', methods=['POST'])
@login_required
def custom_push():
    """发送自定义内容到选定设备"""
    try:
        data = request.json

        # 验证必要的参数
        if not data.get('title'):
            return jsonify({'error': '缺少推送标题'}), 400
        if not data.get('content'):
            return jsonify({'error': '缺少推送内容'}), 400
        if not data.get('device_ids') or not isinstance(data.get('device_ids'), list):
            return jsonify({'error': '缺少设备ID列表'}), 400

        title = data.get('title')
        content = data.get('content')
        device_ids = data.get('device_ids')

        # 从数据库获取设备列表
        device_model = get_device_model()
        all_devices = device_model.get_all()

        # 存储推送结果
        results = []
        current_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 按设备类型分组
        wxpush_devices = []
        other_devices = []
        not_found_ids = list(device_ids)  # Copy list

        for device_id in device_ids:
            # 比较时去除前后空格，并统一转为字符串
            device = next((d for d in all_devices if str(d.get('id')).strip() == str(device_id).strip()), None)
            if device:
                if device_id in not_found_ids:
                    not_found_ids.remove(device_id)

                # WxPush设备需要特殊批量处理
                if device.get("type") == "wxpush":
                    wxpush_devices.append(device)
                else:
                    other_devices.append(device)

        # 处理WxPush设备 - 使用批量API
        notification_service = current_app.notification_service
        if wxpush_devices:
            uids = [d.get("uid") for d in wxpush_devices if d.get("uid")]

            if uids:
                success_count = notification_service.send_wxpush_batch(uids, title, content)
                # WxPush的批量API不返回每个uid的成功/失败状态，所以我们只能笼统地处理
                if success_count > 0:
                    current_app.logger.info(f"自定义WxPush批量推送成功发送到 {success_count}/{len(uids)} 个设备")
                    for device in wxpush_devices:
                        results.append({'device_id': device.get('id'),'device_name': device.get('name'),'device_type': 'wxpush','status': 'success'})
                else:
                    current_app.logger.error("自定义WxPush批量推送失败")
                    for device in wxpush_devices:
                        results.append({'device_id': device.get('id'),'device_name': device.get('name'),'device_type': 'wxpush','status': 'failed', 'message': '批量推送失败'})

        # 处理其他设备（Bark, PushMe等）
        for device in other_devices:
            device_id = device.get('id')
            device_name = device.get('name', device_id)
            device_type = device.get('type', 'unknown')

            # 使用同步版本的推送函数，避免异步包装器的问题
            success = send_test_notification_sync(device, title, content)

            if success:
                results.append({'device_id': device_id, 'device_name': device_name, 'device_type': device_type, 'status': 'success'})
            else:
                results.append({'device_id': device_id, 'device_name': device_name, 'device_type': device_type, 'status': 'failed', 'message': f"推送失败，请查看日志"})

        # 添加未找到的设备到结果
        for device_id in not_found_ids:
            results.append({'device_id': device_id, 'status': 'failed', 'message': '设备不存在'})

        # 统计结果
        success_count = sum(1 for r in results if r.get('status') == 'success')
        fail_count = len(results) - success_count

        return jsonify({
            'status': 'finished',
            'total': len(results),
            'success_count': success_count,
            'fail_count': fail_count,
            'results': results,
            'timestamp': current_time_str
        })

    except Exception as e:
        current_app.logger.error(f"自定义推送处理失败: {str(e)}")
        return jsonify({'error': str(e)}), 500