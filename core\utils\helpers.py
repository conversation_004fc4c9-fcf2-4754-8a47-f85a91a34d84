"""辅助函数工具模块"""

import asyncio
import json
import random
import time
from typing import Any, Dict, Optional
from datetime import datetime


async def random_delay(min_seconds: float = 0.1, max_seconds: float = 0.2):
    """异步随机延迟，防止请求过于频繁"""
    delay = random.uniform(min_seconds, max_seconds)
    await asyncio.sleep(delay)


def random_delay_sync(min_seconds: float = 0.1, max_seconds: float = 0.3):
    """同步随机延迟"""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """安全解析JSON字符串"""
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default


def safe_json_dumps(obj: Any, default: Any = None) -> str:
    """安全序列化为JSON字符串"""
    try:
        return json.dumps(obj, ensure_ascii=False, indent=2)
    except (TypeError, ValueError):
        return str(default) if default is not None else "{}"


def is_valid_ip_port(ip_port: str) -> bool:
    """验证IP:PORT格式"""
    try:
        parts = ip_port.split(':')
        if len(parts) != 2:
            return False

        ip, port = parts
        # 简单的IP格式验证
        ip_parts = ip.split('.')
        if len(ip_parts) != 4:
            return False

        for part in ip_parts:
            if not part.isdigit() or not 0 <= int(part) <= 255:
                return False

        # 端口验证
        if not port.isdigit() or not 1 <= int(port) <= 65535:
            return False

        return True
    except:
        return False


def retry_on_exception(max_retries: int = 3, exceptions: tuple = (Exception,)):
    """重试装饰器 - 增强版本，包含异常类型验证"""

    # 验证异常类型参数
    if not isinstance(exceptions, (tuple, list)):
        exceptions = (exceptions,)

    # 确保所有元素都是BaseException的子类
    validated_exceptions = []
    for exc in exceptions:
        if isinstance(exc, type) and issubclass(exc, BaseException):
            validated_exceptions.append(exc)
        else:
            # 如果不是有效的异常类，记录警告并使用通用Exception
            print(f"警告: {exc} 不是有效的异常类，将使用Exception替代")
            validated_exceptions.append(Exception)

    # 如果没有有效的异常类，使用默认的Exception
    if not validated_exceptions:
        validated_exceptions = [Exception]

    final_exceptions = tuple(validated_exceptions)

    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except final_exceptions as e:
                    if attempt == max_retries - 1:
                        raise e
                    wait_time = 2 ** attempt + random.uniform(0, 1)
                    await asyncio.sleep(wait_time)

        def sync_wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except final_exceptions as e:
                    if attempt == max_retries - 1:
                        raise e
                    wait_time = 2 ** attempt + random.uniform(0, 1)
                    time.sleep(wait_time)

        # 检查函数是否是异步的
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator