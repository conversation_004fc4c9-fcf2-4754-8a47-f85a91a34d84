"""敏感数据脱敏工具模块 - 保护日志中的敏感信息"""

import re
import json
from typing import Dict, Any, Union, List


class SensitiveDataMasker:
    """敏感数据脱敏工具类"""

    # 敏感字段名称（小写，用于模糊匹配）
    SENSITIVE_FIELDS = {
        'cookie', 'cookies', 'token', 'access_token', 'refresh_token',
        'password', 'passwd', 'pwd', 'secret', 'key', 'api_key',
        'bark_key', 'pushme_key', 'uid', 'sessioncode', 'auth',
        'authorization', 'credential', 'private', 'cert', 'signature'
    }

    # 敏感值模式（正则表达式）
    SENSITIVE_PATTERNS = [
        (r'AT_[A-Za-z0-9]{20,}', '敏感Token'),  # 微信推送Token
        (r'[A-Za-z0-9]{40,}', '长Token'),        # 长Token/Key
        (r'\d{11}', '手机号'),                   # 手机号
        (r'\d{15,18}', '身份证号'),              # 身份证号
    ]

    def __init__(self, mask_char: str = '*', preserve_length: int = 4):
        """
        初始化脱敏器

        Args:
            mask_char: 脱敏字符，默认为*
            preserve_length: 保留的前后字符长度，默认为4
        """
        self.mask_char = mask_char
        self.preserve_length = preserve_length

    def mask_string(self, text: str) -> str:
        """
        脱敏字符串

        Args:
            text: 要脱敏的字符串

        Returns:
            脱敏后的字符串
        """
        if not isinstance(text, str) or len(text) <= 8:
            return self.mask_char * 6

        preserve_len = min(self.preserve_length, len(text) // 3)
        if preserve_len * 2 >= len(text):
            return self.mask_char * 6

        return (text[:preserve_len] +
                self.mask_char * 6 +
                text[-preserve_len:])

    def mask_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        脱敏字典数据

        Args:
            data: 要脱敏的字典

        Returns:
            脱敏后的字典
        """
        if not isinstance(data, dict):
            return data

        masked = {}
        for key, value in data.items():
            key_lower = key.lower()

            # 检查键名是否为敏感字段
            if any(sensitive in key_lower for sensitive in self.SENSITIVE_FIELDS):
                if isinstance(value, str):
                    masked[key] = self.mask_string(value)
                elif isinstance(value, dict):
                    # 如果值是字典（如cookies），递归脱敏
                    masked[key] = self.mask_dict(value)
                else:
                    masked[key] = f"<{type(value).__name__}:已脱敏>"
            elif isinstance(value, dict):
                # 递归处理嵌套字典
                masked[key] = self.mask_dict(value)
            elif isinstance(value, list):
                # 处理列表
                masked[key] = self.mask_list(value)
            elif isinstance(value, str):
                # 检查字符串内容是否匹配敏感模式
                masked[key] = self.mask_patterns_in_string(value)
            else:
                masked[key] = value

        return masked

    def mask_list(self, data: List[Any]) -> List[Any]:
        """
        脱敏列表数据

        Args:
            data: 要脱敏的列表

        Returns:
            脱敏后的列表
        """
        masked = []
        for item in data:
            if isinstance(item, dict):
                masked.append(self.mask_dict(item))
            elif isinstance(item, list):
                masked.append(self.mask_list(item))
            elif isinstance(item, str):
                masked.append(self.mask_patterns_in_string(item))
            else:
                masked.append(item)
        return masked

    def mask_patterns_in_string(self, text: str) -> str:
        """
        脱敏字符串中的敏感模式

        Args:
            text: 要检查的字符串

        Returns:
            脱敏后的字符串
        """
        if not isinstance(text, str):
            return text

        result = text
        for pattern, description in self.SENSITIVE_PATTERNS:
            if re.search(pattern, result):
                result = re.sub(pattern, f'<{description}:已脱敏>', result)

        return result

    def mask_json_string(self, json_str: str) -> str:
        """
        脱敏JSON字符串

        Args:
            json_str: JSON字符串

        Returns:
            脱敏后的JSON字符串
        """
        try:
            data = json.loads(json_str)
            masked_data = self.mask_dict(data) if isinstance(data, dict) else data
            return json.dumps(masked_data, ensure_ascii=False, indent=2)
        except (json.JSONDecodeError, TypeError):
            # 如果不是有效JSON，直接进行模式脱敏
            return self.mask_patterns_in_string(json_str)

    def mask_any(self, data: Any) -> Any:
        """
        脱敏任意类型的数据

        Args:
            data: 要脱敏的数据

        Returns:
            脱敏后的数据
        """
        if isinstance(data, dict):
            return self.mask_dict(data)
        elif isinstance(data, list):
            return self.mask_list(data)
        elif isinstance(data, str):
            # 尝试解析为JSON
            if data.strip().startswith(('{', '[')):
                try:
                    parsed = json.loads(data)
                    return json.dumps(self.mask_any(parsed), ensure_ascii=False)
                except (json.JSONDecodeError, TypeError):
                    pass
            return self.mask_patterns_in_string(data)
        else:
            return data

    def safe_log_format(self, message: str, *args, **kwargs) -> str:
        """
        安全的日志格式化，自动脱敏参数

        Args:
            message: 日志消息模板
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            脱敏后的格式化字符串
        """
        try:
            # 脱敏所有参数
            safe_args = [self.mask_any(arg) for arg in args]
            safe_kwargs = {k: self.mask_any(v) for k, v in kwargs.items()}

            # 格式化消息
            if args or kwargs:
                return message.format(*safe_args, **safe_kwargs)
            else:
                return self.mask_patterns_in_string(message)
        except Exception:
            # 如果格式化失败，至少脱敏原始消息
            return self.mask_patterns_in_string(str(message))


# 全局脱敏器实例
global_masker = SensitiveDataMasker()


def mask_sensitive_data(data: Any) -> Any:
    """
    全局函数：脱敏敏感数据

    Args:
        data: 要脱敏的数据

    Returns:
        脱敏后的数据
    """
    return global_masker.mask_any(data)


def safe_log(message: str, *args, **kwargs) -> str:
    """
    全局函数：安全的日志格式化

    Args:
        message: 日志消息
        *args: 格式化参数
        **kwargs: 格式化参数

    Returns:
        脱敏后的日志消息
    """
    return global_masker.safe_log_format(message, *args, **kwargs)