#!/usr/bin/env python3
"""
青城住房监控服务启动脚本
独立监控服务 - 解耦版本 2.0
"""

import sys
import os
import signal
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.utils.unified_logging import get_unified_logger

class MonitorServiceStarter:
    """监控服务启动器"""

    def __init__(self):
        self.logger = get_unified_logger("监控服务启动器")
        self.shutdown_requested = False

    def setup_signal_handlers(self):
        """设置基本信号处理器（避免与内层服务冲突）"""
        # 移除复杂的信号处理，让监控服务应用自己处理信号
        # 这里只保留最基本的处理以防万一
        def basic_signal_handler(signum, frame):
            self.logger.info(f"收到信号 {signum}，将交由监控服务处理...")
            self.shutdown_requested = True

        # 只在非Windows系统上设置，Windows由监控服务内部处理
        if sys.platform != 'win32':
            signal.signal(signal.SIGINT, basic_signal_handler)
            if hasattr(signal, 'SIGTERM'):
                signal.signal(signal.SIGTERM, basic_signal_handler)

    def print_header(self):
        """打印系统信息"""
        self.logger.info("="*60)
        self.logger.info("青城住房监控服务 - 独立监控服务 v2.0")
        self.logger.info("="*60)
        self.logger.info("系统环境信息:")
        self.logger.info(f"操作系统: {os.name}")
        self.logger.info(f"Python版本: {sys.version}")
        self.logger.info(f"工作目录: {os.getcwd()}")
        self.logger.info("="*60)

    def check_dependencies(self):
        """检查依赖"""
        self.logger.info("正在检查依赖...")

        # 检查aiohttp
        try:
            import aiohttp
            self.logger.info(f"[+] aiohttp: {aiohttp.__version__}")
        except ImportError:
            self.logger.error("[-] 缺少依赖: aiohttp")
            self.logger.error("请运行: pip install aiohttp")
            return False

        # 检查项目模块
        try:
            from monitor_service.config import monitor_config
            self.logger.info(f"[+] 监控服务配置: {monitor_config.host}:{monitor_config.port}")
        except ImportError as e:
            self.logger.error(f"[-] 项目模块导入失败: {str(e)}")
            return False

        self.logger.info("依赖检查完成")
        self.logger.info("-"*60)
        return True

    def start_service(self):
        """启动监控服务 - 直接调用，无异步包装"""
        try:
            self.logger.info("正在启动监控服务...")
            from monitor_service.app import main as service_main

            # 直接运行监控服务，让它自己管理异步和信号处理
            asyncio.run(service_main())

        except KeyboardInterrupt:
            self.logger.info("监控服务被用户中断")
        except Exception as e:
            self.logger.error(f"启动监控服务失败: {str(e)}", context=e)
            raise

    def run(self):
        """运行启动器"""
        try:
            # 激活SSL错误抑制，减少无害警告
            try:
                from core.exceptions import activate_ssl_suppression
                activate_ssl_suppression()
                self.logger.debug("SSL错误抑制已激活")
            except ImportError:
                self.logger.debug("SSL错误抑制模块不可用，继续启动")

            # 设置基本信号处理器
            self.setup_signal_handlers()

            # 打印头部信息
            self.print_header()

            # 检查依赖
            if not self.check_dependencies():
                sys.exit(1)

            # 在Windows上设置事件循环策略
            if sys.platform == 'win32':
                asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

            # 直接启动监控服务（已包含asyncio.run）
            self.start_service()

        except KeyboardInterrupt:
            self.logger.info("收到中断信号，监控服务已停止")
        except Exception as e:
            self.logger.error(f"启动监控服务失败: {str(e)}", context=e)
            sys.exit(1)
        finally:
            self.logger.info("监控服务启动器已退出")


def main():
    """主函数"""
    starter = MonitorServiceStarter()
    starter.run()


if __name__ == "__main__":
    main()