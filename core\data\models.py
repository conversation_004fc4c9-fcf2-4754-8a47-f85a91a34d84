"""数据模型定义"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Set, Any
from datetime import datetime
from enum import Enum


class DeviceType(Enum):
    """设备类型枚举"""
    BARK = "bark"
    WXPUSH = "wxpush"
    PUSHME = "pushme"


class ChangeType(Enum):
    """变化类型枚举"""
    INCREASE = "increase"
    DECREASE = "decrease"
    NO_CHANGE = "no_change"


@dataclass
class Device:
    """设备模型"""
    id: str
    name: str
    type: str
    config: Dict[str, Any] = field(default_factory=dict)
    expire_date: Optional[str] = None
    created_at: Optional[datetime] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

    @property
    def is_expired(self) -> bool:
        """检查设备是否已过期"""
        if not self.expire_date:
            return False
        try:
            expire_date = datetime.strptime(self.expire_date, "%Y-%m-%d").date()
            return expire_date <= datetime.now().date()
        except ValueError:
            return False

    @property
    def days_until_expiry(self) -> Optional[int]:
        """获取距离过期的天数"""
        if not self.expire_date:
            return None
        try:
            expire_date = datetime.strptime(self.expire_date, "%Y-%m-%d").date()
            today = datetime.now().date()
            return (expire_date - today).days
        except ValueError:
            return None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'type': self.type,
            'config': self.config,
            'expire_date': self.expire_date,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Device':
        """从字典创建设备对象"""
        created_at = None
        if data.get('created_at'):
            try:
                created_at = datetime.fromisoformat(data['created_at'])
            except ValueError:
                created_at = datetime.now()

        return cls(
            id=data['id'],
            name=data['name'],
            type=data['type'],
            config=data.get('config', {}),
            expire_date=data.get('expire_date'),
            created_at=created_at
        )


@dataclass
class House:
    """房源模型"""
    name: str
    enabled: bool = True
    device_ids: List[str] = field(default_factory=list)
    created_at: Optional[datetime] = None
    last_count: int = 0
    estate_id: Optional[str] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'enabled': self.enabled,
            'device_ids': self.device_ids,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_count': self.last_count,
            'estate_id': self.estate_id
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'House':
        """从字典创建房源对象"""
        created_at = None
        if data.get('created_at'):
            try:
                created_at = datetime.fromisoformat(data['created_at'])
            except ValueError:
                created_at = datetime.now()

        return cls(
            name=data['name'],
            enabled=data.get('enabled', True),
            device_ids=data.get('device_ids', []),
            created_at=created_at,
            last_count=data.get('last_count', 0),
            estate_id=data.get('estate_id')
        )


@dataclass
class HouseDetail:
    """房源详情模型"""
    id: str
    name: str
    position: str
    roomno: str
    outtype: str
    direction: str
    area: float
    rent: float

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'position': self.position,
            'roomno': self.roomno,
            'outtype': self.outtype,
            'direction': self.direction,
            'area': self.area,
            'rent': self.rent
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HouseDetail':
        """从字典创建房源详情对象"""
        return cls(
            id=data.get('id', ''),
            name=data.get('name', '未知'),
            position=data.get('position', '未知'),
            roomno=data.get('roomno', '未知'),
            outtype=data.get('outtype', '未知'),
            direction=data.get('direction', '未知'),
            area=float(data.get('area', 0)),
            rent=float(data.get('rent', 0))
        )


@dataclass
class HouseDetailBaseline:
    """房源详情基线数据模型，用于数据库存储"""
    house_id: str  # 关联的房源ID
    detail_id: str  # 房源详情ID
    name: str
    position: str
    roomno: str
    outtype: str
    direction: str
    area: float
    rent: float
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        self.updated_at = self.created_at

    def update(self, detail: HouseDetail) -> bool:
        """根据新的HouseDetail更新基线数据，如有变化返回True"""
        changed = False
        if self.name != detail.name:
            self.name = detail.name
            changed = True
        if self.position != detail.position:
            self.position = detail.position
            changed = True
        if self.roomno != detail.roomno:
            self.roomno = detail.roomno
            changed = True
        if self.outtype != detail.outtype:
            self.outtype = detail.outtype
            changed = True
        if self.direction != detail.direction:
            self.direction = detail.direction
            changed = True
        if self.area != detail.area:
            self.area = detail.area
            changed = True
        if self.rent != detail.rent:
            self.rent = detail.rent
            changed = True

        if changed:
            self.updated_at = datetime.now()
        return changed

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'house_id': self.house_id,
            'detail_id': self.detail_id,
            'name': self.name,
            'position': self.position,
            'roomno': self.roomno,
            'outtype': self.outtype,
            'direction': self.direction,
            'area': self.area,
            'rent': self.rent,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HouseDetailBaseline':
        """从字典创建房源详情基线对象"""
        created_at = None
        updated_at = None

        if data.get('created_at'):
            try:
                created_at = datetime.fromisoformat(data['created_at'])
            except ValueError:
                created_at = datetime.now()

        if data.get('updated_at'):
            try:
                updated_at = datetime.fromisoformat(data['updated_at'])
            except ValueError:
                updated_at = datetime.now()
        else:
            updated_at = created_at

        return cls(
            house_id=data['house_id'],
            detail_id=data['detail_id'],
            name=data.get('name', '未知'),
            position=data.get('position', '未知'),
            roomno=data.get('roomno', '未知'),
            outtype=data.get('outtype', '未知'),
            direction=data.get('direction', '未知'),
            area=float(data.get('area', 0)),
            rent=float(data.get('rent', 0)),
            created_at=created_at,
            updated_at=updated_at
        )

    @classmethod
    def from_house_detail(cls, house_id: str, detail: HouseDetail) -> 'HouseDetailBaseline':
        """从HouseDetail创建基线数据"""
        return cls(
            house_id=house_id,
            detail_id=detail.id,
            name=detail.name,
            position=detail.position,
            roomno=detail.roomno,
            outtype=detail.outtype,
            direction=detail.direction,
            area=detail.area,
            rent=detail.rent
        )


@dataclass
class MonitorResult:
    """监控结果模型"""
    house_name: str
    old_count: int
    new_count: int
    change_type: Optional[ChangeType] = None
    details: Optional[List[HouseDetail]] = None
    timestamp: Optional[datetime] = None
    estate_id: Optional[str] = None
    error_message: Optional[str] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

        # 自动确定变化类型（如果未提供）
        if self.change_type is None:
            if self.new_count > self.old_count:
                self.change_type = ChangeType.INCREASE
            elif self.new_count < self.old_count:
                self.change_type = ChangeType.DECREASE
            else:
                self.change_type = ChangeType.NO_CHANGE

    @property
    def change_amount(self) -> int:
        """获取变化数量"""
        return self.new_count - self.old_count

    @property
    def has_change(self) -> bool:
        """是否有变化"""
        return self.change_type != ChangeType.NO_CHANGE

    @property
    def is_success(self) -> bool:
        """监控是否成功"""
        return self.error_message is None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'house_name': self.house_name,
            'old_count': self.old_count,
            'new_count': self.new_count,
            'change_type': self.change_type.value,
            'change_amount': self.change_amount,
            'details': [d.to_dict() for d in self.details] if self.details else None,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'estate_id': self.estate_id,
            'error_message': self.error_message
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MonitorResult':
        """从字典创建监控结果对象"""
        timestamp = None
        if data.get('timestamp'):
            try:
                timestamp = datetime.fromisoformat(data['timestamp'])
            except ValueError:
                timestamp = datetime.now()

        details = None
        if data.get('details'):
            details = [HouseDetail.from_dict(d) for d in data['details']]

        change_type = ChangeType(data.get('change_type', 'no_change'))

        return cls(
            house_name=data['house_name'],
            old_count=data['old_count'],
            new_count=data['new_count'],
            change_type=change_type,
            details=details,
            timestamp=timestamp,
            estate_id=data.get('estate_id'),
            error_message=data.get('error_message')
        )


@dataclass
class HistoryData:
    """历史数据模型"""
    house_counts: Dict[str, int] = field(default_factory=dict)
    estate_ids: Dict[str, str] = field(default_factory=dict)
    known_houses: Dict[str, Set[str]] = field(default_factory=dict)
    house_details: Dict[str, Dict[str, 'HouseDetail']] = field(default_factory=dict)
    last_update: Optional[datetime] = None

    def __post_init__(self):
        if self.last_update is None:
            self.last_update = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式用于JSON序列化"""
        return {
            'house_counts': self.house_counts,
            'estate_ids': self.estate_ids,
            'known_houses': {k: list(v) for k, v in self.known_houses.items()},
            'house_details': {
                house_name: {house_id: detail.to_dict() for house_id, detail in details.items()}
                for house_name, details in self.house_details.items()
            },
            'last_update': self.last_update.isoformat() if self.last_update else None
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HistoryData':
        """从字典创建历史数据对象"""
        last_update = None
        if data.get('last_update'):
            try:
                last_update = datetime.fromisoformat(data['last_update'])
            except ValueError:
                last_update = datetime.now()

        known_houses = {}
        for k, v in data.get('known_houses', {}).items():
            known_houses[k] = set(v) if isinstance(v, list) else set()

        house_details = {}
        if 'house_details' in data:
            for house_name, details_dict in data['house_details'].items():
                house_details[house_name] = {
                    house_id: HouseDetail.from_dict(detail_data)
                    for house_id, detail_data in details_dict.items()
                }

        return cls(
            house_counts=data.get('house_counts', {}),
            estate_ids=data.get('estate_ids', {}),
            known_houses=known_houses,
            house_details=house_details,
            last_update=last_update
        )


@dataclass
class BaselineDatabase:
    """房源详情基线数据库模型"""
    baselines: Dict[str, Dict[str, HouseDetailBaseline]] = field(default_factory=dict)  # house_name -> (detail_id -> baseline)
    last_update: Optional[datetime] = None

    def __post_init__(self):
        if self.last_update is None:
            self.last_update = datetime.now()

    def add_or_update_baseline(self, house_name: str, house_id: str, detail: HouseDetail) -> bool:
        """添加或更新基线数据，如有变化返回True"""
        if house_name not in self.baselines:
            self.baselines[house_name] = {}

        detail_id = detail.id
        changed = False

        if detail_id in self.baselines[house_name]:
            # 更新现有基线
            baseline = self.baselines[house_name][detail_id]
            changed = baseline.update(detail)
        else:
            # 新增基线数据
            self.baselines[house_name][detail_id] = HouseDetailBaseline.from_house_detail(house_id, detail)
            changed = True

        if changed:
            self.last_update = datetime.now()
        return changed

    def get_baseline(self, house_name: str, detail_id: str) -> Optional[HouseDetailBaseline]:
        """获取指定房源和详情ID的基线数据"""
        return self.baselines.get(house_name, {}).get(detail_id)

    def get_all_baselines(self, house_name: str) -> Dict[str, HouseDetailBaseline]:
        """获取指定房源的所有基线数据"""
        return self.baselines.get(house_name, {})

    def remove_baseline(self, house_name: str, detail_id: str) -> bool:
        """移除基线数据，成功返回True"""
        if house_name in self.baselines and detail_id in self.baselines[house_name]:
            del self.baselines[house_name][detail_id]
            self.last_update = datetime.now()
            # 如果房源没有任何基线数据，删除该房源键
            if not self.baselines[house_name]:
                del self.baselines[house_name]
            return True
        return False

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式用于JSON序列化"""
        return {
            'baselines': {
                house_name: {
                    detail_id: baseline.to_dict()
                    for detail_id, baseline in house_baselines.items()
                }
                for house_name, house_baselines in self.baselines.items()
            },
            'last_update': self.last_update.isoformat() if self.last_update else None
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaselineDatabase':
        """从字典创建基线数据库对象"""
        last_update = None
        if data.get('last_update'):
            try:
                last_update = datetime.fromisoformat(data['last_update'])
            except ValueError:
                last_update = datetime.now()

        baselines = {}
        if 'baselines' in data:
            for house_name, details_dict in data['baselines'].items():
                baselines[house_name] = {
                    detail_id: HouseDetailBaseline.from_dict(baseline_data)
                    for detail_id, baseline_data in details_dict.items()
                }

        return cls(
            baselines=baselines,
            last_update=last_update
        )