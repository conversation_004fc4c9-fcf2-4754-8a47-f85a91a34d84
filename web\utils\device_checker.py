"""
设备过期检查器模块 - 管理设备过期状态检查和通知
"""

import time
import threading
from datetime import datetime, timedelta


class DeviceExpiryChecker:
    """设备过期检查器类"""

    def __init__(self, config_manager, logger, notification_service):
        self.config_manager = config_manager
        self.logger = logger
        self.notification_service = notification_service  # 存储通知服务实例
        self.check_interval = 86400  # 默认每24小时检查一次（单位：秒）
        self.reminder_days = 2  # 提前多少天发送提醒
        self.notification_interval = 86400  # 同一设备的通知间隔（单位：秒），默认24小时
        self.running = False
        self.thread = None
        self.stop_event = threading.Event()  # 使用Event实现高效等待

        # 记录已通知的设备，避免重复发送通知
        # 格式: {device_id: last_notification_time}
        self.notified_expired_devices = {}  # 已过期设备的通知记录
        self.notified_expiring_devices = {}  # 即将过期设备的通知记录

        # 记录最近打印的日志，避免短时间内重复打印相同信息
        # 格式: {device_id+message: timestamp}
        self.recent_logs = {}
        self.log_dedup_interval = 3600  # 日志去重间隔（单位：秒），默认1小时

        # 记录上次检查时间
        self.last_check_time = 0
        self.min_check_interval = 43200  # 最小检查间隔（单位：秒），默认12小时

    def start(self):
        """启动设备过期检查任务"""
        if self.thread is None or not self.thread.is_alive():
            self.running = True
            self.stop_event.clear()  # 确保Event是未设置状态
            self.thread = threading.Thread(target=self._run_check_loop)
            self.thread.daemon = True
            self.thread.start()
            #self.logger.info("设备过期检查任务已启动，将每24小时自动检查一次设备过期状态")

    def stop(self):
        """停止设备过期检查任务"""
        if not self.running:
            return  # 已经停止，避免重复操作

        self.logger.info("正在停止设备过期检查任务...")
        self.running = False
        self.stop_event.set()  # 设置Event以唤醒等待中的线程

        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=8)  # 增加超时时间
            if self.thread.is_alive():
                self.logger.warning("设备过期检查线程未能在超时时间内结束")
            else:
                self.logger.info("设备过期检查线程已成功结束")

        self.logger.info("设备过期检查任务已停止")

    def _run_check_loop(self):
        """运行检查循环"""
        # 不在启动时立即检查，只按24小时间隔定时检查
        self.logger.info("设备过期检查器已启动，将每24小时执行一次检查")

        while self.running:
            try:
                # 使用Event等待，而不是循环sleep，以降低CPU消耗
                # 等待 check_interval 秒，或者直到 stop() 被调用
                is_stopped = self.stop_event.wait(timeout=self.check_interval)

                if is_stopped or not self.running:
                    break  # 如果事件被设置或running为False，则退出循环

                # 检查距离上次检查的时间是否足够
                current_time = time.time()
                time_since_last_check = current_time - self.last_check_time

                if time_since_last_check < self.min_check_interval:
                    # 距离上次检查时间不足最小间隔，记录跳过信息并继续等待
                    remaining_minutes = int((self.min_check_interval - time_since_last_check) / 60)
                    elapsed_minutes = int(time_since_last_check / 60)
                    min_interval_minutes = int(self.min_check_interval / 60)

                    self.logger.info(f"距离上次设备过期检查仅 {elapsed_minutes} 分钟，"
                                   f"未达最小间隔 {min_interval_minutes} 分钟，"
                                   f"跳过本次检查，将在 {remaining_minutes} 分钟后再次检查")
                    continue

                # 执行设备过期检查
                self._check_devices()
            except Exception as e:
                self.logger.error(f"设备过期检查任务出错: {str(e)}")
                time.sleep(60)  # 出错后等待一分钟再继续

    def _check_devices(self):
        """检查所有设备的过期状态"""
        try:
            self.last_check_time = time.time()

            self.logger.info("开始检查设备过期状态...")

            # 从数据库获取设备列表（统一使用数据库模式）
            data_manager = self.config_manager.get_data_manager()
            device_list = data_manager.get_devices()

            # 获取当前日期
            today = datetime.now().date()

            # 统计信息
            expired_devices = []
            soon_expire_devices = []

            # 用于跟踪已处理的设备ID，避免重复
            processed_device_ids = set()

            for device in device_list:
                device_id = device.get('id')

                # 避免重复处理同一设备
                if device_id in processed_device_ids:
                    continue

                processed_device_ids.add(device_id)
                device_name = device.get('name', device_id)
                expire_date_str = device.get('expire_date')

                # 如果没有设置过期时间，则跳过
                if not expire_date_str:
                    continue

                try:
                    # 解析过期日期
                    expire_date = datetime.strptime(expire_date_str, "%Y-%m-%d").date()

                    # 检查是否已过期
                    if expire_date <= today:
                        self.logger.warning(f"设备 {device_name} (ID: {device_id}) 已过期")
                        expired_devices.append(device)
                    else:
                        # 计算剩余天数
                        days_left = (expire_date - today).days

                        # 检查是否即将过期
                        if days_left <= self.reminder_days:
                            self.logger.info(f"设备 {device_name} (ID: {device_id}) 即将过期，剩余 {days_left} 天")
                            soon_expire_devices.append((device, days_left))
                except ValueError:
                    self.logger.error(f"设备 {device_name} 的过期日期格式无效: {expire_date_str}")

            # 处理已过期的设备
            self._handle_expired_devices(expired_devices)

            # 处理即将过期的设备
            if soon_expire_devices:
                self._handle_soon_expire_devices(soon_expire_devices)

            self.logger.info(f"设备过期检查完成: {len(expired_devices)} 个已过期, {len(soon_expire_devices)} 个即将过期")

        except Exception as e:
            self.logger.error(f"检查设备过期状态时出错: {str(e)}")

    def _handle_expired_devices(self, expired_devices):
        """处理已过期的设备"""
        data_manager = self.config_manager.get_data_manager()

        # 对每个过期设备，发送通知并从所有房源的设备列表中移除
        for device in expired_devices:
            device_id = device.get('id')
            device_name = device.get('name', device_id)

            self.logger.info(f"处理过期设备 {device_name} (ID: {device_id})")

            # 1. 发送过期通知 (无条件)
            # _send_expired_notification 方法不使用 affected_houses 参数，因此传递空列表是安全的
            self._send_expired_notification(device, [])

            # 2. 从房源中移除设备
            try:
                monitor_configs = data_manager.get_monitor_configs()
                for house_config in monitor_configs:
                    house_name = house_config.get('name', '未命名房源')
                    if device_id in house_config.get('device_ids', []):
                        # 从设备列表中移除
                        house_config['device_ids'].remove(device_id)
                        data_manager.save_monitor_config(house_config)
                        self.logger.info(f"已从房源 {house_name} 中移除过期设备 {device_name}")
            except Exception as e:
                self.logger.error(f"从房源中移除过期设备 {device_name} 失败: {str(e)}")

    def _handle_soon_expire_devices(self, soon_expire_devices):
        """处理即将过期的设备"""
        # 使用字典去重，确保每个设备只处理一次（按设备ID）
        # 如果同一设备有多条记录，保留剩余天数最少的那条
        device_map = {}
        for device, days_left in soon_expire_devices:
            device_id = device.get('id')
            if device_id not in device_map or days_left < device_map[device_id][1]:
                device_map[device_id] = (device, days_left)

        # 对去重后的设备发送提醒
        for device, days_left in device_map.values():
            self._send_expiry_reminder(device, days_left)

    def _send_expired_notification(self, device, affected_houses):
        """发送设备过期通知"""
        device_id = device.get('id')
        device_name = device.get('name', device_id)

        # 检查是否需要发送通知（避免频繁通知）
        current_time = time.time()
        last_notification_time = self.notified_expired_devices.get(device_id, 0)
        time_since_last_notification = current_time - last_notification_time

        # 如果距离上次通知的时间小于通知间隔，则跳过本次通知
        if time_since_last_notification < self.notification_interval:
            # 构建日志消息，但避免频繁记录
            log_key = f"expired_{device_id}"
            if log_key not in self.recent_logs or (current_time - self.recent_logs[log_key]) > self.log_dedup_interval:
                remaining_hours = int((self.notification_interval - time_since_last_notification) / 3600)
                self.logger.info(f"设备 {device_name} (ID: {device_id}) 已过期通知仍在冷却中，将在 {remaining_hours} 小时后可再次发送")
                self.recent_logs[log_key] = current_time
            return

        # 构建发送给设备本身的通知内容
        device_title = "青城住房监控服务已过期"
        current_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        device_content = (f"您的设备已过期，监控服务已自动停止\n"
                         f"如需继续使用，请联系青城住房监控管理员进行续时间\n"
                         f"未租到房子可以免费续时间，未进行主动续时间默认你租到房子，将取消监控\n"
                         f"设备名称: {device_name}\n"
                         f"过期时间: {device.get('expire_date')}\n"
                         f"时间: {current_time_str}")

        # 发送通知给设备本身
        success = self._send_notification(device_id, device_title, device_content)

        # 如果发送成功，记录通知时间
        if success:
            self.notified_expired_devices[device_id] = current_time
            self.logger.info(f"已向设备 {device_name} 发送过期通知")
        else:
            self.logger.warning(f"向设备 {device_name} 发送过期通知失败")

    def _send_expiry_reminder(self, device, days_left):
        """发送设备即将过期的提醒"""
        device_id = device.get('id')
        device_name = device.get('name', device_id)

        # 检查是否需要发送通知（避免频繁通知）
        current_time = time.time()
        last_notification_time = self.notified_expiring_devices.get(device_id, 0)
        time_since_last_notification = current_time - last_notification_time

        # 如果距离上次通知的时间小于通知间隔，则跳过本次通知
        if time_since_last_notification < self.notification_interval:
            # 构建日志消息，但避免频繁记录
            log_key = f"expiring_{device_id}_{days_left}"
            if log_key not in self.recent_logs or (current_time - self.recent_logs[log_key]) > self.log_dedup_interval:
                remaining_hours = int((self.notification_interval - time_since_last_notification) / 3600)
                self.logger.info(f"设备 {device_name} (ID: {device_id}) 即将过期通知仍在冷却中，将在 {remaining_hours} 小时后可再次发送")
                self.recent_logs[log_key] = current_time
            return

        # 直接使用即将过期的设备ID发送通知，而不是通知管理员
        # 构建通知内容
        title = "青城住房监控即将过期提醒"
        current_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        content = (f"您的设备即将过期，请及时联系青城住房监控管理员进行续时间！未租到房子免费续时间\n"
                  f"默认不主动进行续时间就代表您已经租到房子了，过期后将自动停止监控\n"
                  f"设备名称: {device_name}\n"
                  f"过期时间: {device.get('expire_date')}\n"
                  f"剩余天数: {days_left}天\n"
                  f"时间: {current_time_str}")

        # 直接发送通知给设备本身
        success = self._send_notification(device_id, title, content)

        # 如果发送成功，记录通知时间
        if success:
            self.notified_expiring_devices[device_id] = current_time
            self.logger.info(f"已向设备 {device_name} (ID: {device_id}) 发送过期提醒，剩余 {days_left} 天")
        else:
            self.logger.warning(f"向设备 {device_name} (ID: {device_id}) 发送过期提醒失败，剩余 {days_left} 天")

    def _send_notification(self, device_id, title, content):
        """
        发送设备过期通知到指定设备

        参数:
            device_id: 目标设备ID
            title: 通知标题
            content: 通知内容

        返回:
            bool: 推送是否成功
        """
        try:
            # 检查通知服务是否可用
            if self.notification_service is None:
                self.logger.error(f"通知服务未初始化，无法向设备 {device_id} 发送通知")
                return False

            # 检查设备检查器是否正在关闭
            if not self.running:
                self.logger.debug(f"设备检查器正在关闭，跳过向设备 {device_id} 发送通知")
                return False

            # 通过数据管理器查找设备
            data_manager = self.config_manager.get_data_manager()
            device = data_manager.get_device(device_id)

            if not device:
                self.logger.error(f"发送通知失败: 未找到设备ID {device_id}")
                return False

            # 再次检查运行状态（防止在查找设备期间状态改变）
            if not self.running:
                self.logger.debug(f"设备检查器正在关闭，跳过向设备 {device_id} 发送通知")
                return False

            # 使用新的、安全的方式来调用异步通知服务
            from services.notification.client import schedule_async_task
            coro = self.notification_service.send(device, title, content)
            future = schedule_async_task(coro)

            # 等待结果。这是一个从同步线程到异步事件循环的桥梁。
            # future.result() 会阻塞直到任务完成并返回结果。
            return future.result(timeout=30)  # 设置30秒超时

        except Exception as e:
            # 在关闭过程中可能出现的异常，区分处理
            error_msg = str(e).lower()
            if any(keyword in error_msg for keyword in [
                'event loop', '后台事件循环不可用', 'runtime error'
            ]):
                self.logger.debug(f"发送通知时出现预期异常（设备检查器关闭中）: {e}")
            else:
                self.logger.error(f"发送通知时发生未处理的异常: {str(e)}")
            return False

    def _get_expired_devices(self):
        """获取过期的设备列表"""
        expired_devices = []

        try:
            # 从数据库获取设备列表
            data_manager = self.config_manager.get_data_manager()
            devices = data_manager.get_devices()

            current_time = datetime.now()

            for device in devices:
                expire_time_str = device.get('expire_time')
                if expire_time_str:
                    try:
                        expire_time = datetime.strptime(expire_time_str, '%Y-%m-%d %H:%M:%S')
                        if current_time > expire_time:
                            expired_devices.append(device)
                            self.logger.info(f"发现过期设备: {device.get('name', device.get('id'))} (过期时间: {expire_time_str})")
                    except ValueError:
                        self.logger.warning(f"设备 {device.get('id')} 的过期时间格式错误: {expire_time_str}")

        except Exception as e:
            self.logger.error(f"获取过期设备列表失败: {str(e)}")

        return expired_devices

    def manual_check_devices(self):
        """手动检测设备过期状态并发送通知

        Returns:
            dict: 检测结果统计
        """
        try:
            self.logger.info("开始手动检测设备过期状态...")

            # 从数据库获取设备列表
            data_manager = self.config_manager.get_data_manager()
            device_list = data_manager.get_devices()

            # 获取当前日期
            today = datetime.now().date()

            # 统计信息
            expired_devices = []
            soon_expire_devices = []
            notifications_sent = []
            notification_errors = []

            # 用于跟踪已处理的设备ID，避免重复
            processed_device_ids = set()

            for device in device_list:
                device_id = device.get('id')

                # 避免重复处理同一设备
                if device_id in processed_device_ids:
                    continue

                processed_device_ids.add(device_id)
                device_name = device.get('name', device_id)
                expire_date_str = device.get('expire_date')

                # 如果没有设置过期时间，则跳过
                if not expire_date_str:
                    continue

                try:
                    # 解析过期日期
                    expire_date = datetime.strptime(expire_date_str, "%Y-%m-%d").date()

                    # 检查是否已过期
                    if expire_date <= today:
                        self.logger.warning(f"手动检测发现过期设备: {device_name} (ID: {device_id})")
                        expired_devices.append(device)

                        # 发送过期通知（忽略冷却时间限制）
                        try:
                            # 临时重置通知冷却，确保手动检测能发送通知
                            original_time = self.notified_expired_devices.get(device_id, 0)
                            self.notified_expired_devices[device_id] = 0

                            # 发送通知
                            self._send_expired_notification(device, [])
                            notifications_sent.append({
                                'device_id': device_id,
                                'device_name': device_name,
                                'type': 'expired',
                                'expire_date': expire_date_str
                            })

                            # 恢复原始通知时间（如果通知失败）
                            # 如果成功，则会被_send_expired_notification更新
                        except Exception as e:
                            self.logger.error(f"发送过期通知失败 {device_name}: {str(e)}")
                            notification_errors.append({
                                'device_id': device_id,
                                'device_name': device_name,
                                'error': str(e)
                            })
                            # 恢复原始通知时间
                            self.notified_expired_devices[device_id] = original_time
                    else:
                        # 计算剩余天数
                        days_left = (expire_date - today).days

                        # 检查是否即将过期
                        if days_left <= self.reminder_days:
                            self.logger.info(f"手动检测发现即将过期设备: {device_name} (ID: {device_id}), 剩余 {days_left} 天")
                            soon_expire_devices.append((device, days_left))

                            # 发送即将过期通知（忽略冷却时间限制）
                            try:
                                # 临时重置通知冷却
                                original_time = self.notified_expiring_devices.get(device_id, 0)
                                self.notified_expiring_devices[device_id] = 0

                                # 发送通知
                                self._send_expiry_reminder(device, days_left)
                                notifications_sent.append({
                                    'device_id': device_id,
                                    'device_name': device_name,
                                    'type': 'expiring',
                                    'expire_date': expire_date_str,
                                    'days_left': days_left
                                })
                            except Exception as e:
                                self.logger.error(f"发送即将过期通知失败 {device_name}: {str(e)}")
                                notification_errors.append({
                                    'device_id': device_id,
                                    'device_name': device_name,
                                    'error': str(e)
                                })
                                # 恢复原始通知时间
                                self.notified_expiring_devices[device_id] = original_time

                except ValueError:
                    self.logger.error(f"设备 {device_name} 的过期日期格式无效: {expire_date_str}")

            # 构建结果统计
            result = {
                'total_devices_checked': len(processed_device_ids),
                'expired_count': len(expired_devices),
                'expiring_count': len(soon_expire_devices),
                'notifications_sent': len(notifications_sent),
                'notification_errors': len(notification_errors),
                'expired_devices': [{'id': d.get('id'), 'name': d.get('name'), 'expire_date': d.get('expire_date')} for d in expired_devices],
                'expiring_devices': [{'id': d[0].get('id'), 'name': d[0].get('name'), 'expire_date': d[0].get('expire_date'), 'days_left': d[1]} for d in soon_expire_devices],
                'notifications': notifications_sent,
                'errors': notification_errors
            }

            self.logger.info(f"手动设备过期检查完成: {len(expired_devices)} 个已过期, {len(soon_expire_devices)} 个即将过期, {len(notifications_sent)} 条通知已发送")

            return result

        except Exception as e:
            self.logger.error(f"手动检查设备过期状态时出错: {str(e)}")
            return {
                'error': str(e),
                'total_devices_checked': 0,
                'expired_count': 0,
                'expiring_count': 0,
                'notifications_sent': 0,
                'notification_errors': 0
            }