"""
调度API模块 - 统一配置版本
"""

from flask import Blueprint, request, jsonify, current_app
from web.utils.decorators import login_required


schedule_api = Blueprint('schedule_api', __name__)


@schedule_api.route('/status', methods=['GET'])
@login_required
def get_schedule_status():
    """获取调度状态"""
    try:
        # 从统一配置获取调度配置
        unified_config = current_app.unified_config
        schedule_config = unified_config.get('schedule', {})

        # 获取调度器状态
        scheduler = getattr(current_app, 'scheduler', None)
        scheduler_running = scheduler.running if scheduler else False

        response = {
            'config': schedule_config,
            'scheduler_running': scheduler_running
        }

        return jsonify(response)

    except Exception as e:
        current_app.logger.error(f"获取调度状态失败: {e}")
        return jsonify({'error': '获取调度状态失败', 'details': str(e)}), 500


@schedule_api.route('/update', methods=['POST'])
@login_required
def update_schedule():
    """更新调度配置"""
    try:
        schedule_data = request.json
        if not schedule_data:
            return jsonify({'error': '调度配置数据不能为空'}), 400

        # 验证调度配置格式
        if 'tasks' not in schedule_data:
            return jsonify({'error': '调度配置必须包含tasks字段'}), 400

        unified_config = current_app.unified_config

        # 保存调度配置到用户配置文件
        config_update = {'schedule': schedule_data}
        unified_config.save_user_config(config_update)

        # 更新应用中的调度器
        scheduler = getattr(current_app, 'scheduler', None)
        if scheduler:
            # 重新加载调度配置
            monitor_action = getattr(current_app, 'monitor_action', None)
            if monitor_action:
                scheduler.from_dict(schedule_data, monitor_action)
                current_app.logger.info("调度配置已更新并重新加载")
            else:
                current_app.logger.warning("monitor_action未找到，调度配置仅保存未重新加载")
        else:
            current_app.logger.warning("调度器未找到，调度配置仅保存")

        return jsonify({'message': '调度配置更新成功'})

    except Exception as e:
        current_app.logger.error(f"更新调度配置失败: {e}")
        return jsonify({'error': '更新调度配置失败', 'details': str(e)}), 500


@schedule_api.route('/start', methods=['POST'])
@login_required
def start_scheduler():
    """启动调度器"""
    try:
        scheduler = getattr(current_app, 'scheduler', None)
        if not scheduler:
            return jsonify({'error': '调度器未初始化'}), 500

        if scheduler.running:
            return jsonify({'message': '调度器已在运行'})

        scheduler.start()
        current_app.logger.info("调度器已启动")
        return jsonify({'message': '调度器启动成功'})

    except Exception as e:
        current_app.logger.error(f"启动调度器失败: {e}")
        return jsonify({'error': '启动调度器失败', 'details': str(e)}), 500


@schedule_api.route('/stop', methods=['POST'])
@login_required
def stop_scheduler():
    """停止调度器"""
    try:
        scheduler = getattr(current_app, 'scheduler', None)
        if not scheduler:
            return jsonify({'error': '调度器未初始化'}), 500

        if not scheduler.running:
            return jsonify({'message': '调度器已停止'})

        scheduler.stop()
        current_app.logger.info("调度器已停止")
        return jsonify({'message': '调度器停止成功'})

    except Exception as e:
        current_app.logger.error(f"停止调度器失败: {e}")
        return jsonify({'error': '停止调度器失败', 'details': str(e)}), 500