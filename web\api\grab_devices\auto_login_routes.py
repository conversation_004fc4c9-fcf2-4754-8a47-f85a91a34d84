"""
自动登录相关路由模块
"""

import json
from datetime import datetime
from flask import jsonify, request, current_app, Blueprint
from web.utils.decorators import login_required

# 创建自动登录路由蓝图
auto_login_routes_bp = Blueprint('auto_login_routes', __name__)

# 延迟导入函数以避免循环依赖
def get_auto_login_manager():
    """获取自动登录管理器实例"""
    from . import get_auto_login_manager as _get_manager
    return _get_manager()

def initialize_auto_login_manager():
    """初始化自动登录管理器"""
    from . import initialize_auto_login_manager as _init_manager
    return _init_manager()

# 检查OCR依赖（为auto_login路由使用）
try:
    from .login_helper import TESSERACT_AVAILABLE, PIL_AVAILABLE
except ImportError:
    TESSERACT_AVAILABLE = False
    PIL_AVAILABLE = False


# ==================== 智能自动登录接口 ====================

@auto_login_routes_bp.route('/grab_devices/<int:device_id>/auto_login', methods=['POST'])
@login_required
def auto_login_grab_device(device_id):
    """智能自动化登录抢房设备 - 使用持续验证码识别"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        phone = device.get('phone')
        if not phone:
            return jsonify({'error': '设备未配置手机号'}), 400

        # 检查OCR依赖
        if not TESSERACT_AVAILABLE or not PIL_AVAILABLE:
            return jsonify({
                'error': 'OCR功能不可用，无法进行自动登录',
                'details': '请确保已安装pytesseract和PIL库'
            }), 400

        # 创建登录助手并初始化代理
        from .login_helper import HuhhothomeLoginHelper
        login_helper = HuhhothomeLoginHelper()

        # 初始化代理管理器（一次性）
        proxy_initialized = login_helper.initialize_proxy_once()
        current_app.logger.info(f"代理初始化结果: {'成功' if proxy_initialized else '跳过或失败'}")

        # 步骤1: 使用持续验证码识别
        current_app.logger.info(f"开始自动登录设备 {device.get('username', '未知')}")

        # 获取最大尝试次数（可以从请求参数中设置）
        max_attempts = request.json.get('max_attempts', 30) if request.json else 30

        # 使用持续验证码识别方法
        recognition_result = login_helper.persistent_captcha_recognition(phone, max_attempts)

        # 检查代理状态
        proxy_enabled = False
        try:
            proxy_enabled = login_helper.proxy_manager is not None and login_helper.proxy_manager.current_proxy is not None
        except:
            pass

        if recognition_result['success']:
            current_app.logger.info(f"✅ 智能自动登录第一步成功")

            # 返回详细的成功信息
            return jsonify({
                'status': 'sms_sent',
                'message': recognition_result['message'],
                'img_code': recognition_result['code'],
                'image': recognition_result['image'],
                'attempt_count': recognition_result['attempt_count'],
                'elapsed_time': recognition_result['elapsed_time'],
                'stats': recognition_result['stats'],
                'recognition_quality': 'high',  # 持续识别的质量更高
                'proxy_enabled': proxy_enabled
            })
        else:
            current_app.logger.error(f"❌ 智能自动登录失败: {recognition_result['message']}")

            # 返回详细的失败信息
            return jsonify({
                'error': '持续验证码识别失败',
                'message': recognition_result['message'],
                'attempt_count': recognition_result['attempt_count'],
                'elapsed_time': recognition_result['elapsed_time'],
                'stats': recognition_result['stats'],
                'details': '多次尝试后仍无法成功识别验证码并发送短信',
                'suggestion': '请检查网络连接或稍后重试',
                'proxy_enabled': proxy_enabled
            }), 400

    except Exception as e:
        current_app.logger.error(f"智能自动登录失败: {str(e)}")
        import traceback
        current_app.logger.error(f"详细错误信息: {traceback.format_exc()}")
        return jsonify({'error': f'自动登录过程中发生异常: {str(e)}'}), 500


@auto_login_routes_bp.route('/grab_devices/<int:device_id>/complete_auto_login', methods=['POST'])
@login_required
def complete_auto_login(device_id):
    """完成自动登录（输入短信验证码后）"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        login_data = request.json
        phone = device.get('phone')
        sms_code = login_data.get('sms_code')
        img_code = login_data.get('img_code')

        if not phone or not sms_code or not img_code:
            return jsonify({'error': '缺少必要的登录信息'}), 400

        # 执行登录（复用前一步的代理配置，无需重新初始化代理）
        from .login_helper import HuhhothomeLoginHelper
        login_helper = HuhhothomeLoginHelper()
        success, access_token, sessioncode, cookies, message = login_helper.login(phone, sms_code, img_code)

        if success:
            # 解析JWT token获取过期时间
            from datetime import datetime
            import json

            expires_at = None
            cookie_remaining_hours = 0

            if access_token:
                jwt_payload = login_helper.parse_jwt_token(access_token)
                if jwt_payload and 'exp' in jwt_payload:
                    expires_at = datetime.fromtimestamp(jwt_payload['exp'])
                    # 计算剩余小时数
                    remaining = expires_at - datetime.now()
                    cookie_remaining_hours = max(0, remaining.total_seconds() / 3600)
                    current_app.logger.info(f"设备 {device['username']} 自动登录成功，Cookie剩余有效期: {cookie_remaining_hours:.2f} 小时")
                else:
                    current_app.logger.warning(f"设备 {device['username']} JWT token解析失败或缺少exp字段")

            # 更新设备信息
            device_update = {
                'id': device_id,
                'username': device['username'],
                'phone': device['phone'],
                'target_estate': device.get('target_estate', ''),
                'house_id': device.get('house_id', ''),
                'enabled': device.get('enabled', True),
                'conditions': device.get('conditions', []),
                'access_token': access_token,
                'sessioncode': sessioncode,
                'cookie': json.dumps(cookies),
                'cookie_expires_at': expires_at.isoformat() if expires_at else None,
                'last_login_at': datetime.now().isoformat()
            }

            updated_device = grab_device_model.create_or_update(device_update)

            # 计算并添加剩余有效期到返回数据
            updated_device['cookie_remaining_hours'] = cookie_remaining_hours

            current_app.logger.info(f"抢房设备 {device['username']} 自动登录成功")

            # 检查代理状态
            proxy_enabled = False
            try:
                proxy_enabled = login_helper.proxy_manager is not None and login_helper.proxy_manager.current_proxy is not None
            except:
                pass

            return jsonify({
                'status': 'success',
                'message': '自动登录成功',
                'device': updated_device,
                'proxy_enabled': proxy_enabled
            })
        else:
            return jsonify({'error': f'登录失败: {message}'}), 400

    except Exception as e:
        current_app.logger.error(f"完成自动登录失败: {str(e)}")
        import traceback
        current_app.logger.error(f"详细错误信息: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500


# ==================== 自动Cookie延长管理接口 ====================

@auto_login_routes_bp.route('/grab_devices/auto_login/start', methods=['POST'])
@login_required
def start_auto_login_manager():
    """启动自动Cookie延长管理器"""
    try:
        manager = get_auto_login_manager()
        if not manager:
            return jsonify({'error': '无法创建自动登录管理器'}), 500

        success = manager.start()
        if success:
            return jsonify({
                'status': 'success',
                'message': '自动Cookie延长管理器已启动',
                'manager_status': manager.get_status()
            })
        else:
            return jsonify({
                'status': 'warning',
                'message': '自动Cookie延长管理器已在运行中',
                'manager_status': manager.get_status()
            })

    except Exception as e:
        current_app.logger.error(f"启动自动登录管理器失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@auto_login_routes_bp.route('/grab_devices/auto_login/stop', methods=['POST'])
@login_required
def stop_auto_login_manager():
    """停止自动Cookie延长管理器"""
    try:
        manager = get_auto_login_manager()
        if not manager:
            return jsonify({'error': '自动登录管理器不存在'}), 404

        success = manager.stop()
        if success:
            return jsonify({
                'status': 'success',
                'message': '自动Cookie延长管理器已停止'
            })
        else:
            return jsonify({
                'status': 'warning',
                'message': '自动Cookie延长管理器未在运行'
            })

    except Exception as e:
        current_app.logger.error(f"停止自动登录管理器失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@auto_login_routes_bp.route('/grab_devices/auto_login/status', methods=['GET'])
@login_required
def get_auto_login_status():
    """获取自动Cookie延长管理器状态"""
    try:
        manager = get_auto_login_manager()
        if not manager:
            return jsonify({
                'status': 'not_created',
                'message': '自动登录管理器未创建'
            })

        return jsonify({
            'status': 'success',
            'manager_status': manager.get_status()
        })

    except Exception as e:
        current_app.logger.error(f"获取自动登录管理器状态失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@auto_login_routes_bp.route('/grab_devices/<int:device_id>/auto_login/history', methods=['GET'])
@login_required
def get_device_auto_login_history(device_id):
    """获取设备的自动登录历史"""
    try:
        manager = get_auto_login_manager()
        if not manager:
            return jsonify({'history': []})

        history = manager.get_auto_login_history(device_id)
        return jsonify({
            'status': 'success',
            'history': history
        })

    except Exception as e:
        current_app.logger.error(f"获取设备自动登录历史失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


# ==================== 管理和调试接口 ====================

@auto_login_routes_bp.route('/grab_devices/auto_login/pending', methods=['GET'])
@login_required
def get_pending_sms_devices():
    """获取等待处理的设备列表（已移除短信验证码支持）"""
    try:
        manager = get_auto_login_manager()
        if not manager:
            return jsonify({'pending_devices': {}})

        pending_devices = manager.get_pending_devices()
        return jsonify({
            'status': 'success',
            'pending_devices': pending_devices,
            'message': '已移除短信验证码支持，返回空列表'
        })

    except Exception as e:
        current_app.logger.error(f"获取等待设备列表失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@auto_login_routes_bp.route('/grab_devices/auto_login/initialize', methods=['POST'])
@login_required
def manual_initialize_auto_login():
    """手动初始化自动登录管理器"""
    try:
        success = initialize_auto_login_manager()

        if success:
            return jsonify({
                'status': 'success',
                'message': '自动登录管理器初始化成功'
            })
        else:
            return jsonify({
                'status': 'failed',
                'message': '自动登录管理器初始化失败'
            })

    except Exception as e:
        current_app.logger.error(f"手动初始化自动登录管理器失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@auto_login_routes_bp.route('/grab_devices/<int:device_id>/debug_cookie', methods=['GET'])
@login_required
def debug_device_cookie(device_id):
    """调试设备Cookie状态"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到设备ID: {device_id}'}), 404

        from .login_helper import HuhhothomeLoginHelper
        login_helper = HuhhothomeLoginHelper()

        access_token = device.get('access_token')
        debug_info = {
            'device_id': device_id,
            'username': device.get('username'),
            'phone': device.get('phone'),
            'has_access_token': bool(access_token),
            'has_sessioncode': bool(device.get('sessioncode')),
            'cookie_expires_at': device.get('cookie_expires_at'),
            'last_login_at': device.get('last_login_at')
        }

        if access_token:
            # 解析JWT token
            jwt_payload = login_helper.parse_jwt_token(access_token)
            debug_info['jwt_payload'] = jwt_payload

            # 计算剩余时间
            remaining_hours = login_helper.get_cookie_remaining_hours(access_token)
            debug_info['cookie_remaining_hours'] = remaining_hours
            debug_info['cookie_remaining_minutes'] = remaining_hours * 60 if remaining_hours else 0

            # 判断是否需要延长
            if remaining_hours:
                debug_info['needs_extend'] = remaining_hours * 60 <= 15  # 15分钟阈值
            else:
                debug_info['needs_extend'] = True

        return jsonify({
            'status': 'success',
            'debug_info': debug_info
        })

    except Exception as e:
        current_app.logger.error(f"调试设备Cookie失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@auto_login_routes_bp.route('/grab_devices/<int:device_id>/force_auto_login', methods=['POST'])
@login_required
def force_device_auto_login(device_id):
    """强制触发设备自动Cookie延长"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到设备ID: {device_id}'}), 404

        manager = get_auto_login_manager()
        if not manager:
            return jsonify({'error': '自动登录管理器不可用'}), 500

        # 强制触发Cookie延长
        current_app.logger.info(f"手动强制触发设备 {device['username']} 的Cookie延长")

        # 调用内部方法
        success = manager._trigger_auto_extend(device)

        if success:
            return jsonify({
                'status': 'success',
                'message': f'设备 {device["username"]} Cookie延长成功'
            })
        else:
            return jsonify({
                'status': 'failed',
                'message': f'设备 {device["username"]} Cookie延长失败，请检查日志获取详细信息'
            })

    except Exception as e:
        current_app.logger.error(f"强制触发设备自动登录失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@auto_login_routes_bp.route('/grab_devices/debug_all_cookies', methods=['GET'])
@login_required
def debug_all_device_cookies():
    """调试所有设备的Cookie状态"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        devices = grab_device_model.get_all()
        enabled_devices = [d for d in devices if d.get('enabled')]

        from .login_helper import HuhhothomeLoginHelper
        login_helper = HuhhothomeLoginHelper()

        debug_summary = {
            'total_devices': len(devices),
            'enabled_devices': len(enabled_devices),
            'devices_with_tokens': 0,
            'devices_need_extend': 0,
            'devices_expired': 0,
            'device_details': []
        }

        for device in enabled_devices:
            device_info = {
                'id': device['id'],
                'username': device.get('username'),
                'phone': device.get('phone'),
                'has_access_token': bool(device.get('access_token')),
                'has_sessioncode': bool(device.get('sessioncode'))
            }

            access_token = device.get('access_token')
            if access_token:
                debug_summary['devices_with_tokens'] += 1

                remaining_hours = login_helper.get_cookie_remaining_hours(access_token)
                device_info['cookie_remaining_hours'] = remaining_hours

                if remaining_hours is not None:
                    remaining_minutes = remaining_hours * 60
                    device_info['cookie_remaining_minutes'] = remaining_minutes

                    if remaining_minutes <= 0:
                        debug_summary['devices_expired'] += 1
                        device_info['status'] = 'expired'
                    elif remaining_minutes <= 15:
                        debug_summary['devices_need_extend'] += 1
                        device_info['status'] = 'needs_extend'
                    else:
                        device_info['status'] = 'normal'
                else:
                    device_info['status'] = 'parse_failed'
            else:
                device_info['status'] = 'no_token'

            debug_summary['device_details'].append(device_info)

        return jsonify({
            'status': 'success',
            'debug_summary': debug_summary
        })

    except Exception as e:
        current_app.logger.error(f"调试所有设备Cookie失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@auto_login_routes_bp.route('/grab_devices/auto_login/check_now', methods=['POST'])
@login_required
def manual_check_all_devices():
    """手动触发检查所有设备Cookie状态"""
    try:
        manager = get_auto_login_manager()
        if not manager:
            return jsonify({'error': '自动登录管理器不可用'}), 500

        if not manager.running:
            return jsonify({'error': '自动登录管理器未运行'}), 400

        current_app.logger.info("手动触发检查所有设备Cookie状态")

        # 直接调用检查方法
        manager._check_all_devices()

        return jsonify({
            'status': 'success',
            'message': '手动检查已完成，请查看日志获取详细信息'
        })

    except Exception as e:
        current_app.logger.error(f"手动检查设备失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@auto_login_routes_bp.route('/grab_devices/auto_login/test', methods=['GET', 'POST'])
@login_required
def test_auto_login_api():
    """测试自动登录API功能"""
    try:
        manager = get_auto_login_manager()

        test_result = {
            'manager_exists': manager is not None,
            'manager_running': manager.running if manager else False,
            'manager_status': manager.get_status() if manager else None
        }

        return jsonify({
            'status': 'success',
            'test_result': test_result,
            'message': '自动登录API测试完成'
        })

    except Exception as e:
        current_app.logger.error(f"测试自动登录API失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@auto_login_routes_bp.route('/grab_devices/debug/admin_notifier', methods=['GET'])
@login_required
def debug_admin_notifier_config():
    """调试管理员通知配置"""
    try:
        data_manager = current_app.config_manager.get_data_manager()

        # 获取管理员通知配置
        admin_notifier_config = data_manager.get_config('admin_notifier', {})

        # 获取设备型号
        device_model = getattr(data_manager, 'device_model', None)
        admin_devices = []

        if device_model and admin_notifier_config.get('device_ids'):
            for device_id in admin_notifier_config.get('device_ids', []):
                try:
                    device = device_model.get_by_id(device_id)
                    if device:
                        admin_devices.append({
                            'id': device_id,
                            'name': device.get('name', '未知'),
                            'type': device.get('type', '未知'),
                            'enabled': device.get('enabled', False)
                        })
                except Exception:
                    admin_devices.append({
                        'id': device_id,
                        'error': '设备不存在或查询失败'
                    })

        return jsonify({
            'status': 'success',
            'admin_notifier_config': admin_notifier_config,
            'admin_devices': admin_devices
        })

    except Exception as e:
        current_app.logger.error(f"调试管理员通知配置失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@auto_login_routes_bp.route('/grab_devices/debug/admin_notifier', methods=['POST'])
@login_required
def update_admin_notifier_config():
    """更新管理员通知配置"""
    try:
        data_manager = current_app.config_manager.get_data_manager()

        new_config = request.json
        if not new_config:
            return jsonify({'error': '缺少配置数据'}), 400

        # 更新配置
        data_manager.set_config('admin_notifier', new_config)

        return jsonify({
            'status': 'success',
            'message': '管理员通知配置已更新',
            'new_config': new_config
        })

    except Exception as e:
        current_app.logger.error(f"更新管理员通知配置失败: {str(e)}")
        return jsonify({'error': str(e)}), 500