"""配置模块 - 简化版本"""

# 导入基础路径常量
from .settings import PROJECT_ROOT

# 导入配置常量（向后兼容）
from .default_config import (
    DATABASE_FILE,
    LOG_DIR,
    BASE_URL,
    COMMON_PARAMS,
    DEFAULT_PROXY_API_URL,
    DEFAULT_CHECK_INTERVAL,
    DEFAULT_MAX_RETRIES,
    LOG_LEVEL_MAPPING,
    DEFAULT_CONFIG
)

# 导入统一配置管理器
from .unified_config import get_config, get_config_summary, UnifiedConfigManager

__all__ = [
    # 基础常量
    'PROJECT_ROOT',
    'DATABASE_FILE',
    'LOG_DIR',
    'BASE_URL',
    'COMMON_PARAMS',
    'DEFAULT_PROXY_API_URL',
    'DEFAULT_CHECK_INTERVAL',
    'DEFAULT_MAX_RETRIES',
    'LOG_LEVEL_MAPPING',
    'DEFAULT_CONFIG',

    # 统一配置管理器
    'get_config',
    'get_config_summary',
    'UnifiedConfigManager'
]