"""
监控服务客户端 - 封装对独立监控服务的HTTP API调用
"""

import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
import time
import os

from core.ssl_manager import get_ssl_manager


class MonitorServiceClient:
    """监控服务HTTP客户端"""

    def __init__(self, base_url: str = None, logger: logging.Logger = None):
        """
        初始化监控服务客户端

        Args:
            base_url: 监控服务的基础URL，默认从环境变量获取
            logger: 日志记录器
        """
        self.base_url = base_url or os.getenv('MONITOR_SERVICE_URL', 'http://127.0.0.1:8088')
        self.logger = logger or logging.getLogger(__name__)
        self.session: Optional[aiohttp.ClientSession] = None
        self._last_health_check = None
        self._health_check_interval = 30  # 健康检查间隔（秒）

        # 重试配置
        self.max_retries = 3
        self.retry_delay = 1.0  # 基础重试延迟（秒）

        # SSL配置管理器
        try:
            self.ssl_manager = get_ssl_manager()
        except Exception as e:
            self.logger.warning(f"SSL管理器初始化失败，使用默认SSL设置: {e}")
            self.ssl_manager = None
        self.timeout = 10.0  # 请求超时（秒）

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

    async def _ensure_session(self):
        """确保HTTP会话已创建"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)

            # 获取SSL配置
            ssl_context = None
            if self.ssl_manager:
                # 监控客户端主要连接本地监控服务，使用housing_monitor模块配置
                ssl_context = self.ssl_manager.get_aiohttp_ssl_param('housing_monitor', self.base_url)

            # 创建连接器，增加稳定性和SSL配置
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=5,
                ttl_dns_cache=300,
                use_dns_cache=True,
                enable_cleanup_closed=True,
                ssl=ssl_context  # 使用SSL配置
            )
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={'User-Agent': 'WebConsole-MonitorClient/1.0'}
            )

    async def close(self):
        """关闭HTTP会话"""
        if self.session and not self.session.closed:
            try:
                await self.session.close()
            except Exception as e:
                # 忽略关闭时的异常
                pass
            finally:
                self.session = None

    async def _request(self, method: str, endpoint: str, **kwargs) -> Tuple[bool, Dict[str, Any]]:
        """
        执行HTTP请求，包含重试逻辑

        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 传递给aiohttp的额外参数

        Returns:
            (success: bool, response_data: dict)
        """
        await self._ensure_session()

        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"

        for attempt in range(self.max_retries + 1):
            try:
                async with self.session.request(method, url, **kwargs) as response:
                    if response.status == 200:
                        data = await response.json()
                        return True, data
                    else:
                        error_text = await response.text()
                        self.logger.warning(f"监控服务请求失败: {response.status} - {error_text}")

                        # 对于4xx错误，不重试
                        if 400 <= response.status < 500:
                            return False, {
                                'error': f'客户端错误: {response.status}',
                                'details': error_text
                            }

                        # 对于5xx错误，继续重试
                        if attempt < self.max_retries:
                            delay = self.retry_delay * (2 ** attempt)
                            self.logger.info(f"监控服务请求失败，{delay}秒后重试 ({attempt + 1}/{self.max_retries})")
                            await asyncio.sleep(delay)
                        else:
                            return False, {
                                'error': f'服务器错误: {response.status}',
                                'details': error_text
                            }

            except aiohttp.ClientError as e:
                self.logger.warning(f"监控服务连接异常: {str(e)}")
                if attempt < self.max_retries:
                    delay = self.retry_delay * (2 ** attempt)
                    self.logger.info(f"连接失败，{delay}秒后重试 ({attempt + 1}/{self.max_retries})")
                    await asyncio.sleep(delay)
                else:
                    return False, {
                        'error': '连接失败',
                        'details': str(e)
                    }
            except Exception as e:
                self.logger.error(f"监控服务请求异常: {str(e)}")
                return False, {
                    'error': '未知错误',
                    'details': str(e)
                }

        # 这里不应该到达，但为了安全起见
        return False, {'error': '重试次数已用尽'}

    async def health_check(self, force: bool = False) -> bool:
        """
        健康检查

        Args:
            force: 强制检查，忽略缓存

        Returns:
            服务是否健康
        """
        now = datetime.now()

        # 如果最近检查过且服务健康，直接返回结果
        if not force and self._last_health_check:
            if now - self._last_health_check < timedelta(seconds=self._health_check_interval):
                return True

        success, data = await self._request('GET', '/health')

        if success:
            self._last_health_check = now
            self.logger.debug("监控服务健康检查成功")
            return True
        else:
            self._last_health_check = None
            self.logger.warning("监控服务健康检查失败")
            return False

    async def get_status(self) -> Tuple[bool, Dict[str, Any]]:
        """获取监控服务状态"""
        return await self._request('GET', '/status')

    async def start_monitor(self) -> Tuple[bool, Dict[str, Any]]:
        """启动监控"""
        return await self._request('POST', '/start')

    async def stop_monitor(self) -> Tuple[bool, Dict[str, Any]]:
        """停止监控"""
        return await self._request('POST', '/stop')

    async def reload_config(self) -> Tuple[bool, Dict[str, Any]]:
        """重载配置"""
        return await self._request('POST', '/reload-config')

    async def get_proxy_status(self) -> Tuple[bool, Dict[str, Any]]:
        """获取代理状态"""
        return await self._request('GET', '/proxy/status')

    async def refresh_proxy(self) -> Tuple[bool, Dict[str, Any]]:
        """刷新代理"""
        return await self._request('POST', '/proxy/refresh')

    def get_service_url(self) -> str:
        """获取监控服务URL"""
        return self.base_url


# 全局客户端实例，用于在Flask应用中共享
_global_client: Optional[MonitorServiceClient] = None


def get_monitor_client(base_url: str = None, logger: logging.Logger = None) -> MonitorServiceClient:
    """
    获取全局监控服务客户端实例

    Args:
        base_url: 监控服务基础URL
        logger: 日志记录器

    Returns:
        MonitorServiceClient实例
    """
    global _global_client

    if _global_client is None:
        _global_client = MonitorServiceClient(base_url, logger)

    return _global_client


async def close_global_client():
    """关闭全局客户端"""
    global _global_client

    if _global_client:
        await _global_client.close()
        _global_client = None


# 同步包装器，用于在Flask视图中调用
def run_async_request(coro, timeout: float = 30.0):
    """
    在Flask应用中运行异步请求的包装器
    专门处理Flask多线程环境中的事件循环问题

    Args:
        coro: 协程对象
        timeout: 超时时间

    Returns:
        协程的结果
    """
    import concurrent.futures
    import threading

    def run_in_new_thread():
        """在新线程中创建事件循环并运行协程"""
        # 创建新的事件循环
        new_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(new_loop)
        try:
            return new_loop.run_until_complete(coro)
        except Exception as e:
            logging.debug(f"协程执行失败: {str(e)}")
            # 对于连接错误，返回失败状态而不是抛出异常
            if "Event loop is closed" in str(e) or "Connection" in str(e):
                return False, {'error': '服务连接失败', 'details': str(e)}
            raise
        finally:
            # 确保事件循环被正确关闭
            try:
                # 先关闭所有任务
                pending = asyncio.all_tasks(new_loop)
                for task in pending:
                    task.cancel()
                if pending:
                    new_loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))

                # 关闭事件循环
                new_loop.close()
            except Exception:
                pass

    try:
        # 在Flask多线程环境中，始终使用新线程运行异步代码
        with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(run_in_new_thread)
            return future.result(timeout=timeout)

    except concurrent.futures.TimeoutError:
        logging.error(f"异步请求超时 ({timeout}秒)")
        raise TimeoutError(f"请求超时: {timeout}秒")
    except Exception as e:
        logging.error(f"运行异步请求时出错: {str(e)}")
        raise


# Flask视图的便利函数
def sync_get_status(logger: logging.Logger = None) -> Tuple[bool, Dict[str, Any]]:
    """同步获取监控状态（Flask视图用）"""
    client = get_monitor_client(logger=logger)
    return run_async_request(client.get_status())


def sync_start_monitor(logger: logging.Logger = None) -> Tuple[bool, Dict[str, Any]]:
    """同步启动监控（Flask视图用）"""
    client = get_monitor_client(logger=logger)
    return run_async_request(client.start_monitor())


def sync_stop_monitor(logger: logging.Logger = None) -> Tuple[bool, Dict[str, Any]]:
    """同步停止监控（Flask视图用）"""
    client = get_monitor_client(logger=logger)
    return run_async_request(client.stop_monitor())


def sync_refresh_proxy(logger: logging.Logger = None) -> Tuple[bool, Dict[str, Any]]:
    """同步刷新代理（Flask视图用）"""
    client = get_monitor_client(logger=logger)
    return run_async_request(client.refresh_proxy())


def sync_health_check(logger: logging.Logger = None) -> bool:
    """同步健康检查（Flask视图用）"""
    client = get_monitor_client(logger=logger)
    return run_async_request(client.health_check())