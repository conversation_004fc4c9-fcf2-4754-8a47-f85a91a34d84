"""动态请求头生成器 - 实现完全随机的浏览器指纹生成"""

import random
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime


class DynamicHeaderGenerator:
    """动态请求头生成器 - 每5分钟生成全新的随机浏览器指纹"""

    def __init__(self, refresh_interval: int = 300):
        self.refresh_interval = refresh_interval  # 5分钟 = 300秒
        self.last_refresh_time = 0
        self.current_template = None

        # 真实浏览器数据池 - 从实际浏览器抓取的数据
        self._init_browser_data_pools()

    def _init_browser_data_pools(self):
        """初始化浏览器数据池"""

        # Chrome版本池 (最近6个月的版本)
        self.chrome_versions = [
            "120.0.0.0", "119.0.0.0", "118.0.0.0", "117.0.0.0", "116.0.0.0", "115.0.0.0"
        ]

        # Firefox版本池
        self.firefox_versions = [
            "121.0", "120.0", "119.0", "118.0", "117.0", "116.0"
        ]

        # Safari版本池
        self.safari_versions = [
            "17.1", "17.0", "16.6", "16.5", "16.4"
        ]

        # Edge版本池
        self.edge_versions = [
            "120.0.0.0", "119.0.0.0", "118.0.0.0", "117.0.0.0"
        ]

        # 操作系统版本池
        self.windows_versions = [
            "Windows NT 10.0; Win64; x64",
            "Windows NT 10.0; WOW64",
            "Windows NT 6.1; Win64; x64",
            "Windows NT 6.1; WOW64"
        ]

        self.mac_versions = [
            "Macintosh; Intel Mac OS X 10_15_7",
            "Macintosh; Intel Mac OS X 10_15_6",
            "Macintosh; Intel Mac OS X 10_14_6",
            "Macintosh; Intel Mac OS X 11_7_10",
            "Macintosh; Intel Mac OS X 12_6_0"
        ]

        self.linux_versions = [
            "X11; Linux x86_64",
            "X11; Linux i686",
            "X11; Ubuntu; Linux x86_64",
            "X11; Ubuntu; Linux i686"
        ]

        # Accept头内容池
        self.accept_values = [
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
        ]

        # Accept-Language池
        self.language_combinations = [
            "zh-CN,zh;q=0.9,en;q=0.8",
            "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
            "zh-CN,zh-Hans;q=0.9",
            "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "zh-CN,zh;q=0.9,ja;q=0.8,en;q=0.7",
            "zh-CN,zh;q=0.8,en-US;q=0.6,en;q=0.4"
        ]

        # Accept-Encoding组合池
        self.encoding_combinations = [
            "gzip, deflate, br",
            "gzip, deflate, br, zstd",
            "gzip, deflate",
            "br, gzip, deflate",
            "deflate, gzip, br"
        ]

        # 可选请求头池
        self.optional_headers = {
            "DNT": ["1", "0"],
            "Cache-Control": ["max-age=0", "no-cache", "no-store"],
            "Pragma": ["no-cache"],
            "Upgrade-Insecure-Requests": ["1"],
            "Sec-Fetch-User": ["?1", "?0"],
            "Sec-Fetch-Site": ["none", "same-origin", "same-site", "cross-site"],
            "Sec-Fetch-Mode": ["navigate", "cors", "no-cors"],
            "Sec-Fetch-Dest": ["document", "empty", "image"]
        }

    def _should_refresh(self) -> bool:
        """检查是否需要刷新模板"""
        current_time = time.time()
        return (current_time - self.last_refresh_time) >= self.refresh_interval

    def _generate_random_user_agent(self) -> Tuple[str, str]:
        """生成随机User-Agent和浏览器类型"""
        browser_choice = random.choice(["chrome", "firefox", "safari", "edge"])

        if browser_choice == "chrome":
            version = random.choice(self.chrome_versions)
            os_choice = random.choice(["windows", "mac", "linux"])

            if os_choice == "windows":
                os_string = random.choice(self.windows_versions)
            elif os_choice == "mac":
                os_string = random.choice(self.mac_versions)
            else:
                os_string = random.choice(self.linux_versions)

            user_agent = f"Mozilla/5.0 ({os_string}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version} Safari/537.36"
            return user_agent, f"Chrome/{version.split('.')[0]}"

        elif browser_choice == "firefox":
            version = random.choice(self.firefox_versions)
            os_choice = random.choice(["windows", "mac", "linux"])

            if os_choice == "windows":
                os_string = random.choice(self.windows_versions)
                user_agent = f"Mozilla/5.0 ({os_string}; rv:{version}) Gecko/20100101 Firefox/{version}"
            elif os_choice == "mac":
                os_string = random.choice(self.mac_versions)
                user_agent = f"Mozilla/5.0 ({os_string}; rv:{version}) Gecko/20100101 Firefox/{version}"
            else:
                os_string = random.choice(self.linux_versions)
                user_agent = f"Mozilla/5.0 ({os_string}; rv:{version}) Gecko/20100101 Firefox/{version}"

            return user_agent, f"Firefox/{version.split('.')[0]}"

        elif browser_choice == "safari":
            version = random.choice(self.safari_versions)
            os_string = random.choice(self.mac_versions)
            webkit_version = "605.1.15"  # Safari常用的WebKit版本

            user_agent = f"Mozilla/5.0 ({os_string}) AppleWebKit/{webkit_version} (KHTML, like Gecko) Version/{version} Safari/{webkit_version}"
            return user_agent, f"Safari/{version.split('.')[0]}"

        else:  # edge
            version = random.choice(self.edge_versions)
            os_choice = random.choice(["windows", "mac"])

            if os_choice == "windows":
                os_string = random.choice(self.windows_versions)
            else:
                os_string = random.choice(self.mac_versions)

            user_agent = f"Mozilla/5.0 ({os_string}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version} Safari/537.36 Edg/{version}"
            return user_agent, f"Edge/{version.split('.')[0]}"

    def _generate_sec_ch_headers(self, browser_type: str, user_agent: str) -> Dict[str, str]:
        """为Chrome系浏览器生成Sec-Ch-*头"""
        headers = {}

        if "Chrome" in browser_type or "Edge" in browser_type:
            # 解析版本号
            if "Chrome" in browser_type:
                version = browser_type.split("/")[1]
                if "Edge" in user_agent:
                    headers["Sec-Ch-Ua"] = f'"Not_A Brand";v="8", "Chromium";v="{version}", "Microsoft Edge";v="{version}"'
                else:
                    headers["Sec-Ch-Ua"] = f'"Not_A Brand";v="8", "Chromium";v="{version}", "Google Chrome";v="{version}"'

            # 随机添加其他Sec-Ch头
            if random.random() < 0.8:  # 80%概率
                headers["Sec-Ch-Ua-Mobile"] = "?0"

            if random.random() < 0.7:  # 70%概率
                if "Windows" in user_agent:
                    headers["Sec-Ch-Ua-Platform"] = '"Windows"'
                elif "Mac" in user_agent:
                    headers["Sec-Ch-Ua-Platform"] = '"macOS"'
                elif "Linux" in user_agent:
                    headers["Sec-Ch-Ua-Platform"] = '"Linux"'

        return headers

    def _generate_new_template(self):
        """生成新的请求头模板"""
        current_time = time.time()

        # 生成随机User-Agent
        user_agent, browser_type = self._generate_random_user_agent()

        # 基础请求头
        template = {
            "User-Agent": user_agent,
            "Accept": random.choice(self.accept_values),
            "Accept-Language": random.choice(self.language_combinations),
            "Accept-Encoding": random.choice(self.encoding_combinations),
            "Connection": "keep-alive"
        }

        # 生成Sec-Ch-*头
        sec_ch_headers = self._generate_sec_ch_headers(browser_type, user_agent)
        template.update(sec_ch_headers)

        # 随机添加可选头 (50%概率添加DNT，30%概率添加Cache-Control等)
        if random.random() < 0.5:
            template["DNT"] = random.choice(self.optional_headers["DNT"])

        if random.random() < 0.3:
            template["Cache-Control"] = random.choice(self.optional_headers["Cache-Control"])

        if random.random() < 0.6:
            template["Upgrade-Insecure-Requests"] = "1"

        # 为Chrome系浏览器添加Sec-Fetch-*头
        if "Chrome" in browser_type or "Edge" in browser_type or "Safari" in browser_type:
            if random.random() < 0.8:
                template["Sec-Fetch-Site"] = random.choice(self.optional_headers["Sec-Fetch-Site"])
            if random.random() < 0.8:
                template["Sec-Fetch-Mode"] = random.choice(self.optional_headers["Sec-Fetch-Mode"])
            if random.random() < 0.8:
                template["Sec-Fetch-Dest"] = random.choice(self.optional_headers["Sec-Fetch-Dest"])
            if random.random() < 0.4:
                template["Sec-Fetch-User"] = random.choice(self.optional_headers["Sec-Fetch-User"])

        # 存储模板和刷新时间
        self.current_template = template
        self.last_refresh_time = current_time

        print(f"[{datetime.now().strftime('%H:%M:%S')}] 动态请求头已刷新 - 浏览器类型: {browser_type}")
        print(f"  User-Agent: {user_agent[:50]}...")

    def get_random_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """获取动态生成的请求头"""
        # 检查是否需要刷新
        if self._should_refresh() or self.current_template is None:
            self._generate_new_template()

        # 复制当前模板
        headers = self.current_template.copy()

        # 每次请求时对部分头进行微调 (保持一致性的同时增加细微变化)

        # 20%概率微调Accept-Language的q值
        if random.random() < 0.2:
            headers["Accept-Language"] = random.choice(self.language_combinations)

        # 10%概率调整Sec-Fetch-Site
        if random.random() < 0.1 and "Sec-Fetch-Site" in headers:
            headers["Sec-Fetch-Site"] = random.choice(self.optional_headers["Sec-Fetch-Site"])

        # 添加自定义头
        if additional_headers:
            headers.update(additional_headers)

        return headers

    def get_current_user_agent(self) -> str:
        """获取当前的User-Agent"""
        if self.current_template is None:
            self._generate_new_template()
        return self.current_template.get("User-Agent", "")

    def force_refresh(self):
        """强制刷新请求头模板"""
        self.last_refresh_time = 0
        self._generate_new_template()

    def get_refresh_info(self) -> Dict[str, any]:
        """获取刷新信息"""
        current_time = time.time()
        time_since_refresh = current_time - self.last_refresh_time
        time_until_next = self.refresh_interval - time_since_refresh

        return {
            "last_refresh": datetime.fromtimestamp(self.last_refresh_time).strftime('%Y-%m-%d %H:%M:%S'),
            "time_since_refresh": int(time_since_refresh),
            "time_until_next": max(0, int(time_until_next)),
            "current_user_agent": self.get_current_user_agent()[:50] + "..." if self.current_template else "未生成"
        }


# 全局实例 - 整个应用共享一个动态生成器
_global_header_generator = None

def get_global_header_generator() -> DynamicHeaderGenerator:
    """获取全局动态请求头生成器"""
    global _global_header_generator
    if _global_header_generator is None:
        _global_header_generator = DynamicHeaderGenerator()
    return _global_header_generator