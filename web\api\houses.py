from flask import Blueprint, jsonify, request, current_app
from web.utils.decorators import login_required

houses_api = Blueprint('houses_api', __name__)

@houses_api.route('/estates', methods=['GET'])
def get_estates():
    """获取房源历史数据"""
    try:
        config_manager = current_app.config_manager

        # 获取数据管理器
        data_manager = config_manager.get_data_manager()

        # 获取房源历史数据
        history = data_manager.get_history()

        return jsonify(history)

    except Exception as e:
        current_app.logger.error(f"获取房源数据失败: {e}")
        return jsonify({'error': str(e)}), 500

@houses_api.route('/houses', methods=['GET'])
@login_required
def get_houses():
    """获取所有房源列表"""
    try:
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()

        # 从数据库获取监控配置
        monitor_configs = data_manager.get_enabled_monitor_configs()

        # 构建房源信息列表
        houses = []
        for house in monitor_configs:
            house_info = {
                'name': house.get('name'),
                'enabled': house.get('enabled', True),
                'device_count': len(house.get('device_ids', [])),
                'url': house.get('url'),
                'update_interval': house.get('update_interval', 60),
                'last_update': house.get('last_update')
            }
            houses.append(house_info)

        return jsonify({
            'houses': houses,
            'total': len(houses)
        })

    except Exception as e:
        current_app.logger.error(f"获取房源列表失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@houses_api.route('/houses', methods=['POST'])
@login_required
def add_house():
    """添加新房源"""
    try:
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()
        house_data = request.json

        if not house_data or 'name' not in house_data:
            return jsonify({'error': '缺少房源名称'}), 400

        house_name = house_data['name']

        # 从数据库获取监控配置
        monitor_configs = data_manager.get_monitor_configs()

        # 检查房源名称是否已存在
        if any(h.get('name') == house_name for h in monitor_configs):
            return jsonify({'error': f'房源名称 "{house_name}" 已存在'}), 400

        # 创建新房源
        new_house = {
            'name': house_name,
            'enabled': house_data.get('enabled', True),
            'device_ids': house_data.get('device_ids', [])
        }

        # 保存新房源到数据库
        data_manager.save_monitor_config(new_house)

        # 通过HTTP API重新加载监控配置
        try:
            from web.clients.monitor_client_simple import simple_reload_config
            success, data = simple_reload_config(current_app.logger)
            if success:
                current_app.logger.info("监控服务配置重载成功")
            else:
                current_app.logger.warning(f"监控服务配置重载失败: {data}")
        except Exception as e:
            current_app.logger.warning(f"无法重载监控服务配置: {str(e)}")

        return jsonify({'status': 'success', 'house': new_house})

    except Exception as e:
        current_app.logger.error(f"添加房源失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@houses_api.route('/houses/<path:house_name>', methods=['PUT'])
@login_required
def update_house(house_name):
    """更新单个房源的配置"""
    try:
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()
        update_data = request.json

        # 从数据库获取监控配置
        monitor_configs = data_manager.get_monitor_configs()

        house_to_update = next((h for h in monitor_configs if h.get('name') == house_name), None)

        if not house_to_update:
            return jsonify({'error': f'未找到房源: {house_name}'}), 404

        # 更新字段
        house_to_update['enabled'] = update_data.get('enabled', house_to_update.get('enabled'))
        house_to_update['device_ids'] = update_data.get('device_ids', house_to_update.get('device_ids'))

        # 如果名称被更改
        if 'name' in update_data and update_data['name'] != house_name:
            new_name = update_data['name']
            # 检查新名称是否已存在
            if any(h.get('name') == new_name for h in monitor_configs):
                return jsonify({'error': f'房源名称 "{new_name}" 已存在'}), 400
            house_to_update['name'] = new_name

        # 保存更新到数据库
        data_manager.save_monitor_config(house_to_update)

        # 通过HTTP API重新加载监控配置
        try:
            from web.clients.monitor_client_simple import simple_reload_config
            success, data = simple_reload_config(current_app.logger)
            if success:
                current_app.logger.info("监控服务配置重载成功")
            else:
                current_app.logger.warning(f"监控服务配置重载失败: {data}")
        except Exception as e:
            current_app.logger.warning(f"无法重载监控服务配置: {str(e)}")

        return jsonify({'status': 'success', 'house': house_to_update})

    except Exception as e:
        current_app.logger.error(f"更新房源失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@houses_api.route('/houses/<house_name>')
@login_required
def get_house_detail(house_name):
    """获取房源详细信息"""
    try:
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()

        # 从数据库获取监控配置
        monitor_configs = data_manager.get_monitor_configs()

        # 查找指定房源
        house_config = next((c for c in monitor_configs if c.get('name') == house_name), None)
        if house_config is None:
            return jsonify({'error': f'未找到房源: {house_name}'}), 404

        # 获取房源关联的设备信息
        device_ids = house_config.get('device_ids', [])
        devices = []

        for device_id in device_ids:
            device = data_manager.get_device(device_id)
            if device:
                devices.append(device)

        # 构建房源详细信息
        house_detail = {
            'name': house_config.get('name'),
            'enabled': house_config.get('enabled', True),
            'url': house_config.get('url'),
            'update_interval': house_config.get('update_interval', 60),
            'last_update': house_config.get('last_update'),
            'device_ids': device_ids,
            'devices': devices,
            'device_count': len(devices)
        }

        return jsonify(house_detail)

    except Exception as e:
        current_app.logger.error(f"获取房源详细信息失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@houses_api.route('/houses/<path:house_name>', methods=['DELETE'])
@login_required
def delete_house(house_name):
    """删除指定房源"""
    try:
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()

        current_app.logger.info(f"开始删除房源: {house_name}")

        # 从数据库获取监控配置
        monitor_configs = data_manager.get_monitor_configs()

        # 检查房源是否存在
        house_to_delete = next((h for h in monitor_configs if h.get('name') == house_name), None)

        if not house_to_delete:
            current_app.logger.warning(f"未找到房源: {house_name}")
            return jsonify({'error': f'未找到房源: {house_name}'}), 404

        current_app.logger.info(f"找到要删除的房源: {house_to_delete}")

        # 执行完整删除操作（删除所有相关数据）
        delete_success = data_manager.delete_house_completely(house_name)

        if not delete_success:
            current_app.logger.error(f"删除房源失败: {house_name}")
            return jsonify({'error': f'删除房源 "{house_name}" 失败'}), 500

        current_app.logger.info(f"房源 '{house_name}' 已成功删除")

        # 通过HTTP API重新加载监控配置
        try:
            from web.clients.monitor_client_simple import simple_reload_config
            success, data = simple_reload_config(current_app.logger)
            if success:
                current_app.logger.info("监控服务配置重载成功")
            else:
                current_app.logger.warning(f"监控服务配置重载失败: {data}")
        except Exception as e:
            current_app.logger.warning(f"无法重载监控服务配置: {str(e)}")

        return jsonify({
            'status': 'success',
            'message': f'房源 "{house_name}" 已成功删除',
            'deleted_house': house_name
        })

    except Exception as e:
        current_app.logger.error(f"删除房源失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@houses_api.route('/device_house_relations', methods=['GET'])
@login_required
def get_device_house_relations():
    """获取设备和房源的关联关系检测报告"""
    try:
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()

                # 获取关联关系 - 直接通过monitor_model访问
        relations = data_manager.monitor_model.get_device_house_relations()

        # 获取孤立设备
        orphaned_devices = data_manager.monitor_model.get_orphaned_devices()

        # 获取所有设备
        all_devices = data_manager.get_devices()

        # 统计信息
        stats = {
            'total_devices': len(all_devices),
            'linked_devices': len(relations),
            'orphaned_devices': len(orphaned_devices),
            'total_relations': len(relations)
        }

        # 按设备类型分组孤立设备
        orphaned_by_type = {}
        for device in orphaned_devices:
            device_type = device['type']
            if device_type not in orphaned_by_type:
                orphaned_by_type[device_type] = []
            orphaned_by_type[device_type].append(device)

        # 按房源分组关联关系
        relations_by_house = {}
        for relation in relations:
            house_name = relation['house_name']
            if house_name not in relations_by_house:
                relations_by_house[house_name] = []
            relations_by_house[house_name].append(relation)

        # 检测问题
        issues = []

        # 检测过期设备仍在使用
        for relation in relations:
            if relation['device_expire_date']:
                from datetime import datetime, date
                try:
                    expire_date = datetime.strptime(relation['device_expire_date'], '%Y-%m-%d').date()
                    if expire_date < date.today():
                        issues.append({
                            'type': 'expired_device_in_use',
                            'severity': 'high',
                            'message': f"已过期设备 '{relation['device_name']}' 仍在房源 '{relation['house_name']}' 中使用",
                            'device_id': relation['device_id'],
                            'house_name': relation['house_name'],
                            'expire_date': relation['device_expire_date']
                        })
                except ValueError:
                    pass

        # 检测禁用房源仍有设备关联
        for relation in relations:
            if not relation['house_enabled']:
                issues.append({
                    'type': 'device_linked_to_disabled_house',
                    'severity': 'medium',
                    'message': f"设备 '{relation['device_name']}' 关联到已禁用的房源 '{relation['house_name']}'",
                    'device_id': relation['device_id'],
                    'house_name': relation['house_name']
                })

        return jsonify({
            'status': 'success',
            'stats': stats,
            'relations': relations,
            'orphaned_devices': orphaned_devices,
            'orphaned_by_type': orphaned_by_type,
            'relations_by_house': relations_by_house,
            'issues': issues
        })

    except Exception as e:
        current_app.logger.error(f"获取设备房源关联关系失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@houses_api.route('/device_house_relations/fix_orphaned', methods=['POST'])
@login_required
def fix_orphaned_devices():
    """批量修复孤立设备，将其关联到指定房源"""
    try:
        config_manager = current_app.config_manager
        data_manager = config_manager.get_data_manager()

        request_data = request.json
        if not request_data:
            return jsonify({'error': '缺少请求数据'}), 400

        device_ids = request_data.get('device_ids', [])
        house_name = request_data.get('house_name', '')

        if not device_ids or not house_name:
            return jsonify({'error': '缺少设备ID列表或房源名称'}), 400

        # 获取房源配置
        monitor_configs = data_manager.get_monitor_configs()
        house_config = next((h for h in monitor_configs if h.get('name') == house_name), None)

        if not house_config:
            return jsonify({'error': f'房源 "{house_name}" 不存在'}), 404

        # 添加设备到房源
        current_device_ids = house_config.get('device_ids', [])
        new_device_ids = list(set(current_device_ids + device_ids))

        house_config['device_ids'] = new_device_ids

        # 保存更新
        data_manager.save_monitor_config(house_config)

        # 通过HTTP API重新加载监控配置
        try:
            from web.clients.monitor_client_simple import simple_reload_config
            success, data = simple_reload_config(current_app.logger)
            if success:
                current_app.logger.info("监控服务配置重载成功")
            else:
                current_app.logger.warning(f"监控服务配置重载失败: {data}")
        except Exception as e:
            current_app.logger.warning(f"无法重载监控服务配置: {str(e)}")

        return jsonify({
            'status': 'success',
            'message': f'成功将 {len(device_ids)} 个设备关联到房源 "{house_name}"',
            'added_devices': len(device_ids),
            'total_devices': len(new_device_ids)
        })

    except Exception as e:
        current_app.logger.error(f"修复孤立设备失败: {str(e)}")
        return jsonify({'error': str(e)}), 500