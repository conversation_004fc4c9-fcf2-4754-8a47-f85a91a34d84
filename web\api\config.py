"""
配置API模块 - 处理配置管理相关的API接口
统一配置版本 - 使用统一配置管理器
"""

import threading
from flask import Blueprint, jsonify, request, current_app
from web.utils.decorators import login_required


config_api = Blueprint('config_api', __name__)


@config_api.route('/config', methods=['GET'])
@login_required
def get_config():
    """获取当前配置"""
    try:
        # 直接从统一配置获取所有配置
        unified_config = current_app.unified_config
        config = unified_config.get_all_config()

        # 获取数据管理器以获取设备和监控配置
        data_manager = current_app.config_manager.get_data_manager()

        # 为了向后兼容，添加设备列表和监控配置
        config['device_list'] = data_manager.get_devices()
        config['monitor_configs'] = data_manager.get_monitor_configs()

        return jsonify(config)

    except Exception as e:
        current_app.logger.error(f"获取配置失败: {e}")
        return jsonify({'error': '获取配置失败', 'details': str(e)}), 500


@config_api.route('/config', methods=['POST'])
@login_required
def update_config():
    """更新配置"""
    try:
        data = request.json
        if not data:
            return jsonify({'error': '无效的配置数据'}), 400

        unified_config = current_app.unified_config

        # 检查是否修改了代理API URL
        proxy_api_changed = False
        old_proxy_url = unified_config.get('proxy_api_url')
        if 'proxy_api_url' in data and old_proxy_url != data.get('proxy_api_url'):
            proxy_api_changed = True
            current_app.logger.info(f"检测到代理API URL变更: {old_proxy_url} -> {data.get('proxy_api_url')}")

        # 分离敏感配置和一般配置
        sensitive_keys = ['USERNAME', 'PASSWORD_HASH', 'SECRET_KEY']
        web_auth_config = {}
        general_config = {}

        for key, value in data.items():
            if key in ['device_list', 'monitor_configs']:
                # 跳过这些数据项，它们由数据管理器处理
                continue
            elif key in sensitive_keys:
                # 映射敏感配置到web_auth
                key_mappings = {
                    'SECRET_KEY': 'secret_key',
                    'USERNAME': 'username',
                    'PASSWORD_HASH': 'password_hash'
                }
                mapped_key = key_mappings.get(key, key)
                web_auth_config[mapped_key] = value
            else:
                # 一般配置
                general_config[key] = value

        # 构建保存的配置结构
        config_to_save = {}

        # 添加敏感配置
        if web_auth_config:
            config_to_save['web_auth'] = web_auth_config

        # 添加一般配置
        config_to_save.update(general_config)

        # 保存到用户配置文件
        if config_to_save:
            unified_config.save_user_config(config_to_save)
            current_app.logger.info("配置已更新")

            # 通过HTTP API重新加载监控配置
            try:
                from web.clients.monitor_client_simple import simple_reload_config
                success, data_result = simple_reload_config(current_app.logger)
                if success:
                    current_app.logger.info("监控服务配置重载成功")
                else:
                    current_app.logger.warning(f"监控服务配置重载失败: {data_result}")
            except Exception as e:
                current_app.logger.warning(f"无法重载监控服务配置: {str(e)}")

            # 如果代理API URL发生变化，立即更新代理
            if proxy_api_changed:
                current_app.logger.info("代理API URL已变更，立即更新代理")
                # 创建一个新线程来更新代理，避免阻塞API响应
                threading.Thread(target=update_proxy_immediately, args=(current_app.logger,)).start()

        return jsonify({'status': 'success', 'proxy_api_changed': proxy_api_changed})

    except Exception as e:
        current_app.logger.error(f"更新配置失败: {e}")
        return jsonify({'error': '更新配置失败', 'details': str(e)}), 500


@config_api.route('/config/summary', methods=['GET'])
@login_required
def get_config_summary():
    """获取配置摘要信息"""
    try:
        unified_config = current_app.unified_config
        summary = unified_config.get_config_summary()
        return jsonify(summary)

    except Exception as e:
        current_app.logger.error(f"获取配置摘要失败: {e}")
        return jsonify({'error': '获取配置摘要失败', 'details': str(e)}), 500


@config_api.route('/config/reload', methods=['POST'])
@login_required
def reload_config():
    """重新加载配置"""
    try:
        unified_config = current_app.unified_config
        unified_config.reload()

        current_app.logger.info("配置已重新加载")
        return jsonify({'message': '配置重新加载成功'})

    except Exception as e:
        current_app.logger.error(f"重新加载配置失败: {e}")
        return jsonify({'error': '重新加载配置失败', 'details': str(e)}), 500


@config_api.route('/config/validate', methods=['POST'])
@login_required
def validate_config():
    """验证配置完整性"""
    try:
        unified_config = current_app.unified_config
        validation_errors = unified_config._validate_config()

        if validation_errors:
            return jsonify({
                'valid': False,
                'errors': validation_errors
            })
        else:
            return jsonify({
                'valid': True,
                'message': '配置验证通过'
            })

    except Exception as e:
        current_app.logger.error(f"配置验证失败: {e}")
        return jsonify({'error': '配置验证失败', 'details': str(e)}), 500


def update_proxy_immediately(logger):
    """立即更新代理IP的辅助函数"""
    try:
        # 通过HTTP API更新代理
        from web.clients.monitor_client_simple import simple_refresh_proxy
        success, data = simple_refresh_proxy(logger)

        if success:
            logger.info(f"代理IP更新完成: {data}")
        else:
            logger.warning(f"代理IP更新失败: {data}")
    except Exception as e:
        logger.error(f"强制更新代理IP失败: {str(e)}")