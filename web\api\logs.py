"""
日志API模块 - 处理日志查看和下载相关的API接口
"""

import os
from datetime import datetime
from flask import Blueprint, jsonify, current_app, send_file, request
from web.utils.decorators import login_required
from core.utils.logger_adapter import get_current_log_file_replacement as get_current_log_file


logs_api = Blueprint('logs_api', __name__)


def read_file_tail(file_path, max_lines=50000, encoding='utf-8'):
    """
    高效读取文件尾部指定行数的内容

    Args:
        file_path: 文件路径
        max_lines: 最大读取行数，默认50000，0或负数表示读取全部
        encoding: 文件编码，默认utf-8

    Returns:
        list: 文件尾部的行列表
    """
    try:
        lines = []
        with open(file_path, 'r', encoding=encoding, errors='replace') as f:
            # 读取全部行
            all_lines = f.readlines()

            # 如果max_lines <= 0，返回全部行
            if max_lines <= 0:
                lines = all_lines
            else:
                # 如果文件总行数小于等于限制行数，返回全部行
                if len(all_lines) <= max_lines:
                    lines = all_lines
                else:
                    # 取最后max_lines行
                    lines = all_lines[-max_lines:]

        return lines
    except Exception as e:
        current_app.logger.error(f"读取文件尾部失败: {str(e)}")
        return []


@logs_api.route('/logs')
@login_required
def get_logs():
    """获取最新的日志内容"""
    try:
        # 获取行数限制参数，默认50000行
        limit = request.args.get('limit', 50000, type=int)

        # 获取当前日志文件路径
        current_log_file = get_current_log_file()

        # 使用错误处理机制读取日志文件
        lines = []
        if os.path.exists(current_log_file):
            lines = read_file_tail(current_log_file, limit)
            if not lines:
                # 如果尾部读取失败，尝试传统方式
                try:
                    with open(current_log_file, 'r', encoding='utf-8', errors='replace') as f:
                        all_lines = f.readlines()
                        lines = all_lines if limit <= 0 else (all_lines[-limit:] if len(all_lines) > limit else all_lines)
                except Exception as e:
                    current_app.logger.warning(f"传统读取也失败: {str(e)}")
        else:
            # 增加对主日志文件的回退读取
            log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'log')
            log_dir = os.path.normpath(log_dir)
            base_log_path = os.path.join(log_dir, 'monitor.log')
            if os.path.exists(base_log_path):
                current_app.logger.info(f"未找到今日日志，正在回退读取主日志文件。")
                lines = read_file_tail(base_log_path, limit)

        return jsonify({
            'logs': lines,
            'total_lines': len(lines),
            'limited': limit
        })
    except Exception as e:
        current_app.logger.error(f"读取日志失败: {str(e)}")
        return jsonify({'error': str(e), 'logs': ["无法读取日志文件。错误: " + str(e)]}), 500


@logs_api.route('/logs/<date>')
@login_required
def get_logs_by_date(date):
    """根据日期获取特定的日志内容"""
    try:
        # 获取行数限制参数，默认50000行
        limit = request.args.get('limit', 50000, type=int)

        # 验证日期格式
        try:
            datetime.strptime(date, "%Y-%m-%d")
        except ValueError:
            return jsonify({'error': '日期格式无效，请使用YYYY-MM-DD格式'}), 400

        # 构建指定日期的日志文件路径 - 修复文件名格式
        log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'log')
        log_dir = os.path.normpath(log_dir)
        log_file_path = os.path.join(log_dir, f'monitor.log.{date}.log')

        # 使用高效读取方式读取日志文件
        lines = []
        if os.path.exists(log_file_path):
            lines = read_file_tail(log_file_path, limit)
            if not lines:
                # 如果尾部读取失败，尝试传统方式
                try:
                    with open(log_file_path, 'r', encoding='utf-8', errors='replace') as f:
                        all_lines = f.readlines()
                        lines = all_lines if limit <= 0 else (all_lines[-limit:] if len(all_lines) > limit else all_lines)
                except Exception as e:
                    current_app.logger.warning(f"读取日志文件 {date} 失败: {str(e)}")
        else:
            return jsonify({'error': f'指定日期 {date} 的日志文件不存在'}), 404

        return jsonify({
            'logs': lines,
            'date': date,
            'total_lines': len(lines),
            'limited': limit
        })
    except Exception as e:
        current_app.logger.error(f"读取日志失败: {str(e)}")
        return jsonify({'error': str(e), 'logs': ["无法读取日志文件。错误: " + str(e)]}), 500


@logs_api.route('/logs/available')
@login_required
def get_available_logs():
    """获取可用的历史日志文件列表"""
    try:
        available_logs = []

        # 获取log目录中的所有文件
        log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'log')
        log_dir = os.path.normpath(log_dir)
        log_files = os.listdir(log_dir)

        # 筛选出符合日志命名格式的文件
        for file in log_files:
            # 修改此处以匹配 monitor.log.YYYY-MM-DD.log 格式
            if file.startswith('monitor.log.') and file.endswith('.log') and file != 'monitor.log':
                # 尝试提取日期部分
                try:
                    # 修改提取日期的方式，去掉前缀和后缀
                    date_str = file.replace('monitor.log.', '').replace('.log', '')
                    # 验证日期格式
                    datetime.strptime(date_str, "%Y-%m-%d")
                    # 构建日志信息
                    log_path = os.path.join(log_dir, file)
                    log_size = os.path.getsize(log_path)
                    log_mtime = os.path.getmtime(log_path)
                    available_logs.append({
                        'date': date_str,
                        'size': log_size,
                        'modified': log_mtime
                    })
                except ValueError:
                    # 跳过不符合日期格式的文件
                    continue

        # 按日期排序，最新的在前面
        available_logs.sort(key=lambda x: x['date'], reverse=True)

        return jsonify({'logs': available_logs})
    except Exception as e:
        current_app.logger.error(f"获取可用日志列表失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@logs_api.route('/logs/download/<date>')
@login_required
def download_logs_by_date(date):
    """下载指定日期的日志文件"""
    try:
        # 验证日期格式
        try:
            datetime.strptime(date, "%Y-%m-%d")
        except ValueError:
            return jsonify({'error': '日期格式无效，请使用YYYY-MM-DD格式'}), 400

        # 构建指定日期的日志文件路径 - 修复文件名格式
        log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'log')
        log_dir = os.path.normpath(log_dir)
        log_file_path = os.path.join(log_dir, f'monitor.log.{date}.log')

        # 检查文件是否存在
        if not os.path.exists(log_file_path):
            return jsonify({'error': f'指定日期 {date} 的日志文件不存在'}), 404

        # 设置下载文件名
        download_name = f'monitor_{date}.log'

        return send_file(log_file_path,
                         mimetype='text/plain; charset=utf-8',
                         as_attachment=True,
                         download_name=download_name)
    except Exception as e:
        current_app.logger.error(f"下载日志失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@logs_api.route('/logs/download')
@login_required
def download_logs():
    """下载当天的日志文件"""
    try:
        # 获取当前日志文件路径
        current_log_file = get_current_log_file()

        # 检查文件是否存在
        if not os.path.exists(current_log_file):
            return jsonify({'error': '当前日志文件不存在'}), 404

        # 获取当前日期作为文件名
        today = datetime.now().strftime("%Y-%m-%d")
        download_name = f'monitor_{today}.log'

        return send_file(current_log_file,
                         mimetype='text/plain; charset=utf-8',
                         as_attachment=True,
                         download_name=download_name)
    except Exception as e:
        current_app.logger.error(f"下载日志失败: {str(e)}")
        return jsonify({'error': str(e)}), 500