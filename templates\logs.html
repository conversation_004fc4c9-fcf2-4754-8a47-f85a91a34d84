<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房源监控系统 - 日志</title>
    <!-- Bootstrap CSS -->
    <link rel="shortcut icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">
    <link rel="icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.min.css">
    <style>
        .log-container {
            height: 700px;
            overflow-y: auto;
            background-color: #f8f9fa;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 0.9rem;
            border-radius: 5px;
            padding: 10px;
        }
        .log-entry {
            padding: 3px 5px;
            border-bottom: 1px solid #eee;
            line-height: 1.4;
        }
        .log-entry.error { color: #dc3545; }
        .log-entry.warning { color: #ffc107; }
        .log-entry.info { color: #0d6efd; }
        .toolbar {
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 5px 5px 0 0;
            border-bottom: 1px solid #ddd;
        }
        .search-container {
            margin-bottom: 10px;
        }
        #searchInput {
            max-width: 300px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #666;
        }
        .log-date-selector {
            width: 100%;
            background-color: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .log-badge {
            font-size: 85%;
            padding: 0.25em 0.6em;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        }
        .navbar-brand {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">房源监控系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('views.index') }}">主页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('views.logs_page') }}">系统日志</a>
                    </li>
                </ul>
            </div>
            <div class="d-flex align-items-center">
                <div class="text-white me-3" id="currentTime"></div>
                <a href="/api/logs/download" id="downloadLogLink" class="btn btn-outline-light btn-sm me-2">下载日志</a>
                <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-light btn-sm">退出登录</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">系统日志</h5>
                <div>
                    <span id="currentLogDate" class="badge bg-primary">当前日志</span>
                </div>
            </div>

            <div class="log-date-selector">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <div class="input-group">
                            <label class="input-group-text" for="logDateSelect">选择日期</label>
                            <select class="form-select" id="logDateSelect">
                                <option value="current">当前日志</option>
                                <!-- 日志日期选项将通过JavaScript动态添加 -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <label class="input-group-text" for="logLimitSelect">显示行数</label>
                            <select class="form-select" id="logLimitSelect">
                                <option value="1000">1千行</option>
                                <option value="5000">5千行</option>
                                <option value="10000">1万行</option>
                                <option value="50000" selected>5万行</option>
                                <option value="100000">10万行</option>
                                <option value="0">全部</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <span id="logFileInfo" class="text-muted">
                            <!-- 日志文件信息将通过JavaScript显示 -->
                        </span>
                    </div>
                </div>
            </div>

            <div class="toolbar">
                <div class="row align-items-center">
                    <div class="col-md-6 search-container">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索日志...">
                            <button class="btn btn-outline-secondary" type="button" id="searchButton">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                            <button class="btn btn-outline-secondary" type="button" id="clearSearchButton">
                                <i class="bi bi-x"></i> 清除
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="btn-group me-2">
                            <button id="refreshButton" class="btn btn-primary">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                            <button id="clearButton" class="btn btn-secondary">
                                <i class="bi bi-trash"></i> 清空显示
                            </button>
                        </div>
                        <div class="form-check form-check-inline align-middle">
                            <input class="form-check-input" type="checkbox" id="autoRefreshToggle">
                            <label class="form-check-label" for="autoRefreshToggle">自动刷新</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="logContainer" class="log-container">
                    <div class="loading">加载日志中...</div>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <div>
                    <span id="logCount">0</span> 条日志
                    <button id="downloadCurrentLogButton" class="btn btn-sm btn-outline-primary ms-2">
                        <i class="bi bi-download"></i> 下载当前显示的日志
                    </button>
                </div>
                <div class="btn-group">
                    <button id="scrollToTopButton" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-arrow-up"></i> 回到顶部
                    </button>
                    <button id="scrollToBottomButton" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-arrow-down"></i> 滚动到底部
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加Bootstrap和自定义脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let autoRefreshInterval = null;
        let allLogs = [];
        let currentDate = 'current'; // 'current'表示当前日志，其他值为特定日期格式YYYY-MM-DD
        let availableDates = []; // 存储可用的日志日期
        let currentLimit = 50000; // 当前行数限制

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 加载可用的日志文件列表
            loadAvailableLogs();

            // 加载当前日志
            loadLogs();

            // 更新当前时间
            updateCurrentTime();

            // 设置定时更新时间
            setInterval(updateCurrentTime, 1000);

            // 绑定按钮事件
            document.getElementById('refreshButton').addEventListener('click', loadLogs);
            document.getElementById('clearButton').addEventListener('click', clearLogDisplay);
            document.getElementById('autoRefreshToggle').addEventListener('change', toggleAutoRefresh);
            document.getElementById('scrollToTopButton').addEventListener('click', scrollToTop);
            document.getElementById('scrollToBottomButton').addEventListener('click', scrollToBottom);
            document.getElementById('searchButton').addEventListener('click', searchLogs);
            document.getElementById('clearSearchButton').addEventListener('click', clearSearch);
            document.getElementById('logDateSelect').addEventListener('change', changeLogDate);
            document.getElementById('logLimitSelect').addEventListener('change', changeLimitAndReload);
            document.getElementById('downloadCurrentLogButton').addEventListener('click', downloadCurrentLog);

            // 添加搜索框回车键监听
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchLogs();
                }
            });
        });

        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            document.getElementById('currentTime').textContent = timeStr;
        }

        // 加载可用的日志文件列表
        async function loadAvailableLogs() {
            try {
                console.log("正在加载可用日志列表...");
                const response = await fetch('/api/logs/available');
                if (!response.ok) {
                    throw new Error(`HTTP错误 ${response.status}`);
                }

                const data = await response.json();
                console.log("获取到的日志列表:", data);

                if (data.error) {
                    console.error("获取日志列表错误:", data.error);
                    // 在页面上显示错误信息
                    document.getElementById('logFileInfo').innerHTML =
                        `<span class="text-danger">加载日志列表失败: ${data.error}</span>`;
                    return;
                }

                if (data.logs && Array.isArray(data.logs)) {
                    // 更新全局变量
                    availableDates = data.logs;
                    console.log(`找到 ${availableDates.length} 个可用日志文件`);

                    // 更新日期选择器
                    updateDateSelector();
                } else {
                    console.warn("未找到可用的日志文件");
                    document.getElementById('logFileInfo').textContent = "未找到可用的历史日志文件";
                }
            } catch (error) {
                console.error('加载日志列表失败:', error);
                document.getElementById('logFileInfo').innerHTML =
                    `<span class="text-danger">加载日志列表失败: ${error.message}</span>`;
            }
        }

        // 更新日期选择器
        function updateDateSelector() {
            const select = document.getElementById('logDateSelect');

            // 保留第一个"当前日志"选项，清除其他选项
            while (select.options.length > 1) {
                select.remove(1);
            }

            // 添加新的日期选项
            availableDates.forEach(log => {
                const option = document.createElement('option');
                option.value = log.date;

                // 格式化日期显示
                const date = new Date(log.date);
                const formattedDate = date.toLocaleDateString('zh-CN');

                // 格式化文件大小
                const size = formatBytes(log.size);

                option.textContent = `${formattedDate} (${size})`;
                select.appendChild(option);
            });

            // 更新日志文件信息
            updateLogFileInfo();
        }

        // 切换日志日期
        function changeLogDate() {
            const select = document.getElementById('logDateSelect');
            currentDate = select.value;

            // 更新当前日志日期显示
            updateCurrentLogDateDisplay();

            // 更新下载链接
            updateDownloadLink();

            // 更新日志文件信息
            updateLogFileInfo();

            // 重新加载日志
            loadLogs();

            // 如果不是当前日志，禁用自动刷新
            if (currentDate !== 'current') {
                document.getElementById('autoRefreshToggle').checked = false;
                clearInterval(autoRefreshInterval);
            }
        }

        // 更新当前日志日期显示
        function updateCurrentLogDateDisplay() {
            const badgeElement = document.getElementById('currentLogDate');

            if (currentDate === 'current') {
                badgeElement.textContent = '当前日志';
                badgeElement.className = 'badge bg-primary';
            } else {
                badgeElement.textContent = `${currentDate} 的日志`;
                badgeElement.className = 'badge bg-secondary';
            }
        }

        // 更新下载链接
        function updateDownloadLink() {
            const downloadLink = document.getElementById('downloadLogLink');

            if (currentDate === 'current') {
                downloadLink.href = '/api/logs/download';
            } else {
                downloadLink.href = `/api/logs/download/${currentDate}`;
            }
        }

        // 更新日志文件信息
        function updateLogFileInfo() {
            const infoElement = document.getElementById('logFileInfo');

            if (currentDate === 'current') {
                infoElement.textContent = '当前日志文件';
                return;
            }

            // 在可用日期中查找当前选择的日期
            const logInfo = availableDates.find(log => log.date === currentDate);

            if (logInfo) {
                // 格式化文件大小
                const size = formatBytes(logInfo.size);

                // 格式化修改时间
                const modifiedDate = new Date(logInfo.modified * 1000);
                const formattedModifiedDate = modifiedDate.toLocaleString('zh-CN');

                infoElement.textContent = `文件大小: ${size} | 最后修改: ${formattedModifiedDate}`;
            } else {
                infoElement.textContent = '';
            }
        }

        // 格式化字节数为可读格式
        function formatBytes(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }

                // 切换行数限制并重新加载
        function changeLimitAndReload() {
            const select = document.getElementById('logLimitSelect');
            currentLimit = parseInt(select.value);

            // 重新加载日志
            loadLogs();
        }

        // 加载日志
        async function loadLogs() {
            try {
                let url = '/api/logs';
                console.log(`正在加载${currentDate === 'current' ? '当前' : currentDate}日志... (限制: ${currentLimit === 0 ? '全部' : currentLimit + '行'})`);

                // 如果是特定日期的日志，使用对应的API
                if (currentDate !== 'current') {
                    url = `/api/logs/${currentDate}`;
                }

                // 添加行数限制参数
                if (currentLimit > 0) {
                    url += `?limit=${currentLimit}`;
                }

                // 显示加载状态
                document.getElementById('logContainer').innerHTML =
                    '<div class="loading">正在加载日志，请稍候...</div>';

                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP错误 ${response.status}`);
                }

                const data = await response.json();
                console.log(`获取到的日志数据:`, {
                    hasError: !!data.error,
                    logCount: data.logs ? data.logs.length : 0,
                    totalLines: data.total_lines,
                    limited: data.limited
                });

                if (data.error) {
                    console.error("获取日志错误:", data.error);
                    document.getElementById('logContainer').innerHTML =
                        `<div class="alert alert-danger m-3">加载日志失败: ${data.error}</div>`;
                    return;
                }

                if (data.logs) {
                    allLogs = data.logs;
                    displayLogs(allLogs);

                    // 更新状态信息
                    updateLogStatusInfo(data);
                }
            } catch (error) {
                console.error('加载日志失败:', error);
                document.getElementById('logContainer').innerHTML =
                    `<div class="alert alert-danger m-3">加载日志失败: ${error.message}</div>`;
            }
        }

        // 更新日志状态信息
        function updateLogStatusInfo(data) {
            if (data.total_lines && data.limited) {
                const limitInfo = data.limited > 0 ? `(显示最后 ${data.limited} 行)` : '(显示全部)';
                document.getElementById('logFileInfo').innerHTML +=
                    ` <span class="badge bg-info">共 ${data.total_lines} 行 ${limitInfo}</span>`;
            }
        }

        // 显示日志
        function displayLogs(logs) {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '';

            if (logs.length === 0) {
                logContainer.innerHTML = '<div class="text-center p-4 text-muted">没有日志记录</div>';
                document.getElementById('logCount').textContent = '0';
                return;
            }

            logs.forEach(log => {
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';

                // 根据日志级别添加样式
                if (log.includes(' - ERROR ')) {
                    logEntry.classList.add('error');
                } else if (log.includes(' - WARNING ')) {
                    logEntry.classList.add('warning');
                } else if (log.includes(' - INFO ')) {
                    logEntry.classList.add('info');
                }

                logEntry.textContent = log;
                logContainer.appendChild(logEntry);
            });

            // 更新日志计数
            document.getElementById('logCount').textContent = logs.length;

            // 滚动到底部
            scrollToBottom();
        }

        // 清空日志显示
        function clearLogDisplay() {
            document.getElementById('logContainer').innerHTML = '';
            document.getElementById('logCount').textContent = '0';
        }

        // 开关自动刷新
        function toggleAutoRefresh(event) {
            // 如果是历史日志，不允许自动刷新
            if (currentDate !== 'current') {
                event.target.checked = false;
                alert('只有当前日志支持自动刷新');
                return;
            }

            if (event.target.checked) {
                autoRefreshInterval = setInterval(loadLogs, 5000); // 每5秒刷新
            } else {
                clearInterval(autoRefreshInterval);
            }
        }

        // 滚动到顶部
        function scrollToTop() {
            const logContainer = document.getElementById('logContainer');
            logContainer.scrollTop = 0;
        }

        // 滚动到底部
        function scrollToBottom() {
            const logContainer = document.getElementById('logContainer');
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 搜索日志
        function searchLogs() {
            const searchTerm = document.getElementById('searchInput').value.trim().toLowerCase();

            if (!searchTerm) {
                // 如果搜索框为空，显示所有日志
                displayLogs(allLogs);
                return;
            }

            // 过滤匹配的日志
            const filteredLogs = allLogs.filter(log =>
                log.toLowerCase().includes(searchTerm)
            );

            // 显示过滤后的日志
            displayLogs(filteredLogs);
        }

        // 清除搜索
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            displayLogs(allLogs);
        }

        // 下载当前显示的日志
        function downloadCurrentLog() {
            // 获取当前要下载的URL
            let url = '/api/logs/download';

            if (currentDate !== 'current') {
                url = `/api/logs/download/${currentDate}`;
            }

            // 创建一个隐藏的a标签并点击它来触发下载
            const a = document.createElement('a');
            a.href = url;
            a.download = `monitor_${currentDate === 'current' ? new Date().toISOString().split('T')[0] : currentDate}.log`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }
    </script>
</body>
</html>