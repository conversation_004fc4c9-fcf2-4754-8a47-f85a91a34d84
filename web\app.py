"""
Flask应用主文件 - 简化版Web控制台
移除了复杂的线程管理和事件循环，通过HTTP API与独立监控服务通信
优化版本 - 统一配置管理
"""

import os
import sys
import secrets
import socket
import logging
from flask import Flask, jsonify

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入模块
from core.scheduling import Scheduler
from core.utils.logger_adapter import setup_logging_replacement
from web.utils.device_checker import DeviceExpiryChecker

# 导入统一异常处理框架
from core.exceptions import AppException

# 导入Blueprint
from web.auth import auth_bp
from web.views import views_bp
from web.api import (
    monitor_api, config_api, devices_api,
    logs_api, schedule_api, notification_api
)
from web.api.houses import houses_api
from web.api.grab_devices import grab_devices_api


def create_app():
    """
    Flask应用工厂函数 - 简化版

    Returns:
        Flask: 配置好的Flask应用实例
    """
    # 获取项目根目录路径
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    template_folder = os.path.join(project_root, 'templates')
    static_folder = os.path.join(project_root, 'static')

    app = Flask(__name__,
                template_folder=template_folder,
                static_folder=static_folder)

    # 初始化应用
    init_app_config(app)
    init_logging(app)
    init_services(app)
    init_blueprints(app)

    return app


def _check_and_migrate_database_config(app, unified_config):
    """检查并迁移数据库配置到统一配置系统"""
    try:
        # 检查是否已经迁移过
        if unified_config.has_web_auth_configured():
            app.logger.info("[OK] 配置已迁移到统一配置系统")
            return

        # 尝试从数据库读取配置进行迁移
        from config.migration_tool import ConfigMigrationTool

        migration_tool = ConfigMigrationTool()
        extracted_config = migration_tool.extract_database_config()

        if extracted_config:
            app.logger.info("[INFO] 检测到数据库配置，开始自动迁移...")
            success = migration_tool.migrate_to_unified_config(extracted_config)
            if success:
                app.logger.info("[OK] 数据库配置已成功迁移到统一配置系统")
                # 重新加载配置
                unified_config.reload()
            else:
                app.logger.error("[ERROR] 数据库配置迁移失败，请手动检查")
        else:
            app.logger.info("[INFO] 未检测到需要迁移的数据库配置")

    except Exception as e:
        app.logger.warning(f"[WARN] 配置迁移检查过程中出现问题: {e}")


class SimpleConfigAdapter:
    """简化的配置适配器，提供向后兼容的接口"""

    def __init__(self, unified_config):
        self._config = unified_config
        self._data_manager = None

    def get_data_manager(self):
        """获取数据管理器实例"""
        if self._data_manager is None:
            from core.data.manager import get_data_manager
            self._data_manager = get_data_manager()
        return self._data_manager

    def get(self, key, default=None):
        """获取配置项，支持旧的配置键映射"""
        # 映射旧的配置键到新的统一配置路径
        key_mappings = {
            'SECRET_KEY': 'web_auth.secret_key',
            'USERNAME': 'web_auth.username',
            'PASSWORD_HASH': 'web_auth.password_hash',
            'schedule': 'schedule',
            'admin_notifier': 'admin_notifier',
            'wxpush_app_token': 'wxpush_app_token',
            'proxy_api_url': 'proxy_api_url'
        }

        mapped_key = key_mappings.get(key, key)
        return self._config.get(mapped_key, default)

    def set(self, key, value):
        """设置配置项"""
        # 映射键并保存
        key_mappings = {
            'SECRET_KEY': 'web_auth.secret_key',
            'USERNAME': 'web_auth.username',
            'PASSWORD_HASH': 'web_auth.password_hash'
        }

        mapped_key = key_mappings.get(key, key)

        # 如果是敏感配置，保存到用户配置文件
        if key in key_mappings:
            config_parts = mapped_key.split('.')
            if len(config_parts) == 2:
                config_update = {config_parts[0]: {config_parts[1]: value}}
                self._config.save_user_config(config_update)
            else:
                self._config.set(mapped_key, value)
        else:
            # 对于一般配置，先更新内存，然后根据需要保存
            if key in ['schedule', 'admin_notifier']:
                config_update = {key: value}
                self._config.save_user_config(config_update)
            else:
                self._config.set(mapped_key, value)

    # 其他兼容方法
    def get_all(self):
        """获取所有配置"""
        return self._config.get_all_config()

    def get_devices(self):
        """获取设备列表"""
        return self.get_data_manager().get_devices()

    def get_monitor_configs(self):
        """获取监控配置列表"""
        return self.get_data_manager().get_monitor_configs()


def init_app_config(app):
    """初始化应用配置"""
    # 导入统一配置管理器
    from config.unified_config import get_config

    # 获取统一配置
    unified_config = get_config()
    app.unified_config = unified_config

    # 检查是否需要迁移数据库配置
    _check_and_migrate_database_config(app, unified_config)

    # 验证配置完整性
    validation_errors = unified_config._validate_config()
    if validation_errors:
        app.logger.warning("配置验证发现问题:")
        for error in validation_errors:
            app.logger.warning(f"  - {error}")
        app.logger.warning("配置验证警告不会阻止启动，但建议修复")
    else:
        app.logger.info("配置验证通过")

    # 设置Flask密钥（优先使用统一配置）
    secret = unified_config.get_web_auth_secret_key()
    if not secret:
        app.logger.warning("配置中未找到 web_auth.secret_key，正在生成新的随机密钥。")
        secret = secrets.token_hex(16)
        # 保存到用户配置文件
        unified_config.save_user_config({
            "web_auth": {"secret_key": secret}
        })
    app.secret_key = secret

    # 设置监控服务配置
    monitor_service_url = unified_config.get('monitor_service_url', 'http://127.0.0.1:8088')
    app.monitor_service_url = monitor_service_url
    app.logger.info(f"监控服务URL: {monitor_service_url}")

    # 检查是否需要首次设置管理员账户
    app.needs_first_setup = not unified_config.has_web_auth_configured()
    if app.needs_first_setup:
        app.logger.warning("[WARN] 管理员账户尚未设置，首次访问将引导到设置页面")

    # 创建简化的配置适配器
    app.config_manager = SimpleConfigAdapter(unified_config)


def init_logging(app):
    """初始化日志配置"""
    setup_logging_replacement(app)


def init_services(app):
    """初始化各种服务 - 简化版"""
    # 初始化调度器
    app.scheduler = Scheduler(logger=app.logger)

    # 初始化后台事件循环和通知服务
    try:
        import threading
        import asyncio
        from core.context import app_context

        # 创建后台事件循环
        def create_background_loop():
            """创建后台事件循环"""
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            app_context['background_event_loop'] = loop
            app_context['stop_event'] = threading.Event()

            try:
                loop.run_forever()
            except Exception as e:
                app.logger.error(f"后台事件循环异常: {str(e)}")
            finally:
                loop.close()

        # 在后台线程中启动事件循环
        background_thread = threading.Thread(
            target=create_background_loop,
            name="BackgroundEventLoop",
            daemon=True
        )
        background_thread.start()

        # 等待事件循环启动
        import time
        max_wait = 5
        wait_count = 0
        while not app_context.get('background_event_loop') and wait_count < max_wait:
            time.sleep(0.1)
            wait_count += 1

        if not app_context.get('background_event_loop'):
            raise RuntimeError("后台事件循环启动失败")

        # 初始化通知服务
        from services.notification.client import NotificationService
        app.notification_service = NotificationService(
            config_manager=app.config_manager,
            logger=app.logger
        )
        app.logger.info("通知服务和后台事件循环初始化成功")

        # 现在用正确的通知服务初始化设备过期检查器
        app.device_expiry_checker = DeviceExpiryChecker(
            app.config_manager,
            app.logger,
            app.notification_service  # 传入正确初始化的通知服务
        )
        app.logger.info("设备过期检查器初始化成功")

    except Exception as e:
        app.logger.error(f"通知服务初始化失败: {str(e)}")
        app.notification_service = None

        # 如果通知服务初始化失败，仍然初始化设备过期检查器（但不能发送通知）
        app.device_expiry_checker = DeviceExpiryChecker(
            app.config_manager,
            app.logger,
            None  # 通知服务初始化失败时传入None
        )
        app.logger.warning("设备过期检查器已初始化，但通知功能不可用")

    # 从配置加载调度任务
    schedule_config = app.config_manager.get('schedule', {})

    # 创建简化的监控操作函数
    def create_monitor_action(flask_app):
        """创建一个简化的监控操作函数，通过HTTP API控制监控服务"""
        def monitor_action(action):
            from web.clients.monitor_client_simple import simple_start_monitor, simple_stop_monitor

            if action == "start":
                flask_app.logger.info("定时任务：尝试启动监控服务")
                try:
                    success, data = simple_start_monitor(flask_app.logger)
                    if success:
                        flask_app.logger.info("定时任务：监控服务启动成功")
                    else:
                        flask_app.logger.warning(f"定时任务：监控服务启动失败 - {data.get('error', '未知错误')}")
                except Exception as e:
                    flask_app.logger.error(f"定时任务：启动监控服务时出错 - {str(e)}")

            elif action == "stop":
                flask_app.logger.info("定时任务：尝试停止监控服务")
                try:
                    success, data = simple_stop_monitor(flask_app.logger)
                    if success:
                        flask_app.logger.info("定时任务：监控服务停止成功")
                    else:
                        flask_app.logger.warning(f"定时任务：监控服务停止失败 - {data.get('error', '未知错误')}")
                except Exception as e:
                    flask_app.logger.error(f"定时任务：停止监控服务时出错 - {str(e)}")

        return monitor_action

    monitor_action = create_monitor_action(app)
    app.monitor_action = monitor_action

    # 加载调度任务
    app.scheduler.from_dict(schedule_config, app.monitor_action)

    # 启动调度器和设备过期检查器
    app.scheduler.start()
    app.device_expiry_checker.start()

    # 初始化自动Cookie延长管理器（简化版）
    def init_auto_login_manager():
        """延迟初始化自动登录管理器"""
        try:
            with app.app_context():
                from web.api.grab_devices import initialize_auto_login_manager
                success = initialize_auto_login_manager()
                if success:
                    app.logger.info("[OK] 自动Cookie延长管理器初始化成功")
                else:
                    app.logger.info("[INFO] 自动Cookie延长管理器初始化完成（无需启动）")
        except Exception as e:
            app.logger.error(f"[ERROR] 自动Cookie延长管理器初始化失败: {e}")

    # 使用线程延迟执行，避免阻塞应用启动
    import threading
    auto_login_thread = threading.Thread(
        target=init_auto_login_manager,
        name="AutoLoginManagerInit",
        daemon=True
    )
    auto_login_thread.start()


def init_blueprints(app):
    """注册所有Blueprint"""
    # 注册认证Blueprint
    app.register_blueprint(auth_bp)

    # 注册视图Blueprint
    app.register_blueprint(views_bp)

    # 注册API Blueprint
    app.register_blueprint(monitor_api, url_prefix='/api')
    app.register_blueprint(config_api, url_prefix='/api')
    app.register_blueprint(devices_api, url_prefix='/api')
    app.register_blueprint(logs_api, url_prefix='/api')
    app.register_blueprint(schedule_api, url_prefix='/api/schedule')
    app.register_blueprint(notification_api, url_prefix='/api')
    app.register_blueprint(houses_api, url_prefix='/api')
    app.register_blueprint(grab_devices_api, url_prefix='/api')

    # 注册全局异常处理器
    register_error_handlers(app)

    # 注册全局请求处理器
    register_request_handlers(app)


def register_error_handlers(app):
    """注册全局异常处理器 - 简化版"""

    @app.errorhandler(AppException)
    def handle_app_exception(e):
        """处理应用异常"""
        app.logger.error(f"应用异常: {e}")
        return jsonify(e.to_dict()), 400

    @app.errorhandler(Exception)
    def handle_generic_exception(e):
        """处理通用异常"""
        app.logger.error(f"未处理的异常: {str(e)}")
        return jsonify({
            'error': '服务器内部错误',
            'details': str(e)
        }), 500


def register_request_handlers(app):
    """注册全局请求处理器"""
    from flask import request, redirect, url_for, session

    @app.before_request
    def check_first_setup():
        """检查是否需要首次设置"""
        # 跳过静态文件和首次设置相关的路由
        if (request.endpoint and
            (request.endpoint.startswith('static') or
             request.endpoint in ['auth.first_setup', 'auth.login'])):
            return

        # 如果需要首次设置且用户未登录，重定向到首次设置页面
        if (hasattr(app, 'needs_first_setup') and
            app.needs_first_setup and
            not session.get('logged_in')):
            return redirect(url_for('auth.first_setup'))


def main():
    """主函数 - 简化版"""
    # 打印环境信息
    print("="*50)
    print("青城住房监控系统 - Web控制台 v2.0")
    print("="*50)
    print("系统环境信息:")
    print(f"操作系统: {os.name}")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print("="*50)
    print("正在启动Web服务器...")

    # 创建应用
    app = create_app()

    # 检查监控服务连接
    try:
        from web.clients.monitor_client_simple import simple_health_check
        is_healthy = simple_health_check(app.logger)
        if is_healthy:
            app.logger.info("[OK] 监控服务连接正常")
        else:
            app.logger.warning("[WARN] 监控服务连接失败，请确保监控服务正在运行")
            app.logger.info(f"监控服务地址: {app.monitor_service_url}")
    except Exception as e:
        app.logger.warning(f"[WARN] 无法连接到监控服务: {str(e)}")
        app.logger.info(f"监控服务地址: {app.monitor_service_url}")

    # 从统一配置获取Web服务参数
    web_config = app.unified_config.get_web_service_config()
    host = web_config.get('host', '0.0.0.0')
    port = web_config.get('port', 50010)

    app.logger.info(f"Web服务将在 {host}:{port} 启动")

    try:
        app.run(host=host, port=port, debug=False, use_reloader=False)
    except KeyboardInterrupt:
        app.logger.info("Web服务器被用户中断")
    except Exception as e:
        app.logger.error(f"Web服务器启动失败: {str(e)}")
    finally:
        # 简化的清理资源
        if hasattr(app, 'scheduler'):
            app.scheduler.stop()
        if hasattr(app, 'device_expiry_checker'):
            app.device_expiry_checker.stop()

        # 清理通知服务和后台事件循环
        try:
            from core.context import app_context

            # 停止后台事件循环
            stop_event = app_context.get('stop_event')
            background_loop = app_context.get('background_event_loop')

            if stop_event:
                stop_event.set()

            if background_loop and not background_loop.is_closed():
                background_loop.call_soon_threadsafe(background_loop.stop)

            # 清理通知服务
            if hasattr(app, 'notification_service') and app.notification_service:
                # 在新的事件循环中关闭通知服务
                import asyncio
                import threading

                def close_notification_service():
                    try:
                        new_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(new_loop)
                        new_loop.run_until_complete(app.notification_service.close())
                        new_loop.close()
                    except Exception as e:
                        app.logger.debug(f"关闭通知服务时出错: {str(e)}")

                cleanup_thread = threading.Thread(target=close_notification_service, daemon=True)
                cleanup_thread.start()
                cleanup_thread.join(timeout=5)

            app.logger.info("通知服务和后台事件循环已清理")

        except Exception as e:
            app.logger.debug(f"清理通知服务时出错: {str(e)}")

        # 关闭监控客户端
        try:
            from web.clients.monitor_client_simple import close_global_simple_client
            close_global_simple_client()
        except Exception as e:
            app.logger.debug(f"关闭监控客户端时出错: {str(e)}")


if __name__ == '__main__':
    main()