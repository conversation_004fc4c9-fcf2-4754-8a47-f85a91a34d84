"""
抢房设备API模块 - 主入口
"""

from flask import Blueprint

# 创建主蓝图
grab_devices_api = Blueprint('grab_devices_api', __name__)

# 导入子模块的蓝图（避免循环导入）
try:
    # 导入基础设备路由模块
    from .device_routes import device_routes_bp
    grab_devices_api.register_blueprint(device_routes_bp, url_prefix='/grab_devices')

    # 导入自动登录路由模块
    from .auto_login_routes import auto_login_routes_bp
    grab_devices_api.register_blueprint(auto_login_routes_bp)

    # 导入辅助路由模块（OCR状态检查等）
    from .routes import grab_devices_bp
    grab_devices_api.register_blueprint(grab_devices_bp)

    #print("✅ grab_devices模块导入成功")
except Exception as e:
    print(f" grab_devices模块导入失败: {e}")
    # 创建一个临时的错误处理路由
    @grab_devices_api.route('/grab_devices')
    def temporary_error():
        return {"error": "抢房设备模块暂时不可用，正在修复中"}, 503

# 导出需要的类和函数
from .auto_login_manager import AutoLoginManager
from .login_helper import HuhhothomeLoginHelper
from .cookie_tester import CookieTester, _check_profile_exists, _get_profile_data, _save_profile_data

# 全局自动登录管理器实例
auto_login_manager = None

def get_auto_login_manager():
    """获取全局自动登录管理器实例"""
    global auto_login_manager

    if auto_login_manager is None:
        try:
            from flask import current_app
            data_manager = current_app.config_manager.get_data_manager()
            logger = current_app.logger
            flask_app = current_app._get_current_object()  # 获取实际的Flask应用实例
            auto_login_manager = AutoLoginManager(data_manager, logger, flask_app)
        except Exception as e:
            from flask import current_app
            current_app.logger.error(f"创建自动登录管理器失败: {e}")
            return None

    return auto_login_manager

def initialize_auto_login_manager():
    """初始化自动登录管理器 - 在应用启动时调用"""
    try:
        from flask import current_app
        from datetime import datetime

        with current_app.app_context():
            data_manager = current_app.config_manager.get_data_manager()
            grab_device_model = getattr(data_manager, 'grab_device_model', None)

            if not grab_device_model:
                current_app.logger.info("未找到抢房设备模型，跳过自动登录管理器初始化")
                return False

            devices = grab_device_model.get_all()
            enabled_devices = [d for d in devices if d.get('enabled')]

            if not enabled_devices:
                current_app.logger.info("没有启用的设备，跳过自动登录管理器初始化")
                return True

            # 只要有启用的设备就启动自动登录管理器
            current_app.logger.info(f"发现 {len(enabled_devices)} 个启用的设备，启动自动Cookie延长管理器")

            # 批量显示设备状态信息，避免重复创建登录助手
            login_helper = HuhhothomeLoginHelper()
            device_status_info = []

            for device in enabled_devices:
                username = device.get('username', '未知设备')
                access_token = device.get('access_token')

                if access_token:
                    remaining_hours = login_helper.get_cookie_remaining_hours(access_token)
                    if remaining_hours is not None:
                        remaining_minutes = remaining_hours * 60
                        device_status_info.append(f"{username}: 已登录，Cookie剩余 {remaining_minutes:.1f} 分钟")
                    else:
                        device_status_info.append(f"{username}: 已登录，但无法解析Cookie有效期")
                else:
                    device_status_info.append(f"{username}: 未登录")

            # 合并设备状态信息为一条日志
            if device_status_info:
                current_app.logger.info("设备状态: " + " | ".join(device_status_info))

            # 启动自动登录管理器
            manager = get_auto_login_manager()
            if manager:
                success = manager.start()
                if success:
                    current_app.logger.info("[OK] 自动Cookie延长管理器已启动")
                    return True
                else:
                    current_app.logger.debug("[INFO] 自动Cookie延长管理器已在运行")
                    return True
            else:
                current_app.logger.error("[ERROR] 无法创建自动登录管理器")
                return False

    except Exception as e:
        from flask import current_app
        current_app.logger.error(f"初始化自动登录管理器失败: {e}")
        return False


def auto_manage_login_manager():
    """自动管理自动登录管理器的启动和停止 - 根据启用设备数量决定"""
    try:
        from flask import current_app

        with current_app.app_context():
            data_manager = current_app.config_manager.get_data_manager()
            grab_device_model = getattr(data_manager, 'grab_device_model', None)

            if not grab_device_model:
                return False

            devices = grab_device_model.get_all()
            enabled_devices = [d for d in devices if d.get('enabled')]

            manager = get_auto_login_manager()
            if not manager:
                return False

            if len(enabled_devices) > 0:
                # 有启用的设备，确保管理器正在运行
                if not manager.running:
                    success = manager.start()
                    if success:
                        current_app.logger.info(f"[OK] 检测到 {len(enabled_devices)} 个启用设备，自动启动Cookie延长管理器")
                    return success
                else:
                    current_app.logger.debug(f"自动登录管理器已在运行，当前管理 {len(enabled_devices)} 个启用设备")
                    return True
            else:
                # 没有启用的设备，停止管理器
                if manager.running:
                    success = manager.stop()
                    if success:
                        current_app.logger.info("🛑 没有启用的设备，自动停止Cookie延长管理器")
                    return success
                else:
                    current_app.logger.debug("没有启用的设备，自动登录管理器已停止")
                    return True

    except Exception as e:
        from flask import current_app
        current_app.logger.error(f"自动管理登录管理器失败: {e}")
        return False