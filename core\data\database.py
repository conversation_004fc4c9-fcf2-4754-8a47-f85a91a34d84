import sqlite3
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
import os

# 导入统一异常处理框架
from core.exceptions import handle_exception, DatabaseException


class DatabaseManager:
    def __init__(self, db_path: str = "monitoring.db"):
        self.db_path = db_path
        self.init_database()

    def get_connection(self):
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=30.0,
                check_same_thread=False,
                isolation_level='DEFERRED'  # 使用延迟事务模式
            )
            # 设置WAL模式，提高并发性能
            conn.execute("PRAGMA journal_mode=WAL")
            # 开启外键约束以确保数据完整性
            conn.execute("PRAGMA foreign_keys = ON")
            # 设置较短的忙碌超时
            conn.execute("PRAGMA busy_timeout=5000")
            return conn
        except sqlite3.Error as e:
            raise handle_exception(e, context=f"数据库连接失败: {self.db_path}")

    def transaction(self):
        """事务上下文管理器，用于原子操作"""
        from contextlib import contextmanager

        @contextmanager
        def _transaction():
            conn = None
            try:
                conn = self.get_connection()
                yield conn
                conn.commit()
            except sqlite3.Error as e:
                if conn:
                    conn.rollback()
                raise handle_exception(e, context="数据库事务执行失败")
            except Exception as e:
                if conn:
                    conn.rollback()
                raise handle_exception(e, context="事务执行过程中发生未知错误")
            finally:
                if conn:
                    conn.close()

        return _transaction()

    def init_database(self):
        """初始化数据库表结构"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 配置表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS config (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 设备表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS devices (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        type TEXT NOT NULL,
                        bark_key TEXT,
                        uid TEXT,
                        pushme_key TEXT,
                        params TEXT,  -- JSON字符串
                        expire_date TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 监控配置表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS monitor_configs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL UNIQUE,
                        enabled BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 监控配置设备关联表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS monitor_config_devices (
                        monitor_config_id INTEGER,
                        device_id TEXT,
                        monitor_config_name TEXT,
                        device_name TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (monitor_config_id, device_id),
                        FOREIGN KEY (monitor_config_id) REFERENCES monitor_configs(id),
                        FOREIGN KEY (device_id) REFERENCES devices(id)
                    )
                """)

                # 房产表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS estates (
                        name TEXT PRIMARY KEY,
                        estate_id TEXT,
                        history_count INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 已知房屋表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS known_houses (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        estate_name TEXT NOT NULL,
                        house_id TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (estate_name) REFERENCES estates(name)
                    )
                """)

                # 历史更新记录表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS update_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 房源详情表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS house_details (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        house_id TEXT NOT NULL,        -- 房源ID（用于抢房）
                        estate_name TEXT NOT NULL,     -- 小区名称
                        name TEXT,                     -- 房源名称
                        position TEXT,                 -- 位置信息（楼号、单元、层数）
                        roomno TEXT,                   -- 房间号
                        outtype TEXT,                  -- 租赁类型（公租房、人才公寓等）
                        direction TEXT,                -- 朝向（南向、北向、东南向等）
                        area REAL,                     -- 面积（平方米）
                        rent REAL,                     -- 租金（元/月）
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (estate_name) REFERENCES estates(name),
                        UNIQUE(house_id, estate_name)
                    )
                """)

                # 抢房设备表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS grab_devices (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT NOT NULL UNIQUE,
                        phone TEXT NOT NULL,
                        cookie TEXT,
                        access_token TEXT,
                        sessioncode TEXT,
                        cookie_expires_at TIMESTAMP,
                        last_login_at TIMESTAMP,
                        target_estate TEXT,
                        house_id TEXT,
                        enabled BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 抢房条件表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS grab_conditions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        grab_device_id INTEGER,
                        condition_type TEXT NOT NULL,  -- 条件类型：area(面积), direction(朝向), building(楼号), floor(楼层), roomno(房间号), outtype(租赁类型), rent(租金)
                        condition_value TEXT NOT NULL, -- 条件值：>50, 南向, 1号楼, 301, 公租房等
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (grab_device_id) REFERENCES grab_devices(id) ON DELETE CASCADE
                    )
                """)

                # 添加索引以优化查询性能
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_house_details_estate ON house_details(estate_name)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_house_details_house_id ON house_details(house_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_known_houses_estate ON known_houses(estate_name)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_estates_name ON estates(name)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_grab_devices_estate ON grab_devices(target_estate)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_grab_devices_enabled ON grab_devices(enabled)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_config_key ON config(key)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_monitor_configs_enabled ON monitor_configs(enabled)")
                # 复合索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_monitor_device_composite ON monitor_config_devices(monitor_config_id, device_id)")

                # 表结构升级检查
                self._upgrade_database_schema(cursor)

                # 数据迁移：填充现有关联关系的名称字段
                self._migrate_monitor_config_device_names(cursor)

                conn.commit()
        except Exception as e:
            raise handle_exception(e, context="数据库初始化失败")

    def _upgrade_database_schema(self, cursor):
        """数据库表结构升级"""
        try:
            # 检查数据库版本，避免重复升级
            current_version = self._get_db_version(cursor)
            target_version = 2  # 当前目标版本

            if current_version >= target_version:
                # 即使版本足够，也要检查sessioncode字段
                cursor.execute("PRAGMA table_info(grab_devices)")
                grab_columns = {row[1] for row in cursor.fetchall()}

                if 'sessioncode' not in grab_columns:
                    cursor.execute("ALTER TABLE grab_devices ADD COLUMN sessioncode TEXT")
                    print("✅ 数据库升级: 给grab_devices表添加sessioncode字段")

                return  # 已经是最新版本，无需其他升级

            print(f"检测到数据库需要升级：从版本 {current_version} 升级到版本 {target_version}")

            # 检查monitor_config_devices表是否需要升级
            cursor.execute("PRAGMA table_info(monitor_config_devices)")
            columns = [row[1] for row in cursor.fetchall()]  # 获取所有列名

            upgrade_needed = False

            # 检查并添加缺失的字段
            if 'monitor_config_name' not in columns:
                print("正在升级数据库：添加monitor_config_name字段...")
                cursor.execute("ALTER TABLE monitor_config_devices ADD COLUMN monitor_config_name TEXT")
                print("monitor_config_name字段添加成功")
                upgrade_needed = True

            if 'device_name' not in columns:
                print("正在升级数据库：添加device_name字段...")
                cursor.execute("ALTER TABLE monitor_config_devices ADD COLUMN device_name TEXT")
                print("device_name字段添加成功")
                upgrade_needed = True

            if 'created_at' not in columns:
                print("正在升级数据库：添加created_at字段...")
                # SQLite不支持带有CURRENT_TIMESTAMP的ALTER TABLE，使用NULL默认值
                cursor.execute("ALTER TABLE monitor_config_devices ADD COLUMN created_at TIMESTAMP")
                print("created_at字段添加成功")
                upgrade_needed = True

            if 'updated_at' not in columns:
                print("正在升级数据库：添加updated_at字段...")
                # SQLite不支持带有CURRENT_TIMESTAMP的ALTER TABLE，使用NULL默认值
                cursor.execute("ALTER TABLE monitor_config_devices ADD COLUMN updated_at TIMESTAMP")
                print("updated_at字段添加成功")
                upgrade_needed = True

            # 检查grab_devices表的sessioncode字段
            cursor.execute("PRAGMA table_info(grab_devices)")
            grab_columns = {row[1] for row in cursor.fetchall()}

            if 'sessioncode' not in grab_columns:
                cursor.execute("ALTER TABLE grab_devices ADD COLUMN sessioncode TEXT")
                print("✅ 数据库升级: 给grab_devices表添加sessioncode字段")
                upgrade_needed = True

            if upgrade_needed:
                # 为现有记录设置时间戳
                cursor.execute("""
                    UPDATE monitor_config_devices
                    SET created_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                    WHERE created_at IS NULL OR updated_at IS NULL
                """)
                print("已为现有记录设置时间戳")

                # 更新数据库版本
                self._set_db_version(cursor, target_version)
                print(f"数据库升级完成：版本 {current_version} → {target_version}")

        except sqlite3.Error as e:
            raise handle_exception(e, context="数据库表结构升级失败")
        except Exception as e:
            print(f"数据库表结构升级失败: {e}")
            # 升级失败不应该阻止应用启动

    def _get_db_version(self, cursor):
        """获取数据库版本"""
        try:
            cursor.execute("SELECT value FROM config WHERE key = 'db_version'")
            result = cursor.fetchone()
            return int(result[0]) if result else 1
        except (sqlite3.Error, ValueError, TypeError):
            return 1

    def _set_db_version(self, cursor, version):
        """设置数据库版本"""
        try:
            cursor.execute("""
                INSERT OR REPLACE INTO config (key, value, updated_at)
                VALUES ('db_version', ?, CURRENT_TIMESTAMP)
            """, (str(version),))
        except sqlite3.Error as e:
            raise handle_exception(e, context=f"设置数据库版本失败: {version}")

    def _migrate_monitor_config_device_names(self, cursor):
        """迁移现有关联关系数据，填充名称字段"""
        try:
            # 检查表是否有必要的字段
            cursor.execute("PRAGMA table_info(monitor_config_devices)")
            columns = [row[1] for row in cursor.fetchall()]

            if 'monitor_config_name' not in columns or 'device_name' not in columns:
                print("跳过数据迁移：缺少必要字段")
                return

            # 检查是否需要迁移（查看是否有记录缺少名称字段）
            cursor.execute("""
                SELECT COUNT(*) FROM monitor_config_devices
                WHERE monitor_config_name IS NULL OR device_name IS NULL
            """)
            need_migration_count = cursor.fetchone()[0]

            if need_migration_count > 0:
                print(f"正在迁移 {need_migration_count} 条关联关系数据...")

                # 更新所有缺少名称的记录
                cursor.execute("""
                    UPDATE monitor_config_devices
                    SET monitor_config_name = (
                        SELECT mc.name FROM monitor_configs mc
                        WHERE mc.id = monitor_config_devices.monitor_config_id
                    ),
                    device_name = (
                        SELECT d.name FROM devices d
                        WHERE d.id = monitor_config_devices.device_id
                    ),
                    updated_at = CURRENT_TIMESTAMP
                    WHERE monitor_config_name IS NULL OR device_name IS NULL
                """)

                updated_rows = cursor.rowcount
                print(f"已成功迁移 {updated_rows} 条关联关系数据")

        except sqlite3.Error as e:
            raise handle_exception(e, context="数据迁移失败")
        except Exception as e:
            print(f"数据迁移失败: {e}")
            # 迁移失败不应该阻止应用启动


class ConfigModel:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def get(self, key: str, default=None):
        """获取配置值"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT value FROM config WHERE key = ?", (key,))
                result = cursor.fetchone()
                if result:
                    try:
                        return json.loads(result[0])
                    except json.JSONDecodeError:
                        return result[0]
                return default
        except Exception as e:
            raise handle_exception(e, context=f"获取配置失败: {key}")

    def set(self, key: str, value: Any):
        """设置配置值"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                value_str = json.dumps(value) if not isinstance(value, str) else value
                cursor.execute("""
                    INSERT OR REPLACE INTO config (key, value, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                """, (key, value_str))
                conn.commit()
        except Exception as e:
            raise handle_exception(e, context=f"设置配置失败: {key}={value}")

    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT key, value FROM config")
                result = {}
                for key, value in cursor.fetchall():
                    try:
                        result[key] = json.loads(value)
                    except json.JSONDecodeError:
                        result[key] = value
                return result
        except Exception as e:
            raise handle_exception(e, context="获取所有配置失败")


class DeviceModel:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def get_all(self) -> List[Dict[str, Any]]:
        """获取所有设备"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, name, type, bark_key, uid, pushme_key, params, expire_date
                    FROM devices ORDER BY name
                """)
                devices = []
                for row in cursor.fetchall():
                    device = {
                        'id': row[0],
                        'name': row[1],
                        'type': row[2],
                        'bark_key': row[3],
                        'uid': row[4],
                        'pushme_key': row[5],
                        'expire_date': row[7]
                    }
                    if row[6]:  # params
                        try:
                            device['params'] = json.loads(row[6])
                        except json.JSONDecodeError:
                            device['params'] = {}
                    devices.append(device)
                return devices
        except Exception as e:
            raise handle_exception(e, context="获取所有设备失败")

    def get_by_id(self, device_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取设备"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, name, type, bark_key, uid, pushme_key, params, expire_date
                    FROM devices WHERE id = ?
                """, (device_id,))
                row = cursor.fetchone()
                if row:
                    device = {
                        'id': row[0],
                        'name': row[1],
                        'type': row[2],
                        'bark_key': row[3],
                        'uid': row[4],
                        'pushme_key': row[5],
                        'expire_date': row[7]
                    }
                    if row[6]:  # params
                        try:
                            device['params'] = json.loads(row[6])
                        except json.JSONDecodeError:
                            device['params'] = {}
                    return device
            return None
        except Exception as e:
            raise handle_exception(e, context=f"根据ID获取设备失败: {device_id}")

    def create_or_update(self, device_data: Dict[str, Any]):
        """创建或更新设备"""
        try:
            with self.db.transaction() as conn:  # 使用事务保护
                cursor = conn.cursor()
                params_json = json.dumps(device_data.get('params', {}))
                cursor.execute("""
                    INSERT OR REPLACE INTO devices
                    (id, name, type, bark_key, uid, pushme_key, params, expire_date, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (
                    device_data['id'],
                    device_data['name'],
                    device_data['type'],
                    device_data.get('bark_key'),
                    device_data.get('uid'),
                    device_data.get('pushme_key'),
                    params_json,
                    device_data.get('expire_date')
                ))
        except Exception as e:
            raise handle_exception(e, context=f"创建或更新设备失败: {device_data.get('id', '未知')}")

    def delete(self, device_id: str):
        """删除设备"""
        try:
            with self.db.transaction() as conn:  # 使用事务保护
                cursor = conn.cursor()
                # 先删除关联关系
                cursor.execute("DELETE FROM monitor_config_devices WHERE device_id = ?", (device_id,))
                # 再删除设备
                cursor.execute("DELETE FROM devices WHERE id = ?", (device_id,))
        except Exception as e:
            raise handle_exception(e, context=f"删除设备失败: {device_id}")


class MonitorConfigModel:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def get_all(self) -> List[Dict[str, Any]]:
        """获取所有监控配置"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, name, enabled FROM monitor_configs ORDER BY name
                """)
                configs = []
                for row in cursor.fetchall():
                    config = {
                        'id': row[0],
                        'name': row[1],
                        'enabled': bool(row[2]),
                        'device_ids': self._get_device_ids(row[0])
                    }
                    configs.append(config)
                return configs
        except Exception as e:
            raise handle_exception(e, context="获取所有监控配置失败")

    def _get_device_ids(self, config_id: int) -> List[str]:
        """获取监控配置关联的设备ID列表"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT device_id FROM monitor_config_devices WHERE monitor_config_id = ?
                """, (config_id,))
                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            # 这里使用警告级别，因为这是辅助查询
            print(f"警告：获取监控配置设备ID失败 {config_id}: {e}")
            return []

    def create_or_update(self, config_data: Dict[str, Any]):
        """创建或更新监控配置"""
        try:
            with self.db.transaction() as conn:  # 使用统一事务管理
                cursor = conn.cursor()

                # 获取或创建配置
                cursor.execute("SELECT id FROM monitor_configs WHERE name = ?", (config_data['name'],))
                result = cursor.fetchone()

                if result:
                    config_id = result[0]
                    cursor.execute("""
                        UPDATE monitor_configs
                        SET enabled = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """, (config_data['enabled'], config_id))
                else:
                    cursor.execute("""
                        INSERT INTO monitor_configs (name, enabled) VALUES (?, ?)
                    """, (config_data['name'], config_data['enabled']))
                    config_id = cursor.lastrowid

                # 更新设备关联（包含名称信息）
                cursor.execute("DELETE FROM monitor_config_devices WHERE monitor_config_id = ?", (config_id,))

                # 获取设备名称信息
                for device_id in config_data.get('device_ids', []):
                    # 查询设备名称
                    cursor.execute("SELECT name FROM devices WHERE id = ?", (device_id,))
                    device_result = cursor.fetchone()
                    device_name = device_result[0] if device_result else device_id

                    cursor.execute("""
                        INSERT INTO monitor_config_devices
                        (monitor_config_id, device_id, monitor_config_name, device_name, updated_at)
                        VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                    """, (config_id, device_id, config_data['name'], device_name))
        except Exception as e:
            raise handle_exception(e, context=f"创建或更新监控配置失败: {config_data.get('name', 'Unknown')}")

    def get_device_house_relations(self) -> List[Dict[str, Any]]:
        """获取设备和房源的详细关联关系"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()

            # 查询所有关联关系，包括名称信息
            cursor.execute("""
                SELECT
                    mcd.monitor_config_id,
                    mcd.device_id,
                    mcd.monitor_config_name,
                    mcd.device_name,
                    mc.enabled as house_enabled,
                    d.type as device_type,
                    d.expire_date
                FROM monitor_config_devices mcd
                LEFT JOIN monitor_configs mc ON mcd.monitor_config_id = mc.id
                LEFT JOIN devices d ON mcd.device_id = d.id
                ORDER BY mcd.monitor_config_name, mcd.device_name
            """)

            relations = []
            for row in cursor.fetchall():
                relations.append({
                    'monitor_config_id': row[0],
                    'device_id': row[1],
                    'house_name': row[2],
                    'device_name': row[3],
                    'house_enabled': bool(row[4]) if row[4] is not None else False,
                    'device_type': row[5],
                    'device_expire_date': row[6]
                })

            return relations

    def get_orphaned_devices(self) -> List[Dict[str, Any]]:
        """获取孤立设备（未关联任何房源的设备）"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute("""
                SELECT d.id, d.name, d.type, d.expire_date
                FROM devices d
                LEFT JOIN monitor_config_devices mcd ON d.id = mcd.device_id
                WHERE mcd.device_id IS NULL
                ORDER BY d.name
            """)

            orphaned_devices = []
            for row in cursor.fetchall():
                orphaned_devices.append({
                    'id': row[0],
                    'name': row[1],
                    'type': row[2],
                    'expire_date': row[3]
                })

            return orphaned_devices


class EstateModel:
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def get_all_history(self) -> Dict[str, int]:
        """获取所有房产的历史计数"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name, history_count FROM estates")
            return {row[0]: row[1] for row in cursor.fetchall()}

    def get_all_estate_ids(self) -> Dict[str, str]:
        """获取所有房产ID映射"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name, estate_id FROM estates")
            return {row[0]: row[1] for row in cursor.fetchall()}

    def get_known_houses(self) -> Dict[str, List[str]]:
        """获取已知房屋映射"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT estate_name, house_id FROM known_houses ORDER BY estate_name
            """)
            result = {}
            for estate_name, house_id in cursor.fetchall():
                if estate_name not in result:
                    result[estate_name] = []
                result[estate_name].append(house_id)
            return result

    def update_history(self, name: str, count: int):
        """更新房产历史计数（使用事务保证原子性）"""
        with self.db.transaction() as conn:
            cursor = conn.cursor()
            # 先检查是否已存在记录
            cursor.execute("SELECT estate_id FROM estates WHERE name = ?", (name,))
            existing = cursor.fetchone()

            if existing:
                # 更新现有记录，保留原有的estate_id
                cursor.execute("""
                    UPDATE estates
                    SET history_count = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE name = ?
                """, (count, name))
            else:
                # 插入新记录，estate_id为空
                cursor.execute("""
                    INSERT INTO estates (name, history_count, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                """, (name, count))

    def set_estate_id(self, name: str, estate_id: str):
        """设置房产ID"""
        try:
            with self.db.transaction() as conn:  # 使用事务保护多步操作
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR IGNORE INTO estates (name, estate_id) VALUES (?, ?)
                """, (name, estate_id))
                cursor.execute("""
                    UPDATE estates SET estate_id = ?, updated_at = CURRENT_TIMESTAMP WHERE name = ?
                """, (estate_id, name))
        except Exception as e:
            raise handle_exception(e, context=f"设置房产ID失败: {name} -> {estate_id}")

    def add_known_house(self, estate_name: str, house_id: str):
        """添加一个已知的房屋ID"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            # 确保房产存在
            cursor.execute("INSERT OR IGNORE INTO estates (name) VALUES (?)", (estate_name,))
            cursor.execute("INSERT INTO known_houses (estate_name, house_id) VALUES (?, ?)", (estate_name, house_id))
            conn.commit()

    def set_known_houses(self, estate_name: str, house_ids: List[str]):
        """设置一个房产的已知房屋ID列表（先清空后添加）"""
        try:
            with self.db.transaction() as conn:  # 使用事务保护删除-插入操作
                cursor = conn.cursor()
                # 确保房产存在
                cursor.execute("INSERT OR IGNORE INTO estates (name) VALUES (?)", (estate_name,))
                # 删除旧的记录
                cursor.execute("DELETE FROM known_houses WHERE estate_name = ?", (estate_name,))
                # 插入新的记录
                if house_ids:
                    cursor.executemany(
                        "INSERT INTO known_houses (estate_name, house_id) VALUES (?, ?)",
                        [(estate_name, house_id) for house_id in house_ids]
                    )
        except Exception as e:
            raise handle_exception(e, context=f"设置已知房屋列表失败: {estate_name}, 数量: {len(house_ids) if house_ids else 0}")

    def get_last_update(self) -> Optional[str]:
        """获取最后更新时间"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT last_update FROM update_history ORDER BY last_update DESC LIMIT 1")
            result = cursor.fetchone()
            return result[0] if result else None

    def update_last_update(self):
        """更新最后更新时间"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("INSERT INTO update_history (last_update) VALUES (CURRENT_TIMESTAMP)")
            conn.commit()

    def get_all_names(self) -> List[str]:
        """获取所有小区名称列表"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT name FROM estates WHERE name IS NOT NULL AND name != '' ORDER BY name")
            return [row[0] for row in cursor.fetchall()]

    def add_estate(self, name: str):
        """添加新小区"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("INSERT OR IGNORE INTO estates (name) VALUES (?)", (name,))
            conn.commit()


class HouseDetailModel:
    """房源详情数据模型"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def save_house_detail(self, estate_name: str, house_detail: Dict[str, Any]):
        """保存或更新房源详情"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO house_details
                (house_id, estate_name, name, position, roomno, outtype, direction, area, rent, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                house_detail['id'],
                estate_name,
                house_detail.get('name', '未知'),
                house_detail.get('position', '未知'),
                house_detail.get('roomno', '未知'),
                house_detail.get('outtype', '未知'),
                house_detail.get('direction', '未知'),
                float(house_detail.get('area', 0)),
                float(house_detail.get('rent', 0))
            ))
            conn.commit()

    def save_house_details(self, estate_name: str, house_details: List[Dict[str, Any]]):
        """批量保存房源详情"""
        try:
            with self.db.transaction() as conn:  # 使用事务保护批量操作
                cursor = conn.cursor()
                for house_detail in house_details:
                    cursor.execute("""
                        INSERT OR REPLACE INTO house_details
                        (house_id, estate_name, name, position, roomno, outtype, direction, area, rent, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    """, (
                        house_detail['id'],
                        estate_name,
                        house_detail.get('name', '未知'),
                        house_detail.get('position', '未知'),
                        house_detail.get('roomno', '未知'),
                        house_detail.get('outtype', '未知'),
                        house_detail.get('direction', '未知'),
                        float(house_detail.get('area', 0)),
                        float(house_detail.get('rent', 0))
                    ))
        except Exception as e:
            raise handle_exception(e, context=f"批量保存房源详情失败: {estate_name}, 数量: {len(house_details)}")

    def get_house_details(self, estate_name: str) -> List[Dict[str, Any]]:
        """获取指定房源的所有详情"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT house_id, name, position, roomno, outtype, direction, area, rent
                FROM house_details
                WHERE estate_name = ?
                ORDER BY rent ASC
            """, (estate_name,))

            details = []
            for row in cursor.fetchall():
                details.append({
                    'id': row[0],
                    'name': row[1],
                    'position': row[2],
                    'roomno': row[3],
                    'outtype': row[4],
                    'direction': row[5],
                    'area': row[6],
                    'rent': row[7]
                })
            return details

    def get_house_detail(self, estate_name: str, house_id: str) -> Optional[Dict[str, Any]]:
        """获取指定房源的详情"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT house_id, name, position, roomno, outtype, direction, area, rent
                FROM house_details
                WHERE estate_name = ? AND house_id = ?
            """, (estate_name, house_id))

            row = cursor.fetchone()
            if row:
                return {
                    'id': row[0],
                    'name': row[1],
                    'position': row[2],
                    'roomno': row[3],
                    'outtype': row[4],
                    'direction': row[5],
                    'area': row[6],
                    'rent': row[7]
                }
            return None

    def delete_house_details(self, estate_name: str):
        """删除指定房源的所有详情"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM house_details WHERE estate_name = ?", (estate_name,))
            conn.commit()


class GrabDeviceModel:
    """抢房设备数据模型"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager

    def get_all(self) -> List[Dict[str, Any]]:
        """获取所有抢房设备"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, username, phone, cookie, access_token, sessioncode, cookie_expires_at,
                       last_login_at, target_estate, house_id, enabled,
                       created_at, updated_at
                FROM grab_devices ORDER BY created_at DESC
            """)
            devices = []
            for row in cursor.fetchall():
                device = {
                    'id': row[0],
                    'username': row[1],
                    'phone': row[2],
                    'cookie': row[3],
                    'access_token': row[4],
                    'sessioncode': row[5],
                    'cookie_expires_at': row[6],
                    'last_login_at': row[7],
                    'target_estate': row[8],
                    'house_id': row[9],
                    'enabled': bool(row[10]),
                    'created_at': row[11],
                    'updated_at': row[12]
                }
                # 获取抢房条件
                device['conditions'] = self._get_conditions(device['id'])
                devices.append(device)
            return devices

    def get_by_id(self, device_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取抢房设备"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, username, phone, cookie, access_token, sessioncode, cookie_expires_at,
                       last_login_at, target_estate, house_id, enabled,
                       created_at, updated_at
                FROM grab_devices WHERE id = ?
            """, (device_id,))
            row = cursor.fetchone()
            if row:
                device = {
                    'id': row[0],
                    'username': row[1],
                    'phone': row[2],
                    'cookie': row[3],
                    'access_token': row[4],
                    'sessioncode': row[5],
                    'cookie_expires_at': row[6],
                    'last_login_at': row[7],
                    'target_estate': row[8],
                    'house_id': row[9],
                    'enabled': bool(row[10]),
                    'created_at': row[11],
                    'updated_at': row[12]
                }
                # 获取抢房条件
                device['conditions'] = self._get_conditions(device['id'])
                return device
            return None

    def get_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """根据用户名获取抢房设备"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, username, phone, cookie, access_token, sessioncode, cookie_expires_at,
                       last_login_at, target_estate, house_id, enabled,
                       created_at, updated_at
                FROM grab_devices WHERE username = ?
            """, (username,))
            row = cursor.fetchone()
            if row:
                device = {
                    'id': row[0],
                    'username': row[1],
                    'phone': row[2],
                    'cookie': row[3],
                    'access_token': row[4],
                    'sessioncode': row[5],
                    'cookie_expires_at': row[6],
                    'last_login_at': row[7],
                    'target_estate': row[8],
                    'house_id': row[9],
                    'enabled': bool(row[10]),
                    'created_at': row[11],
                    'updated_at': row[12]
                }
                # 获取抢房条件
                device['conditions'] = self._get_conditions(device['id'])
                return device
            return None

    def create_or_update(self, device_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建或更新抢房设备"""
        try:
            with self.db.transaction() as conn:  # 使用统一事务管理
                cursor = conn.cursor()

                # 检查用户名是否已存在
                device_id = device_data.get('id')
                if device_id:
                    # 更新现有设备
                    cursor.execute("""
                        UPDATE grab_devices
                        SET username = ?, phone = ?, cookie = ?, access_token = ?, sessioncode = ?,
                            cookie_expires_at = ?, last_login_at = ?, target_estate = ?,
                            house_id = ?, enabled = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """, (
                        device_data['username'], device_data['phone'],
                        device_data.get('cookie', ''), device_data.get('access_token', ''),
                        device_data.get('sessioncode', ''),
                        device_data.get('cookie_expires_at'), device_data.get('last_login_at'),
                        device_data.get('target_estate', ''), device_data.get('house_id', ''),
                        device_data.get('enabled', True), device_id
                    ))
                else:
                    # 创建新设备
                    cursor.execute("""
                        INSERT INTO grab_devices (username, phone, cookie, access_token, sessioncode,
                                                 cookie_expires_at, last_login_at, target_estate, house_id, enabled)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        device_data['username'], device_data['phone'],
                        device_data.get('cookie', ''), device_data.get('access_token', ''),
                        device_data.get('sessioncode', ''),
                        device_data.get('cookie_expires_at'), device_data.get('last_login_at'),
                        device_data.get('target_estate', ''), device_data.get('house_id', ''),
                        device_data.get('enabled', True)
                    ))
                    device_id = cursor.lastrowid

                # 更新条件
                if 'conditions' in device_data:
                    self._update_conditions_with_cursor(cursor, device_id, device_data['conditions'])

            # 在事务外获取完整数据
            return self.get_by_id(device_id)

        except Exception as e:
            raise handle_exception(e, context=f"创建或更新抢房设备失败: {device_data.get('username', 'Unknown')}")

    def delete(self, device_id: int) -> bool:
        """删除抢房设备"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM grab_devices WHERE id = ?", (device_id,))
            conn.commit()
            return cursor.rowcount > 0

    def _get_conditions(self, device_id: int) -> List[Dict[str, Any]]:
        """获取设备的抢房条件"""
        with self.db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, condition_type, condition_value, created_at
                FROM grab_conditions WHERE grab_device_id = ?
                ORDER BY id
            """, (device_id,))
            return [
                {
                    'id': row[0],
                    'type': row[1],
                    'value': row[2],
                    'created_at': row[3]
                }
                for row in cursor.fetchall()
            ]

    def _update_conditions_with_cursor(self, cursor, device_id: int, conditions: List[Dict[str, Any]]):
        """使用现有cursor更新设备的抢房条件"""
        # 删除现有条件
        cursor.execute("DELETE FROM grab_conditions WHERE grab_device_id = ?", (device_id,))

        # 添加新条件
        for condition in conditions:
            cursor.execute("""
                INSERT INTO grab_conditions (grab_device_id, condition_type, condition_value)
                VALUES (?, ?, ?)
            """, (device_id, condition['type'], condition['value']))

    def _update_conditions(self, device_id: int, conditions: List[Dict[str, Any]]):
        """更新设备的抢房条件"""
        try:
            with self.db.transaction() as conn:  # 使用事务保护删除-插入操作
                cursor = conn.cursor()

                # 删除现有条件
                cursor.execute("DELETE FROM grab_conditions WHERE grab_device_id = ?", (device_id,))

                # 添加新条件
                for condition in conditions:
                    cursor.execute("""
                        INSERT INTO grab_conditions (grab_device_id, condition_type, condition_value)
                        VALUES (?, ?, ?)
                    """, (device_id, condition['type'], condition['value']))
        except Exception as e:
            raise handle_exception(e, context=f"更新抢房条件失败: 设备ID {device_id}, 条件数量: {len(conditions)}")