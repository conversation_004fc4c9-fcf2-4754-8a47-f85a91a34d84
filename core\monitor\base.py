"""监控基类定义"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

from ..data.models import MonitorResult


class BaseMonitor(ABC):
    """监控器基类"""
    
    @abstractmethod
    async def monitor_async(self, config: Dict[str, Any]) -> Optional[MonitorResult]:
        """执行异步监控任务
        
        Args:
            config: 监控配置
            
        Returns:
            MonitorResult: 监控结果，如果监控失败返回None
        """
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> tuple[bool, str]:
        """验证监控配置
        
        Args:
            config: 监控配置
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """获取监控器状态（可选实现）
        
        Returns:
            Dict: 状态信息
        """
        return {
            "type": self.__class__.__name__,
            "status": "ready"
        } 