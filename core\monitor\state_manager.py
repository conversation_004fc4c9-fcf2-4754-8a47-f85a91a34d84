"""状态管理器 - 管理监控系统的运行状态"""

import asyncio
import threading
import time
from typing import Dict, Any, Optional, Set
from datetime import datetime

from ..utils.unified_logging import Logger


class StateManager:
    """监控状态管理器"""

    def __init__(self, logger: Optional[Logger] = None):
        self.logger = logger or Logger("StateManager")

        # 运行状态
        self._is_running = False
        self._stop_requested = False
        self._shutdown_in_progress = False
        self.start_time: Optional[float] = None
        self.stop_time = None
        self.last_cycle_start_time: Optional[float] = None
        self.last_cycle_end_time: Optional[float] = None
        self.last_cycle_duration: Optional[float] = None
        self.last_cycle_success: bool = True

        # 线程安全锁
        self._lock = threading.Lock()
        self._async_lock = asyncio.Lock()

        # 监控统计
        self.total_cycles = 0
        self.successful_cycles = 0
        self.failed_cycles = 0
        self.last_cycle_time = None
        self.last_error = None

        # 活跃的监控任务
        self.active_tasks: Set[str] = set()
        self.task_results: Dict[str, Any] = {}

        # 性能统计
        self.cycle_times = []
        self.max_cycle_time = 0
        self.min_cycle_time = float('inf')
        self.avg_cycle_time = 0

    @property
    def is_running(self) -> bool:
        """应用是否正在运行"""
        return self._is_running

    def start(self) -> bool:
        """启动监控状态"""
        with self._lock:
            if self._is_running:
                self.logger.warning("监控已在运行中")
                return False

            self._is_running = True
            self._stop_requested = False
            self._shutdown_in_progress = False
            self.start_time = datetime.now()
            self.stop_time = None

            # 重置统计
            self.total_cycles = 0
            self.successful_cycles = 0
            self.failed_cycles = 0
            self.last_error = None
            self.active_tasks.clear()
            self.task_results.clear()

            self.logger.info("监控状态已启动")
            return True

    def stop(self) -> bool:
        """停止监控状态"""
        with self._lock:
            if not self._is_running:
                self.logger.warning("监控未在运行")
                return False

            self._stop_requested = True
            self._is_running = False
            self.stop_time = datetime.now()

            self.logger.info("监控状态已停止")
            return True

    def start_shutdown(self) -> bool:
        """
        开始关闭流程，确保只执行一次。
        返回True表示可以继续关闭，False表示关闭已在进行中。
        """
        if self._shutdown_in_progress:
            return False
        self._shutdown_in_progress = True
        return True

    def is_stop_requested(self) -> bool:
        """检查是否请求停止"""
        return self._stop_requested

    def record_cycle_start(self):
        """记录监控周期开始"""
        with self._lock:
            self.total_cycles += 1
            self.last_cycle_start_time = time.time()

    def record_cycle_end(self, success: bool, error: Optional[str] = None):
        """记录监控周期结束"""
        with self._lock:
            if success:
                self.successful_cycles += 1
                self.last_error = None
            else:
                self.failed_cycles += 1
                self.last_error = error

            # 记录周期时间
            if self.last_cycle_start_time:
                cycle_duration = time.time() - self.last_cycle_start_time
                self.cycle_times.append(cycle_duration)

                # 保持最近100个周期的记录
                if len(self.cycle_times) > 100:
                    self.cycle_times.pop(0)

                # 更新统计
                self.max_cycle_time = max(self.max_cycle_time, cycle_duration)
                self.min_cycle_time = min(self.min_cycle_time, cycle_duration)
                self.avg_cycle_time = sum(self.cycle_times) / len(self.cycle_times)

    def add_task(self, task_name: str) -> bool:
        """添加活跃任务"""
        with self._lock:
            if task_name in self.active_tasks:
                return False

            self.active_tasks.add(task_name)
            self.task_results[task_name] = {
                "start_time": datetime.now(),
                "status": "running"
            }
            self.logger.debug(f"添加活跃任务: {task_name}")
            return True

    def remove_task(self, task_name: str, result: Optional[Dict] = None) -> bool:
        """移除活跃任务"""
        with self._lock:
            if task_name not in self.active_tasks:
                return False

            self.active_tasks.remove(task_name)

            if task_name in self.task_results:
                self.task_results[task_name].update({
                    "end_time": datetime.now(),
                    "status": "completed",
                    "result": result
                })

            self.logger.debug(f"移除活跃任务: {task_name}")
            return True

    def get_task_status(self, task_name: str) -> Optional[Dict]:
        """获取任务状态"""
        with self._lock:
            return self.task_results.get(task_name)

    def get_active_tasks(self) -> Set[str]:
        """获取活跃任务列表"""
        with self._lock:
            return self.active_tasks.copy()

    def get_status(self) -> Dict[str, Any]:
        """获取完整状态信息"""
        with self._lock:
            status = {
                "is_running": self._is_running,
                "should_stop": self._stop_requested,
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "stop_time": self.stop_time.isoformat() if self.stop_time else None,
                "uptime": self._calculate_uptime(),
                "statistics": {
                    "total_cycles": self.total_cycles,
                    "successful_cycles": self.successful_cycles,
                    "failed_cycles": self.failed_cycles,
                    "success_rate": self._calculate_success_rate(),
                    "last_error": self.last_error
                },
                "performance": {
                    "max_cycle_time": self.max_cycle_time,
                    "min_cycle_time": self.min_cycle_time if self.min_cycle_time != float('inf') else 0,
                    "avg_cycle_time": self.avg_cycle_time,
                    "recent_cycles": len(self.cycle_times)
                },
                "active_tasks": list(self.active_tasks),
                "task_count": len(self.active_tasks)
            }

            return status

    def _calculate_uptime(self) -> Optional[float]:
        """计算运行时间（秒）"""
        if not self.start_time:
            return None

        end_time = self.stop_time or datetime.now()
        return (end_time - self.start_time).total_seconds()

    def _calculate_success_rate(self) -> float:
        """计算成功率"""
        if self.total_cycles == 0:
            return 0.0

        return (self.successful_cycles / self.total_cycles) * 100

    def reset_statistics(self):
        """重置统计信息"""
        with self._lock:
            self.total_cycles = 0
            self.successful_cycles = 0
            self.failed_cycles = 0
            self.last_error = None
            self.cycle_times.clear()
            self.max_cycle_time = 0
            self.min_cycle_time = float('inf')
            self.avg_cycle_time = 0

            self.logger.info("统计信息已重置")

    def get_summary(self) -> str:
        """获取状态摘要"""
        status = self.get_status()

        if status["is_running"]:
            uptime_minutes = int(status["uptime"] / 60) if status["uptime"] else 0
            summary = (f"运行中 | 运行时间: {uptime_minutes}分钟 | "
                      f"周期: {status['statistics']['total_cycles']} | "
                      f"成功率: {status['statistics']['success_rate']:.1f}% | "
                      f"活跃任务: {status['task_count']}")
        else:
            summary = "已停止"

        return summary