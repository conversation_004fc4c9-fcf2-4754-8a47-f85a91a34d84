"""
基础设备管理路由模块
"""

import json
import asyncio
import sys
from datetime import datetime
from flask import jsonify, request, current_app, Blueprint
from web.utils.decorators import login_required
from .login_helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TESSERACT_AVAILABLE, PIL_AVAILABLE
from .cookie_tester import CookieTester, _check_profile_exists, _get_profile_data

# 创建设备路由蓝图
device_routes_bp = Blueprint('device_routes', __name__)


@device_routes_bp.route('/')
@login_required
def get_grab_devices():
    """获取抢房设备列表"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        devices = grab_device_model.get_all()

        # 获取查询参数
        search_query = request.args.get('search', '').strip()
        enabled_filter = request.args.get('enabled', '')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 15))

        # 筛选设备
        filtered_devices = devices

        # 按搜索关键词筛选
        if search_query:
            filtered_devices = [
                d for d in filtered_devices
                if search_query.lower() in d.get('username', '').lower() or
                   search_query.lower() in d.get('target_estate', '').lower()
            ]

        # 按启用状态筛选
        if enabled_filter:
            enabled_bool = enabled_filter.lower() == 'true'
            filtered_devices = [d for d in filtered_devices if d.get('enabled') == enabled_bool]

        # 计算分页信息
        total_count = len(filtered_devices)
        total_pages = (total_count + per_page - 1) // per_page
        start_index = (page - 1) * per_page
        end_index = start_index + per_page

        # 获取当前页的设备
        page_devices = filtered_devices[start_index:end_index]

        # 检查profiles目录是否存在
        for device in page_devices:
            device['profile_exists'] = _check_profile_exists(device['username'])

            # 计算cookie剩余有效期
            if device.get('cookie_expires_at'):
                try:
                    expires_at = datetime.fromisoformat(device['cookie_expires_at'].replace('Z', '+00:00'))
                    remaining = expires_at - datetime.now()
                    device['cookie_remaining_hours'] = max(0, remaining.total_seconds() / 3600)
                except:
                    device['cookie_remaining_hours'] = 0
            else:
                device['cookie_remaining_hours'] = 0

        return jsonify({
            'devices': page_devices,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total_count': total_count,
                'total_pages': total_pages,
                'has_prev': page > 1,
                'has_next': page < total_pages
            },
            'stats': {
                'total': len(devices),
                'enabled': len([d for d in devices if d.get('enabled')]),
                'disabled': len([d for d in devices if not d.get('enabled')]),
                'logged': len([d for d in devices if d.get('access_token')])
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取抢房设备列表失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@device_routes_bp.route('/<int:device_id>')
@login_required
def get_grab_device(device_id):
    """获取单个抢房设备信息"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)

        if device:
            device['profile_exists'] = _check_profile_exists(device['username'])

            # 计算cookie剩余有效期
            if device.get('cookie_expires_at'):
                try:
                    expires_at = datetime.fromisoformat(device['cookie_expires_at'].replace('Z', '+00:00'))
                    remaining = expires_at - datetime.now()
                    device['cookie_remaining_hours'] = max(0, remaining.total_seconds() / 3600)
                except:
                    device['cookie_remaining_hours'] = 0
            else:
                device['cookie_remaining_hours'] = 0

            return jsonify({'device': device})
        else:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

    except Exception as e:
        current_app.logger.error(f"获取抢房设备信息失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@device_routes_bp.route('/', methods=['POST'])
@login_required
def add_grab_device():
    """添加新抢房设备"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        new_device = request.json

        # 验证必要的字段
        if not new_device.get('username'):
            return jsonify({'error': '缺少用户名'}), 400
        if not new_device.get('phone'):
            return jsonify({'error': '缺少手机号'}), 400

        # 检查用户名是否已存在
        existing_device = grab_device_model.get_by_username(new_device.get('username'))
        if existing_device:
            return jsonify({'error': f'用户名已存在: {new_device.get("username")}'}), 400

        # 保存新设备到数据库
        device = grab_device_model.create_or_update(new_device)
        current_app.logger.info(f"成功添加抢房设备: {device.get('username')}")

        # 如果新设备是启用状态，触发自动管理登录管理器
        if new_device.get('enabled', False):
            current_app.logger.info(f"新设备 {device['username']} 已启用，检查是否需要启动自动登录管理器")

            try:
                from . import auto_manage_login_manager
                auto_manage_login_manager()
            except Exception as e:
                current_app.logger.warning(f"自动管理登录管理器失败: {e}")

        return jsonify({'status': 'success', 'device': device})

    except Exception as e:
        current_app.logger.error(f"添加抢房设备失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@device_routes_bp.route('/<int:device_id>', methods=['PUT'])
@login_required
def update_grab_device(device_id):
    """更新抢房设备信息"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        updated_device = request.json

        # 验证必要的字段
        if not updated_device.get('username'):
            return jsonify({'error': '缺少用户名'}), 400
        if not updated_device.get('phone'):
            return jsonify({'error': '缺少手机号'}), 400

        # 检查设备是否存在
        existing_device = grab_device_model.get_by_id(device_id)
        if not existing_device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        # 保留原始ID
        updated_device['id'] = device_id

        # 处理抢房条件数据
        conditions = updated_device.get('conditions', [])
        current_app.logger.info(f"设备 {device_id} 的抢房条件: {conditions}")

        # 验证条件数据格式
        if conditions:
            valid_conditions = []
            for condition in conditions:
                if isinstance(condition, dict) and 'type' in condition and 'value' in condition:
                    if condition['type'] and condition['value']:
                        valid_conditions.append(condition)
                        current_app.logger.info(f"有效条件: {condition['type']} = {condition['value']}")
            updated_device['conditions'] = valid_conditions
            current_app.logger.info(f"设备 {device_id} 保存了 {len(valid_conditions)} 个有效抢房条件")
        else:
            updated_device['conditions'] = []
            current_app.logger.info(f"设备 {device_id} 未设置抢房条件")

        # 保持登录状态信息（除非明确要求清除）
        preserve_login = updated_device.get('preserve_login', True)
        if preserve_login and existing_device:
            login_fields = ['access_token', 'sessioncode', 'cookie', 'cookie_expires_at', 'last_login_at']
            for field in login_fields:
                if field not in updated_device and existing_device.get(field):
                    updated_device[field] = existing_device[field]

        # 计算Cookie剩余时间（仅用于显示，不触发立即检查）
        access_token = updated_device.get('access_token')
        if access_token:
            login_helper = HuhhothomeLoginHelper()
            remaining_hours = login_helper.get_cookie_remaining_hours(access_token)

            if remaining_hours is not None:
                remaining_minutes = remaining_hours * 60
                current_app.logger.info(f"设备 {device_id} Cookie剩余时间: {remaining_minutes:.1f} 分钟")

                # 仅记录Cookie状态，不触发立即检查
                # 自动登录管理器会在定时检查中发现并处理Cookie即将过期的设备
                if remaining_minutes <= 15:
                    current_app.logger.info(f"设备 {device_id} Cookie剩余 {remaining_minutes:.1f} 分钟，将由自动登录管理器在下次检查时处理")

                # 更新剩余时间
                updated_device['cookie_remaining_hours'] = remaining_hours
            else:
                current_app.logger.warning(f"设备 {device_id} 无法解析Cookie有效期")
                updated_device['cookie_remaining_hours'] = 0
        else:
            current_app.logger.info(f"设备 {device_id} 未设置access_token")
            updated_device['cookie_remaining_hours'] = 0

        # 保存到数据库
        device = grab_device_model.create_or_update(updated_device)

        # 检查设备启用状态是否发生变化，自动管理登录管理器
        old_enabled = existing_device.get('enabled', False)
        new_enabled = updated_device.get('enabled', False)

        if old_enabled != new_enabled:
            # 设备启用状态发生变化，触发自动管理
            current_app.logger.info(f"设备 {device['username']} 启用状态变更: {old_enabled} -> {new_enabled}")

            try:
                from . import auto_manage_login_manager
                auto_manage_login_manager()
            except Exception as e:
                current_app.logger.warning(f"自动管理登录管理器失败: {e}")

        return jsonify({'status': 'success', 'device': device})

    except Exception as e:
        current_app.logger.error(f"更新抢房设备失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@device_routes_bp.route('/<int:device_id>', methods=['DELETE'])
@login_required
def delete_grab_device(device_id):
    """删除抢房设备"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        # 检查设备是否存在
        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        # 删除设备
        success = grab_device_model.delete(device_id)

        if success:
            current_app.logger.info(f"成功删除抢房设备: {device['username']}")

            # 如果删除的是启用设备，检查是否需要停止自动登录管理器
            if device.get('enabled', False):
                current_app.logger.info(f"删除了启用设备 {device['username']}，检查是否需要停止自动登录管理器")

                try:
                    from . import auto_manage_login_manager
                    auto_manage_login_manager()
                except Exception as e:
                    current_app.logger.warning(f"自动管理登录管理器失败: {e}")

            return jsonify({'status': 'success'})
        else:
            return jsonify({'error': '删除失败'}), 500

    except Exception as e:
        current_app.logger.error(f"删除抢房设备失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@device_routes_bp.route('/<int:device_id>/get_captcha', methods=['POST'])
@login_required
def get_captcha(device_id):
    """获取验证码图片"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        phone = device.get('phone')
        if not phone:
            return jsonify({'error': '设备未配置手机号'}), 400

        # 获取验证码
        login_helper = HuhhothomeLoginHelper()
        img_base64 = login_helper.fetch_captcha_image(phone)

        if not img_base64:
            return jsonify({'error': '获取验证码失败'}), 500

        # 尝试OCR识别
        captcha_candidates = []
        if TESSERACT_AVAILABLE and PIL_AVAILABLE:
            captcha_candidates = login_helper.quick_ocr_captcha(img_base64)

        # 检查代理状态
        proxy_enabled = False
        try:
            proxy_enabled = login_helper.proxy_manager is not None and login_helper.proxy_manager.current_proxy is not None
        except:
            pass

        return jsonify({
            'status': 'success',
            'image': f"data:image/jpeg;base64,{img_base64}",
            'candidates': captcha_candidates,
            'proxy_enabled': proxy_enabled
        })

    except Exception as e:
        current_app.logger.error(f"获取验证码失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@device_routes_bp.route('/<int:device_id>/send_sms', methods=['POST'])
@login_required
def send_sms_code(device_id):
    """发送短信验证码"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        phone = device.get('phone')
        if not phone:
            return jsonify({'error': '设备未配置手机号'}), 400

        # 获取图形验证码
        img_code = request.json.get('captcha')
        if not img_code:
            return jsonify({'error': '缺少图形验证码'}), 400

        # 发送短信验证码
        login_helper = HuhhothomeLoginHelper()
        success, message = login_helper.send_sms_code(phone, img_code)

        if success:
            # 检查代理状态
            proxy_enabled = False
            try:
                proxy_enabled = login_helper.proxy_manager is not None and login_helper.proxy_manager.current_proxy is not None
            except:
                pass

            return jsonify({
                'status': 'success',
                'message': f'验证码已发送至手机号 {phone}',
                'proxy_enabled': proxy_enabled
            })
        else:
            return jsonify({'error': f'发送验证码失败: {message}'}), 400

    except Exception as e:
        current_app.logger.error(f"发送短信验证码失败: {str(e)}")
        import traceback
        current_app.logger.error(f"详细错误信息: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500


@device_routes_bp.route('/<int:device_id>/test_cookie', methods=['POST'])
@login_required
def test_device_cookie(device_id):
    """测试抢房设备的cookie有效性"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        # 检查是否有cookie和access_token
        if not device.get('cookie') or not device.get('access_token'):
            return jsonify({
                'status': 'error',
                'message': '设备未登录或缺少认证信息'
            }), 400

        # 测试cookie有效性
        tester = CookieTester()
        result = tester.test_cookie_validity(device)

        return jsonify({
            'status': 'success' if result['success'] else 'error',
            'valid': result['success'],
            'message': result['message'],
            'details': result.get('details', '')
        })

    except Exception as e:
        current_app.logger.error(f"测试cookie有效性失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@device_routes_bp.route('/<int:device_id>/extend_cookie', methods=['POST'])
@login_required
def extend_device_cookie(device_id):
    """手动延长抢房设备Cookie有效期 - 使用autologin接口"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        request_data = request.json or {}
        phone = request_data.get('phone')
        access_token = request_data.get('access_token')
        sessioncode = request_data.get('sessioncode')

        # 验证必要参数
        if not phone or not access_token or not sessioncode:
            # 尝试从设备数据中获取
            phone = phone or device.get('phone')
            access_token = access_token or device.get('access_token')
            sessioncode = sessioncode or device.get('sessioncode')

        if not phone or not access_token or not sessioncode:
            missing = []
            if not phone: missing.append('phone')
            if not access_token: missing.append('access_token')
            if not sessioncode: missing.append('sessioncode')

            return jsonify({
                'status': 'failed',
                'error': f'缺少必要参数: {", ".join(missing)}',
                'details': '设备需要先登录以获取access_token和sessioncode'
            }), 400

        current_app.logger.info(f"手动延长设备 {device['username']} (ID: {device_id}) 的Cookie")

        login_helper = HuhhothomeLoginHelper()

        # 调用autologin接口延长Cookie
        extend_result = login_helper.auto_extend_cookie(
            phone, access_token, sessioncode
        )

        if extend_result['success']:
            # 从结果中获取数据
            new_access_token = extend_result.get('access_token')
            cookies = extend_result.get('cookies')
            message = extend_result.get('message', 'Cookie延长成功')
            cookie_remaining_hours = extend_result.get('remaining_hours', 0)

            # 解析新的JWT token获取过期时间
            expires_at = None

            if new_access_token:
                jwt_payload = login_helper.parse_jwt_token(new_access_token)
                if jwt_payload and 'exp' in jwt_payload:
                    expires_at = datetime.fromtimestamp(jwt_payload['exp'])
                    current_app.logger.info(f"设备 {device['username']} Cookie延长成功，新的剩余有效期: {cookie_remaining_hours:.2f} 小时")

            # 更新设备信息
            device_update = {
                'id': device_id,
                'username': device['username'],
                'phone': device['phone'],
                'target_estate': device.get('target_estate', ''),
                'house_id': device.get('house_id', ''),
                'enabled': device.get('enabled', True),
                'conditions': device.get('conditions', []),
                'access_token': new_access_token,
                'sessioncode': sessioncode,  # sessioncode保持不变
                'cookie': json.dumps(cookies) if cookies else device.get('cookie'),
                'cookie_expires_at': expires_at.isoformat() if expires_at else None,
                'last_login_at': datetime.now().isoformat()
            }

            updated_device = grab_device_model.create_or_update(device_update)

            current_app.logger.info(f"设备 {device['username']} Cookie延长成功，已更新数据库")

            return jsonify({
                'status': 'success',
                'message': message,
                'device': updated_device,
                'cookie_remaining_hours': cookie_remaining_hours,
                'new_access_token_generated': True,
                'method': 'autologin_api'
            })
        else:
            error_message = extend_result.get('message', '未知错误')
            current_app.logger.error(f"设备 {device['username']} Cookie延长失败: {error_message}")

            return jsonify({
                'status': 'failed',
                'message': error_message,
                'error': 'autologin接口调用失败',
                'suggestion': '请检查设备的access_token和sessioncode是否有效，或尝试重新登录'
            }), 400

    except Exception as e:
        current_app.logger.error(f"延长设备Cookie失败: {str(e)}")
        import traceback
        current_app.logger.error(f"详细错误信息: {traceback.format_exc()}")
        return jsonify({
            'error': f'延长Cookie时发生异常: {str(e)}',
            'details': '服务器内部错误，请稍后重试或联系管理员'
        }), 500


@device_routes_bp.route('/<int:device_id>/complete_extend', methods=['POST'])
@login_required
def complete_cookie_extend(device_id):
    """完成Cookie延长（输入短信验证码后） - 已移除，请使用login接口"""
    return jsonify({
        'error': '此接口已移除，请使用 /login 接口重新登录设备',
        'suggestion': '使用设备登录接口获取新的access_token和sessioncode'
    }), 410  # HTTP 410 Gone


@device_routes_bp.route('/check_cookies', methods=['POST'])
@login_required
def check_all_cookies():
    """检查所有设备的Cookie状态 - 只检查状态，不自动延长"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        devices = grab_device_model.get_all()
        results = []

        for device in devices:
            if not device.get('enabled') or not device.get('access_token'):
                continue

            device_result = {
                'device_id': device.get('id'),
                'username': device.get('username'),
                'status': 'checked'
            }

            try:
                login_helper = HuhhothomeLoginHelper()
                remaining_hours = login_helper.get_cookie_remaining_hours(device.get('access_token'))

                if remaining_hours is None:
                    device_result['status'] = 'error'
                    device_result['message'] = '无法解析Cookie有效期'
                elif remaining_hours <= 0:
                    device_result['status'] = 'expired'
                    device_result['message'] = 'Cookie已过期'
                    device_result['remaining_hours'] = 0
                elif remaining_hours * 60 <= 15:  # 剩余15分钟以下
                    device_result['status'] = 'needs_extend'
                    device_result['remaining_hours'] = remaining_hours
                    device_result['message'] = f'剩余{remaining_hours*60:.1f}分钟，需要延长'
                    device_result['suggestion'] = '使用自动Cookie延长管理器或手动延长Cookie'
                else:
                    device_result['status'] = 'healthy'
                    device_result['remaining_hours'] = remaining_hours
                    device_result['message'] = f'Cookie健康，剩余{remaining_hours:.2f}小时'

            except Exception as e:
                device_result['status'] = 'error'
                device_result['message'] = f'检查失败: {str(e)}'

            results.append(device_result)

        # 统计结果
        stats = {
            'total': len(results),
            'healthy': len([r for r in results if r['status'] == 'healthy']),
            'needs_extend': len([r for r in results if r['status'] == 'needs_extend']),
            'expired': len([r for r in results if r['status'] == 'expired']),
            'error': len([r for r in results if r['status'] == 'error'])
        }

        return jsonify({
            'status': 'success',
            'results': results,
            'stats': stats,
            'message': f'检查完成，{stats["total"]}个设备中{stats["needs_extend"]}个需要延长，{stats["expired"]}个已过期'
        })

    except Exception as e:
        current_app.logger.error(f"批量检查Cookie失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@device_routes_bp.route('/<int:device_id>/login', methods=['POST'])
@login_required
def login_grab_device(device_id):
    """抢房设备登录"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        login_data = request.json
        phone = device.get('phone')
        sms_code = login_data.get('sms_code')
        img_code = login_data.get('img_code')

        if not phone or not sms_code or not img_code:
            return jsonify({'error': '缺少必要的登录信息'}), 400

        # 执行登录
        login_helper = HuhhothomeLoginHelper()
        success, access_token, sessioncode, cookies, message = login_helper.login(phone, sms_code, img_code)

        if success:
            # 解析JWT token获取过期时间
            expires_at = None
            cookie_remaining_hours = 0

            if access_token:
                jwt_payload = login_helper.parse_jwt_token(access_token)
                if jwt_payload and 'exp' in jwt_payload:
                    expires_at = datetime.fromtimestamp(jwt_payload['exp'])
                    # 计算剩余小时数
                    remaining = expires_at - datetime.now()
                    cookie_remaining_hours = max(0, remaining.total_seconds() / 3600)
                    current_app.logger.info(f"设备 {device['username']} 登录成功，Cookie剩余有效期: {cookie_remaining_hours:.2f} 小时")
                else:
                    current_app.logger.warning(f"设备 {device['username']} JWT token解析失败或缺少exp字段")

            # 更新设备信息
            device_update = {
                'id': device_id,
                'username': device['username'],
                'phone': device['phone'],
                'target_estate': device.get('target_estate', ''),
                'house_id': device.get('house_id', ''),
                'enabled': device.get('enabled', True),
                'conditions': device.get('conditions', []),
                'access_token': access_token,
                'sessioncode': sessioncode,
                'cookie': json.dumps(cookies),
                'cookie_expires_at': expires_at.isoformat() if expires_at else None,
                'last_login_at': datetime.now().isoformat()
            }

            updated_device = grab_device_model.create_or_update(device_update)

            # 计算并添加剩余有效期到返回数据
            updated_device['cookie_remaining_hours'] = cookie_remaining_hours

            current_app.logger.info(f"抢房设备 {device['username']} 登录成功")

            # 检查代理状态
            proxy_enabled = False
            try:
                proxy_enabled = login_helper.proxy_manager is not None and login_helper.proxy_manager.current_proxy is not None
            except:
                pass

            return jsonify({
                'status': 'success',
                'message': '登录成功',
                'device': updated_device,
                'proxy_enabled': proxy_enabled
            })
        else:
            return jsonify({'error': f'登录失败: {message}'}), 400

    except Exception as e:
        current_app.logger.error(f"抢房设备登录失败: {str(e)}")
        import traceback
        current_app.logger.error(f"详细错误信息: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500


@device_routes_bp.route('/<int:device_id>/profile')
@login_required
def get_device_profile(device_id):
    """获取设备对应的profile信息"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        profile_data = _get_profile_data(device['username'])

        return jsonify({
            'status': 'success',
            'profile': profile_data
        })

    except Exception as e:
        current_app.logger.error(f"获取profile信息失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@device_routes_bp.route('/estates', methods=['GET'])
def get_estates():
    """获取所有小区列表"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        estate_model = getattr(data_manager, 'estate_model', None)

        if not estate_model:
            # 如果没有初始化estate_model，记录错误并返回空列表
            current_app.logger.error("EstateModel未正确初始化")
            return jsonify({
                'success': False,
                'message': 'EstateModel未正确初始化'
            }), 500

        # 从estates表获取所有小区名称
        estates = estate_model.get_all_names()

        # 如果没有数据，添加一些默认小区
        if not estates:
            default_estates = ["青年人才公寓", "青年公租房", "创客公寓", "人才公寓"]
            for estate_name in default_estates:
                try:
                    estate_model.add_estate(estate_name)
                except Exception as e:
                    current_app.logger.warning(f"添加默认小区失败 {estate_name}: {str(e)}")

            # 重新获取小区列表
            estates = estate_model.get_all_names()

        return jsonify({
            'success': True,
            'data': sorted(estates)
        })

    except Exception as e:
        current_app.logger.error(f"获取小区列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取小区列表失败: {str(e)}'
        }), 500


@device_routes_bp.route('/<int:device_id>/application_records', methods=['GET'])
@login_required
def get_application_records(device_id):
    """获取抢房设备的申请记录"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        # 检查设备是否已登录（有access_token）
        access_token = device.get('access_token')
        if not access_token:
            return jsonify({
                'status': 'error',
                'error': '设备未登录，无法获取申请记录',
                'suggestion': '请先登录该设备以获取access_token'
            }), 400

        current_app.logger.info(f"获取设备 {device.get('username')} (ID: {device_id}) 的申请记录")

        # 使用登录助手获取申请记录
        login_helper = HuhhothomeLoginHelper()
        result = login_helper.get_application_records(access_token)

        if result['success']:
            # 解析申请记录数据
            data = result.get('data', {})
            records = data.get('datas', [])

            # 格式化申请记录数据，便于前端显示
            formatted_records = []
            for record in records:
                item_map = record.get('itemmap', {})
                formatted_record = {
                    'formid': record.get('formid'),
                    'created': record.get('created'),
                    'approveno': item_map.get('approveno'),
                    'name': item_map.get('name'),
                    'phone': item_map.get('phone'),
                    'cardno': item_map.get('cardno'),
                    'houseestatename': item_map.get('houseestatename'),
                    'housecode': item_map.get('housecode'),
                    'company': item_map.get('company'),
                    'education': item_map.get('education'),
                    'marriage': item_map.get('marriage'),
                    'group': item_map.get('group'),
                    'leasetime': item_map.get('leasetime'),
                    'status': item_map.get('status'),
                    'allotlstatus': item_map.get('allotlstatus'),
                    'appointtime': item_map.get('appointtime'),
                    'addr': item_map.get('addr'),
                    'village': item_map.get('village'),
                    'room': item_map.get('room'),
                    'tollet': item_map.get('tollet'),
                    'remark': item_map.get('remark')
                }
                formatted_records.append(formatted_record)

            # 按申请时间倒序排列
            if formatted_records:
                formatted_records.sort(key=lambda x: x.get('created', 0), reverse=True)

            return jsonify({
                'status': 'success',
                'message': f'成功获取 {len(formatted_records)} 条申请记录',
                'device_info': {
                    'id': device_id,
                    'username': device.get('username'),
                    'phone': device.get('phone')
                },
                'records': formatted_records,
                'pagination': {
                    'total_count': data.get('rowcount', len(formatted_records)),
                    'page_count': data.get('pagecount', 1),
                    'current_page': data.get('pageno', 1),
                    'lines_per_page': data.get('linesperpage', 5)
                }
            })
        else:
            error_message = result.get('error', '获取申请记录失败')
            errcode = result.get('errcode')

            current_app.logger.error(f"设备 {device.get('username')} 申请记录获取失败: {error_message} (errcode: {errcode})")

            return jsonify({
                'status': 'error',
                'error': error_message,
                'errcode': errcode,
                'suggestion': '请检查设备登录状态或重新登录设备'
            }), 400

    except Exception as e:
        current_app.logger.error(f"获取申请记录失败: {str(e)}")
        import traceback
        current_app.logger.error(f"详细错误信息: {traceback.format_exc()}")
        return jsonify({
            'error': f'获取申请记录时发生异常: {str(e)}',
            'suggestion': '请稍后重试或联系管理员'
        }), 500


@device_routes_bp.route('/<int:device_id>/user_profile', methods=['GET'])
@login_required
def get_user_profile(device_id):
    """获取抢房设备的用户主页信息"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        # 检查设备是否已登录（有access_token）
        access_token = device.get('access_token')
        if not access_token:
            return jsonify({
                'status': 'error',
                'error': '设备未登录，无法获取用户主页信息',
                'suggestion': '请先登录该设备以获取access_token'
            }), 400

        current_app.logger.info(f"获取设备 {device.get('username')} (ID: {device_id}) 的用户主页信息")

        # 使用登录助手获取用户主页信息
        login_helper = HuhhothomeLoginHelper()
        result = login_helper.get_user_profile(access_token)

        if result['success']:
            # 解析用户主页数据
            data = result.get('data', {})
            item_map = data.get('itemmap', {})

            # 格式化用户信息数据，便于前端显示
            formatted_profile = {
                'formid': data.get('formid'),
                'created': data.get('created'),
                'lastmodified': data.get('lastmodified'),
                'domainid': data.get('domainid'),
                'applicationid': data.get('applicationid'),
                'formname': data.get('formname'),

                # 基本信息
                'name': item_map.get('name'),
                'sex': item_map.get('sex'),
                'phone': item_map.get('phone'),
                'email': item_map.get('email'),
                'cardtype': item_map.get('cardtype'),
                'cardno': item_map.get('cardno'),
                'cardaddr': item_map.get('cardaddr'),
                'addr': item_map.get('addr'),
                'tel': item_map.get('tel'),

                # 工作信息
                'workname': item_map.get('workname'),
                'companyid': item_map.get('companyid'),
                'managerdept': item_map.get('managerdept'),

                # 认证信息
                'vstatus': item_map.get('vstatus'),
                'vstatustype': item_map.get('vstatustype'),
                'regtype': item_map.get('regtype'),
                'types': item_map.get('types'),
                'status': item_map.get('status'),
                'locked': item_map.get('locked'),

                # 地区信息
                'country': item_map.get('country'),
                'village': item_map.get('village'),
                'township': item_map.get('township'),

                # 证件照片（需要安全处理）
                'cardt': item_map.get('cardt', []),  # 身份证正面
                'cardb': item_map.get('cardb', []),  # 身份证反面

                # 其他信息
                'sessioncode': item_map.get('sessioncode'),
                'weixinopenid': item_map.get('weixinopenid'),
                'aliopenid': item_map.get('aliopenid'),
                'code': item_map.get('code'),
                'blackid': item_map.get('blackid'),
                'legalno': item_map.get('legalno'),
                'legaler': item_map.get('legaler'),
                'sysuserid': item_map.get('sysuserid'),
                'weixinpayaccount': item_map.get('weixinpayaccount')
            }

            return jsonify({
                'status': 'success',
                'message': '成功获取用户主页信息',
                'device_info': {
                    'id': device_id,
                    'username': device.get('username'),
                    'phone': device.get('phone')
                },
                'profile': formatted_profile
            })
        else:
            error_message = result.get('error', '获取用户主页信息失败')
            errcode = result.get('errcode')

            current_app.logger.error(f"设备 {device.get('username')} 用户主页信息获取失败: {error_message} (errcode: {errcode})")

            return jsonify({
                'status': 'error',
                'error': error_message,
                'errcode': errcode,
                'suggestion': '请检查设备登录状态或重新登录设备'
            }), 400

    except Exception as e:
        current_app.logger.error(f"获取用户主页信息失败: {str(e)}")
        import traceback
        current_app.logger.error(f"详细错误信息: {traceback.format_exc()}")
        return jsonify({
            'error': f'获取用户主页信息时发生异常: {str(e)}',
            'suggestion': '请稍后重试或联系管理员'
        }), 500


@device_routes_bp.route('/<int:device_id>/preupload_cache', methods=['GET'])
@login_required
def get_preupload_cache(device_id):
    """获取抢房设备的预上传缓存状态"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        username = device.get('username')
        if not username:
            return jsonify({'error': '设备未配置用户名'}), 400

        # 导入缓存管理器
        try:
            from cache.upload_cache import FileUploadCache
            cache_manager = FileUploadCache()

            # 获取用户的缓存数据
            cache_data = cache_manager.get_user_cache_data(username)

            return jsonify({
                'status': 'success',
                'cache_data': cache_data,
                'username': username
            })

        except ImportError as e:
            current_app.logger.error(f"导入预上传缓存模块失败: {e}")
            return jsonify({
                'status': 'error',
                'message': '预上传缓存功能不可用，请检查系统配置'
            }), 500

    except Exception as e:
        current_app.logger.error(f"获取预上传缓存状态失败: {str(e)}")
        return jsonify({'error': str(e)}), 500


@device_routes_bp.route('/<int:device_id>/refresh_preupload_cache', methods=['POST'])
@login_required
def refresh_preupload_cache(device_id):
    """刷新抢房设备的预上传缓存"""
    try:
        data_manager = current_app.config_manager.get_data_manager()
        grab_device_model = data_manager.grab_device_model

        device = grab_device_model.get_by_id(device_id)
        if not device:
            return jsonify({'error': f'未找到抢房设备ID: {device_id}'}), 404

        username = device.get('username')
        if not username:
            return jsonify({'error': '设备未配置用户名'}), 400

        # 检查设备是否已登录
        access_token = device.get('access_token')
        cookie = device.get('cookie')
        if not access_token or not cookie:
            return jsonify({
                'status': 'error',
                'message': '设备未登录，无法刷新预上传缓存'
            }), 400

        # 导入预上传服务
        try:
            from services.preupload_service import PreUploadService
            from cache.upload_cache import FileUploadCache

            # 创建预上传服务实例
            cache_manager = FileUploadCache()
            preupload_service = PreUploadService(cache_manager)

            # 在后台线程中执行预上传刷新
            def refresh_worker():
                try:
                    # 在Windows上设置事件循环策略
                    if sys.platform == 'win32':
                        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

                    # 创建新的事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # 创建登录客户端（支持代理配置）
                    from ZDQF.login import HuhhothomeLogin

                    # 获取代理配置（与正常抢房流程保持一致）
                    proxy_config = None
                    proxy_manager = None

                    try:
                        # 尝试获取代理管理器
                        from core.network.proxy_manager import ProxyManager

                        # 尝试从多个来源获取代理配置
                        proxy_api_url = None
                        try:
                            # 方法1: 从环境变量获取（优先，避免Flask上下文问题）
                            import os
                            proxy_api_url = os.environ.get('PROXY_API_URL')

                            # 方法2: 从配置文件获取
                            if not proxy_api_url:
                                # 优先查找 user_config.json，然后是 config.json
                                config_files = ['user_config.json', 'config.json']
                                for config_file in config_files:
                                    if os.path.exists(config_file):
                                        import json
                                        with open(config_file, 'r', encoding='utf-8') as f:
                                            config_data = json.load(f)
                                            proxy_api_url = config_data.get('proxy_api_url')
                                            if proxy_api_url:
                                                break

                            # 方法3: 尝试从Flask应用配置获取（仅在有应用上下文时）
                            if not proxy_api_url:
                                try:
                                    from flask import has_app_context
                                    if has_app_context():
                                        proxy_api_url = (
                                            current_app.config.get('PROXY_API_URL') or
                                            getattr(current_app.config_manager, 'proxy_api_url', None) if hasattr(current_app, 'config_manager') else None
                                        )
                                except Exception:
                                    pass  # 忽略Flask上下文相关错误
                        except Exception:
                            pass

                        # 创建安全的日志记录函数
                        def safe_log(level, message):
                            """安全的日志记录，避免Flask上下文问题"""
                            try:
                                from flask import has_app_context
                                if has_app_context():
                                    getattr(current_app.logger, level)(message)
                                else:
                                    # 使用标准日志记录
                                    import logging
                                    logger = logging.getLogger(__name__)
                                    getattr(logger, level)(message)
                            except Exception:
                                # 最后的备选方案
                                print(f"[{level.upper()}] {message}")

                        if proxy_api_url:
                            safe_log('info', f"🌐 刷新缓存: 为用户 {username} 初始化代理管理器...")
                            proxy_manager = ProxyManager(proxy_api_url)
                            # 同步获取代理
                            if proxy_manager.update_proxy_sync():
                                proxy_config = proxy_manager.get_proxy_dict()
                                safe_log('info', f"🔗 刷新缓存: 用户 {username} 将使用代理 {proxy_manager.current_proxy}")
                            else:
                                safe_log('warning', f" 刷新缓存: 用户 {username} 代理获取失败，将使用直连")
                                proxy_manager = None
                        else:
                            # 尝试从配置文件获取静态代理配置
                            try:
                                # 优先查找 user_config.json，然后是 config.json
                                config_files = ['user_config.json', 'config.json']
                                config_found = False

                                for config_file in config_files:
                                    if os.path.exists(config_file):
                                        import json
                                        with open(config_file, 'r', encoding='utf-8') as f:
                                            config_data = json.load(f)
                                            proxy_config = config_data.get('proxy_config')
                                            if proxy_config:
                                                safe_log('info', f"🔗 刷新缓存: 用户 {username} 使用配置文件代理 ({config_file})")
                                                config_found = True
                                                break
                                            else:
                                                safe_log('info', f" 刷新缓存: 用户 {username} 未配置代理，使用直连模式 ({config_file})")
                                                config_found = True
                                                break

                                if not config_found:
                                    safe_log('info', f" 刷新缓存: 用户 {username} 配置文件不存在，使用直连模式")
                            except Exception:
                                safe_log('info', f" 刷新缓存: 用户 {username} 读取配置失败，使用直连模式")

                    except Exception as proxy_error:
                        # 使用安全的日志记录
                        try:
                            from flask import has_app_context
                            if has_app_context():
                                current_app.logger.warning(f" 刷新缓存: 用户 {username} 代理初始化失败: {proxy_error}")
                            else:
                                import logging
                                logger = logging.getLogger(__name__)
                                logger.warning(f" 刷新缓存: 用户 {username} 代理初始化失败: {proxy_error}")
                        except Exception:
                            print(f"[WARNING]  刷新缓存: 用户 {username} 代理初始化失败: {proxy_error}")

                        proxy_config = None
                        proxy_manager = None

                    # 创建登录客户端（与正常抢房流程相同的逻辑）
                    if proxy_manager and proxy_manager.current_proxy:
                        login_client = HuhhothomeLogin(proxy_config=proxy_config, proxy_manager=proxy_manager)
                    elif proxy_config:
                        login_client = HuhhothomeLogin(proxy_config=proxy_config)
                    else:
                        login_client = HuhhothomeLogin()

                    # 设置access_token和cookie
                    login_client.access_token = access_token
                    login_client.headers['Membertoken'] = access_token

                    # 设置Cookie
                    if cookie:
                        cookie_pairs = cookie.split(';')
                        for pair in cookie_pairs:
                            if '=' in pair:
                                name, value = pair.strip().split('=', 1)
                                login_client.session.cookies.set(name, value)

                    # 运行预上传刷新（同步方法，强制刷新模式）
                    result = preupload_service.preupload_user_files(username, login_client, force_refresh=True)
                    loop.close()

                    # 使用print而不是current_app.logger，避免应用上下文问题
                    print(f"用户 {username} 预上传缓存刷新完成: {result}")
                    return result

                except Exception as e:
                    # 使用print而不是current_app.logger，避免应用上下文问题
                    print(f"用户 {username} 预上传缓存刷新失败: {e}")
                    return {'success_count': 0, 'failed_count': 0, 'error': str(e)}

            # 启动后台线程执行刷新
            import threading
            result_container = {}

            def thread_worker():
                result_container['result'] = refresh_worker()

            thread = threading.Thread(target=thread_worker)
            thread.start()
            thread.join(timeout=30)  # 最多等待30秒

            if thread.is_alive():
                return jsonify({
                    'status': 'error',
                    'message': '预上传缓存刷新超时，请稍后重试'
                }), 500

            result = result_container.get('result', {'success_count': 0, 'failed_count': 0})

            return jsonify({
                'status': 'success',
                'message': '预上传缓存刷新完成',
                'result': result
            })

        except ImportError as e:
            current_app.logger.error(f"导入预上传服务模块失败: {e}")
            return jsonify({
                'status': 'error',
                'message': '预上传缓存功能不可用，请检查系统配置'
            }), 500

    except Exception as e:
        current_app.logger.error(f"刷新预上传缓存失败: {str(e)}")
        return jsonify({'error': str(e)}), 500