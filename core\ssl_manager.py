"""
统一SSL配置管理器
提供细粒度的SSL配置控制，支持模块级和域名级配置
"""

import ssl
from typing import Dict, Any, Optional, Union
from pathlib import Path

from .utils.unified_logging import Logger


class SSLConfigManager:
    """SSL配置管理器"""

    def __init__(self, config_manager=None):
        """
        初始化SSL配置管理器

        Args:
            config_manager: 统一配置管理器实例
        """
        self.logger = Logger("SSLConfigManager")
        self.config_manager = config_manager
        self._ssl_contexts = {}  # SSL上下文缓存

        # 默认SSL配置
        self.default_config = {
            "enabled": True,
            "verify_certificates": True,
            "modules": {
                "housing_monitor": {
                    "enabled": True,
                    "verify": True,
                    "domains": {
                        "www.huhhothome.cn": {"verify": True},
                        "api.huhhothome.cn": {"verify": False, "reason": "SSL证书不包含此域名"}
                    }
                },
                "grab_executor": {
                    "enabled": True,
                    "verify": True,
                    "domains": {
                        "www.huhhothome.cn": {"verify": True},
                        "api.huhhothome.cn": {"verify": False, "reason": "SSL证书不包含此域名"}
                    }
                },
                "cookie_tester": {
                    "enabled": True,
                    "verify": True,
                    "domains": {
                        "www.huhhothome.cn": {"verify": True}
                    }
                },
                "login_helper": {
                    "enabled": True,
                    "verify": True,
                    "domains": {
                        "www.huhhothome.cn": {"verify": True}
                    }
                },
                "notifications": {
                    "enabled": False,  # 通知器禁用SSL验证
                    "verify": False,
                    "domains": {
                        "api.day.app": {"verify": False},
                        "wxpusher.zjiecode.com": {"verify": False},
                        "push.i-i.me": {"verify": False}
                    }
                }
            }
        }

    def get_ssl_config(self) -> Dict[str, Any]:
        """获取SSL配置"""
        if self.config_manager:
            # 从统一配置管理器获取
            ssl_config = self.config_manager.get('ssl_config', {})
            if ssl_config:
                return ssl_config

        # 返回默认配置
        return self.default_config

    def is_ssl_enabled(self, module: str = None) -> bool:
        """
        检查SSL是否启用

        Args:
            module: 模块名称，如果为None则检查全局设置

        Returns:
            bool: SSL是否启用
        """
        config = self.get_ssl_config()

        if module:
            module_config = config.get('modules', {}).get(module, {})
            return module_config.get('enabled', config.get('enabled', True))

        return config.get('enabled', True)

    def should_verify_ssl(self, module: str, domain: str = None) -> bool:
        """
        检查是否应该验证SSL证书

        Args:
            module: 模块名称
            domain: 域名，如果提供则检查域名级配置

        Returns:
            bool: 是否应该验证SSL
        """
        config = self.get_ssl_config()

        # 如果SSL全局禁用，返回False
        if not config.get('enabled', True):
            return False

        module_config = config.get('modules', {}).get(module, {})

        # 如果模块SSL禁用，返回False
        if not module_config.get('enabled', True):
            return False

        # 检查域名级配置
        if domain:
            domain_config = module_config.get('domains', {}).get(domain, {})
            if 'verify' in domain_config:
                return domain_config['verify']

        # 返回模块级配置
        return module_config.get('verify', config.get('verify_certificates', True))

    def get_ssl_context(self, module: str, domain: str = None,
                       create_if_missing: bool = True) -> Union[ssl.SSLContext, bool]:
        """
        获取SSL上下文

        Args:
            module: 模块名称
            domain: 域名
            create_if_missing: 如果缓存中没有则创建

        Returns:
            SSL上下文对象，或False（禁用SSL验证时）
        """
        # 检查是否应该验证SSL
        should_verify = self.should_verify_ssl(module, domain)

        if not should_verify:
            return False

        # 生成缓存键
        cache_key = f"{module}_{domain or 'default'}"

        # 检查缓存
        if cache_key in self._ssl_contexts:
            return self._ssl_contexts[cache_key]

        if create_if_missing:
            # 创建SSL上下文
            context = ssl.create_default_context()

            # 根据配置调整SSL上下文
            config = self.get_ssl_config()
            module_config = config.get('modules', {}).get(module, {})

            # 如果域名配置为不验证，但模块启用SSL，则创建不验证证书的上下文
            if domain:
                domain_config = module_config.get('domains', {}).get(domain, {})
                if not domain_config.get('verify', True):
                    context.check_hostname = False
                    context.verify_mode = ssl.CERT_NONE

            self._ssl_contexts[cache_key] = context
            return context

        return None

    def get_requests_verify_param(self, module: str, domain: str = None) -> Union[bool, str]:
        """
        获取requests库的verify参数

        Args:
            module: 模块名称
            domain: 域名

        Returns:
            verify参数值（True/False或证书路径）
        """
        return self.should_verify_ssl(module, domain)

    def get_aiohttp_ssl_param(self, module: str, domain: str = None) -> Union[ssl.SSLContext, bool]:
        """
        获取aiohttp的SSL参数

        Args:
            module: 模块名称
            domain: 域名

        Returns:
            SSL上下文或False
        """
        return self.get_ssl_context(module, domain)

    def update_ssl_config(self, config_updates: Dict[str, Any]):
        """
        更新SSL配置

        Args:
            config_updates: 配置更新
        """
        if self.config_manager:
            # 获取当前SSL配置
            current_ssl_config = self.config_manager.get('ssl_config', {})

            # 深度合并配置
            self._deep_update(current_ssl_config, config_updates)

            # 保存到配置管理器
            self.config_manager.set('ssl_config', current_ssl_config)

            # 清除SSL上下文缓存
            self._ssl_contexts.clear()

            self.logger.info("SSL配置已更新")
        else:
            self.logger.warning("无法更新SSL配置：未提供配置管理器")

    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value

    def validate_ssl_config(self) -> Dict[str, Any]:
        """
        验证SSL配置

        Returns:
            验证结果
        """
        config = self.get_ssl_config()
        errors = []
        warnings = []

        # 检查基本结构
        if not isinstance(config, dict):
            errors.append("SSL配置必须是字典类型")
            return {'valid': False, 'errors': errors, 'warnings': warnings}

        # 检查模块配置
        modules = config.get('modules', {})
        if not isinstance(modules, dict):
            errors.append("modules配置必须是字典类型")
        else:
            for module_name, module_config in modules.items():
                if not isinstance(module_config, dict):
                    errors.append(f"模块 {module_name} 的配置必须是字典类型")
                    continue

                # 检查域名配置
                domains = module_config.get('domains', {})
                if not isinstance(domains, dict):
                    warnings.append(f"模块 {module_name} 的domains配置应该是字典类型")

        # 检查安全性
        if not config.get('enabled', True):
            warnings.append("SSL全局禁用可能存在安全风险")

        # 检查通知器模块
        notifications_config = modules.get('notifications', {})
        if notifications_config.get('enabled', False):
            warnings.append("建议禁用通知器模块的SSL验证以避免连接问题")

        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }

    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        config = self.get_ssl_config()

        summary = {
            'ssl_enabled': config.get('enabled', True),
            'verify_certificates': config.get('verify_certificates', True),
            'modules': {}
        }

        for module_name, module_config in config.get('modules', {}).items():
            summary['modules'][module_name] = {
                'enabled': module_config.get('enabled', True),
                'verify': module_config.get('verify', True),
                'domain_count': len(module_config.get('domains', {}))
            }

        return summary

    def clear_ssl_cache(self):
        """清除SSL上下文缓存"""
        self._ssl_contexts.clear()
        self.logger.debug("SSL上下文缓存已清除")


# 全局SSL配置管理器实例
_ssl_manager = None


def get_ssl_manager(config_manager=None) -> SSLConfigManager:
    """
    获取全局SSL配置管理器实例

    Args:
        config_manager: 统一配置管理器实例

    Returns:
        SSL配置管理器实例
    """
    global _ssl_manager

    if _ssl_manager is None:
        _ssl_manager = SSLConfigManager(config_manager)

    return _ssl_manager


def should_verify_ssl(module: str, domain: str = None) -> bool:
    """
    便捷函数：检查是否应该验证SSL

    Args:
        module: 模块名称
        domain: 域名

    Returns:
        bool: 是否应该验证SSL
    """
    return get_ssl_manager().should_verify_ssl(module, domain)


def get_requests_ssl_config(module: str, domain: str = None) -> Dict[str, Any]:
    """
    便捷函数：获取requests的SSL配置

    Args:
        module: 模块名称
        domain: 域名

    Returns:
        包含verify参数的字典
    """
    ssl_manager = get_ssl_manager()
    return {
        'verify': ssl_manager.get_requests_verify_param(module, domain)
    }


def get_aiohttp_ssl_config(module: str, domain: str = None) -> Dict[str, Any]:
    """
    便捷函数：获取aiohttp的SSL配置

    Args:
        module: 模块名称
        domain: 域名

    Returns:
        包含ssl参数的字典
    """
    ssl_manager = get_ssl_manager()
    return {
        'ssl': ssl_manager.get_aiohttp_ssl_param(module, domain)
    }
