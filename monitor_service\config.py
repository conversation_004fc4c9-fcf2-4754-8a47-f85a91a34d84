"""
监控服务配置管理 - 基于统一配置管理器
简化版本 - 直接使用统一配置
"""

import os
import sys
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.unified_config import get_config


class MonitorServiceConfig:
    """监控服务配置类 - 基于统一配置管理器"""

    def __init__(self):
        # 获取统一配置
        self.unified_config = get_config()

    @property
    def host(self) -> str:
        """获取监控服务主机地址"""
        return self.unified_config.get('monitor_service.host', '127.0.0.1')

    @property
    def port(self) -> int:
        """获取监控服务端口"""
        return self.unified_config.get('monitor_service.port', 8088)

    @property
    def debug(self) -> bool:
        """获取调试模式设置"""
        return self.unified_config.get('monitor_service.debug', False)

    @property
    def database_file(self) -> str:
        """获取数据库文件路径"""
        return self.unified_config.get('database.file', 'monitoring.db')

    @property
    def log_level(self) -> str:
        """获取日志级别"""
        return self.unified_config.get('logging.level', 'INFO')

    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.unified_config.get_all_config()

    def reload(self):
        """重新加载配置"""
        self.unified_config.reload()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'host': self.host,
            'port': self.port,
            'debug': self.debug,
            'database_file': self.database_file,
            'log_level': self.log_level
        }


# 全局配置实例
monitor_config = MonitorServiceConfig()