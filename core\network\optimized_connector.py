"""优化连接器 - HTTP连接池性能优化配置"""

import aiohttp
import asyncio
import time
import socket
import os
from typing import Dict, Optional, Any, List
from collections import defaultdict, deque
from ..utils.unified_logging import Logger


class OptimizedConnectorConfig:
    """优化连接器配置类 - 封装连接池最佳实践"""

    # 预定义的优化配置方案 - 扩展版本
    PERFORMANCE_CONFIGS = {
        'proxy_optimized': {
            'limit': 80,               # 适中的连接池大小
            'limit_per_host': 25,      # 每个主机连接数
            'ttl_dns_cache': 400,      # DNS缓存时间
            'use_dns_cache': True,
            'keepalive_timeout': 45,   # 保持连接时间
            'enable_cleanup_closed': True,
            'timeout_sock_connect': 8, # 增加连接超时时间，适应代理连接
            'timeout_sock_read': 12,   # 增加读取超时时间
            'force_close': False,
            'family': socket.AF_INET,
            'happy_eyeballs_delay': 0.3,  # 稍微增加连接建立时间
        },
        'high_performance': {
            'limit': 200,              # 增加总连接池大小
            'limit_per_host': 50,      # 增加每个主机连接数
            'ttl_dns_cache': 600,      # 延长DNS缓存时间
            'use_dns_cache': True,
            'keepalive_timeout': 60,   # 保持连接时间
            'enable_cleanup_closed': True,
            'timeout_sock_connect': 5,
            'timeout_sock_read': 15,
            'force_close': False,      # 避免强制关闭连接
            'family': socket.AF_INET,  # 强制IPv4，提高稳定性
            'happy_eyeballs_delay': 0.25,  # 优化连接建立时间
        },
        'high_concurrency': {
            'limit': 100,
            'limit_per_host': 30,
            'ttl_dns_cache': 300,
            'use_dns_cache': True,
            'keepalive_timeout': 30,
            'enable_cleanup_closed': True,
            'timeout_sock_connect': 5,
            'timeout_sock_read': 10,
            'force_close': False,
            'family': socket.AF_INET,
            'happy_eyeballs_delay': 0.25,
        },
        'balanced': {
            'limit': 100,
            'limit_per_host': 30,
            'ttl_dns_cache': 300,
            'use_dns_cache': True,
            'keepalive_timeout': 30,
            'enable_cleanup_closed': True,
            'timeout_sock_connect': 8,  # 从3秒增加到8秒，解决代理连接超时
            'timeout_sock_read': 12,    # 从8秒增加到12秒
            'force_close': False,
            'family': socket.AF_INET,
            'happy_eyeballs_delay': 0.25,
        },
        'conservative': {
            'limit': 50,
            'limit_per_host': 10,
            'ttl_dns_cache': 120,
            'use_dns_cache': True,
            'keepalive_timeout': 15,
            'enable_cleanup_closed': True,
            'timeout_sock_connect': 6,  # 从3秒增加到6秒
            'timeout_sock_read': 8,     # 从5秒增加到8秒
            'force_close': True,       # 保守模式每次请求后关闭连接
            'family': socket.AF_INET,
            'happy_eyeballs_delay': 0.1,
        }
    }

    def __init__(self, profile: str = 'balanced', custom_config: Optional[Dict] = None):
        """
        初始化连接器配置

        Args:
            profile: 预定义配置方案 ('high_performance', 'high_concurrency', 'balanced', 'conservative')
            custom_config: 自定义配置覆盖
        """
        self.profile = profile
        self.config = self.PERFORMANCE_CONFIGS.get(profile, self.PERFORMANCE_CONFIGS['balanced']).copy()

        if custom_config:
            self.config.update(custom_config)

        self.logger = Logger(f"OptimizedConnector[{profile}]")
        self.logger.info(f"初始化连接器配置，方案: {profile}")

    def create_connector(self, ssl_context: bool = False) -> aiohttp.TCPConnector:
        """创建优化的TCP连接器"""
        try:
            connector_kwargs = {
                'ssl': ssl_context,
                'limit': self.config['limit'],
                'limit_per_host': self.config['limit_per_host'],
                'ttl_dns_cache': self.config['ttl_dns_cache'],
                'use_dns_cache': self.config['use_dns_cache'],
                'enable_cleanup_closed': self.config['enable_cleanup_closed'],
                'force_close': self.config.get('force_close', False),
                'family': self.config.get('family', socket.AF_INET),
                'happy_eyeballs_delay': self.config.get('happy_eyeballs_delay', 0.25),
            }

            # 处理参数冲突：当force_close=True时，不能设置keepalive_timeout
            if not self.config.get('force_close', False):
                connector_kwargs['keepalive_timeout'] = self.config['keepalive_timeout']
            else:
                self.logger.debug(f"配置方案 {self.profile}: force_close=True，已忽略keepalive_timeout参数")

            connector = aiohttp.TCPConnector(**connector_kwargs)

            self.logger.debug(f"创建优化连接器成功，配置: {self.get_config_summary()}")
            return connector

        except Exception as e:
            self.logger.error(f"创建连接器失败: {e}")
            # 降级为基础连接器
            return aiohttp.TCPConnector(ssl=ssl_context)

    def create_timeout(self, total: Optional[float] = None, connect: Optional[float] = None) -> aiohttp.ClientTimeout:
        """创建优化的超时配置"""
        return aiohttp.ClientTimeout(
            total=total or 10,
            connect=connect or self.config['timeout_sock_connect'],
            sock_read=self.config['timeout_sock_read']
        )

    def get_config_summary(self) -> str:
        """获取配置摘要"""
        return f"总连接:{self.config['limit']}, 单主机:{self.config['limit_per_host']}, DNS缓存:{self.config['ttl_dns_cache']}s"


class PerformanceMetrics:
    """性能指标收集器"""

    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.response_times = deque(maxlen=window_size)
        self.success_count = 0
        self.total_count = 0
        self.connection_errors = 0
        self.last_reset_time = time.time()

    def record_request(self, response_time: float, success: bool, connection_error: bool = False):
        """记录请求性能"""
        self.response_times.append(response_time)
        self.total_count += 1
        if success:
            self.success_count += 1
        if connection_error:
            self.connection_errors += 1

    def get_metrics(self) -> Dict[str, float]:
        """获取性能指标"""
        if not self.response_times:
            return {
                'success_rate': 0.0,
                'avg_response_time': 0.0,
                'connection_error_rate': 0.0
            }

        success_rate = self.success_count / self.total_count if self.total_count > 0 else 0.0
        avg_response_time = sum(self.response_times) / len(self.response_times)
        connection_error_rate = self.connection_errors / self.total_count if self.total_count > 0 else 0.0

        return {
            'success_rate': success_rate,
            'avg_response_time': avg_response_time,
            'connection_error_rate': connection_error_rate
        }

    def reset(self):
        """重置指标"""
        self.response_times.clear()
        self.success_count = 0
        self.total_count = 0
        self.connection_errors = 0
        self.last_reset_time = time.time()


class AdaptiveConnectorManager:
    """自适应连接器管理器 - 根据性能自动调整配置"""

    def __init__(self, logger: Optional[Logger] = None, default_profile: str = 'balanced'):
        self.logger = logger or Logger("AdaptiveConnectorManager")
        self.current_profile = default_profile
        self.current_connector = None
        self.current_session = None
        self.metrics = PerformanceMetrics()
        self.last_profile_switch = time.time()
        self.switch_cooldown = 300  # 5分钟切换冷却
        self.evaluation_interval = 60  # 1分钟评估一次
        self.last_evaluation = time.time()

        # 性能阈值
        self.thresholds = {
            'low_success_rate': 0.8,
            'high_success_rate': 0.95,
            'high_response_time': 3.0,
            'low_response_time': 1.0,
            'high_error_rate': 0.1
        }

    async def get_optimal_session(self, ssl_context: bool = False) -> aiohttp.ClientSession:
        """获取优化的会话，自动调整配置"""
        # 检查是否需要评估和调整
        current_time = time.time()
        if current_time - self.last_evaluation > self.evaluation_interval:
            await self._evaluate_and_adjust()
            self.last_evaluation = current_time

        # 创建或返回当前会话
        if self.current_session is None or self.current_session.closed:
            await self._create_new_session(ssl_context)

        return self.current_session

    async def _evaluate_and_adjust(self):
        """评估性能并调整配置"""
        metrics = self.metrics.get_metrics()

        if metrics['success_rate'] == 0.0:  # 没有足够数据
            return

        current_time = time.time()
        if current_time - self.last_profile_switch < self.switch_cooldown:
            return  # 冷却期内不切换

        new_profile = self._determine_optimal_profile(metrics)

        if new_profile != self.current_profile:
            await self._switch_profile(new_profile)

    def _determine_optimal_profile(self, metrics: Dict[str, float]) -> str:
        """根据性能指标决定最优配置"""
        success_rate = metrics['success_rate']
        avg_time = metrics['avg_response_time']
        error_rate = metrics['connection_error_rate']

        # 简化的系统资源检查
        cpu_count = os.cpu_count() or 4

        if success_rate < self.thresholds['low_success_rate'] or error_rate > self.thresholds['high_error_rate']:
            # 性能较差，使用保守配置
            return 'conservative'
        elif (success_rate > self.thresholds['high_success_rate'] and
              avg_time < self.thresholds['low_response_time'] and
              cpu_count >= 4):
            # 高性能且资源充足，使用高性能配置
            return 'high_performance'
        elif success_rate > self.thresholds['high_success_rate'] and avg_time < self.thresholds['high_response_time']:
            # 中等性能，使用平衡配置
            return 'balanced'
        else:
            # 默认平衡配置
            return 'balanced'

    async def _switch_profile(self, new_profile: str):
        """切换连接器配置"""
        self.logger.info(f"性能评估：切换连接器配置 {self.current_profile} -> {new_profile}")

        # 关闭旧会话，确保完全清理
        if self.current_session:
            try:
                # 获取连接器引用
                old_connector = getattr(self.current_session, '_connector', None)

                # 关闭会话
                await self.current_session.close()
                self.current_session = None

                # 等待会话完全关闭
                await asyncio.sleep(0.1)

                # 如果有连接器，确保它也被关闭
                if old_connector and not old_connector.closed:
                    await old_connector.close()
                    # 额外等待确保连接器完全关闭
                    await asyncio.sleep(0.1)

                self.logger.debug(f"旧连接器配置 {self.current_profile} 已完全清理")

            except Exception as e:
                # 使用统一异常处理
                from core.exceptions import handle_exception, is_ignorable_ssl_exception
                if is_ignorable_ssl_exception(e):
                    self.logger.debug(f"切换配置时出现可忽略的清理异常: {e}")
                else:
                    handled_e = handle_exception(e, context="切换连接器配置", logger=self.logger)
                    self.logger.warning(f"切换配置时清理旧资源失败: {handled_e}")

        self.current_profile = new_profile
        self.last_profile_switch = time.time()

        # 重置性能指标
        self.metrics.reset()

    async def _create_new_session(self, ssl_context: bool = False):
        """创建新的会话"""
        # 确保没有残留的旧会话
        if self.current_session and not self.current_session.closed:
            self.logger.warning("创建新会话时发现未关闭的旧会话，正在清理...")
            try:
                await self.current_session.close()
                await asyncio.sleep(0.05)  # 短暂等待确保清理完成
            except Exception as e:
                from core.exceptions import is_ignorable_ssl_exception
                if not is_ignorable_ssl_exception(e):
                    self.logger.warning(f"清理旧会话时出错: {e}")

        config = OptimizedConnectorConfig(self.current_profile)
        self.current_connector = config.create_connector(ssl_context)
        self.current_session = aiohttp.ClientSession(connector=self.current_connector)

        self.logger.debug(f"创建新会话，配置: {self.current_profile}")

    def record_request_performance(self, response_time: float, success: bool, connection_error: bool = False):
        """记录请求性能"""
        self.metrics.record_request(response_time, success, connection_error)

    async def close(self):
        """关闭管理器"""
        if self.current_session and not self.current_session.closed:
            try:
                # 获取连接器引用
                connector = getattr(self.current_session, '_connector', None)

                # 关闭会话
                await self.current_session.close()

                # 等待会话关闭
                await asyncio.sleep(0.1)

                # 如果连接器存在且未关闭，手动关闭它
                if connector and not connector.closed:
                    await connector.close()
                    await asyncio.sleep(0.1)

                self.logger.debug("AdaptiveConnectorManager 资源已完全清理")

            except Exception as e:
                from core.exceptions import handle_exception, is_ignorable_ssl_exception
                if is_ignorable_ssl_exception(e):
                    self.logger.debug(f"AdaptiveConnectorManager关闭时出现可忽略的异常: {e}")
                else:
                    handled_e = handle_exception(e, context="关闭AdaptiveConnectorManager", logger=self.logger)
                    self.logger.warning(f"AdaptiveConnectorManager关闭时出错: {handled_e}")

        self.current_session = None
        self.current_connector = None


class HousingMonitorConnector:
    """专为青城住房监控系统优化的连接器"""

    def __init__(self, logger: Optional[Logger] = None):
        self.logger = logger or Logger("HousingMonitorConnector")
        # 使用专门的代理优化配置
        self.adaptive_manager = AdaptiveConnectorManager(logger, default_profile='proxy_optimized')
        self._warmup_lock = asyncio.Lock()

        # 预热常用域名的DNS
        self.target_domains = [
            'www.huhhothome.cn',
            'api.huhhothome.cn',
            # 其他常用域名可以在这里添加
        ]

        self.warmup_completed = False

    async def get_session(self, ssl_context: bool = False) -> aiohttp.ClientSession:
        """获取优化的会话"""
        # 首次使用时预热连接，使用锁防止并发预热
        if not self.warmup_completed:
            async with self._warmup_lock:
                # 再次检查，因为在等待锁的过程中可能其他协程已经完成了预热
                if not self.warmup_completed:
                    await self.warmup_connections()
                    self.warmup_completed = True

        return await self.adaptive_manager.get_optimal_session(ssl_context)

    async def warmup_connections(self):
        """预热连接池"""
        self.logger.info("开始预热连接池...")
        tasks = []

        for domain in self.target_domains:
            tasks.append(self._warmup_domain(domain))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        success_count = sum(1 for r in results if not isinstance(r, Exception))
        self.logger.info(f"连接预热完成: {success_count}/{len(self.target_domains)} 个域名成功")

    async def _warmup_domain(self, domain: str):
        """预热单个域名的连接"""
        try:
            session = await self.adaptive_manager.get_optimal_session()
            timeout = aiohttp.ClientTimeout(total=5)

            async with session.get(f'http://{domain}', timeout=timeout) as response:
                pass  # 只是建立连接，不处理响应

            self.logger.debug(f"预热域名 {domain} 成功")
        except Exception as e:
            self.logger.debug(f"预热域名 {domain} 失败: {e}")

    def record_performance(self, response_time: float, success: bool, connection_error: bool = False):
        """记录性能指标"""
        self.adaptive_manager.record_request_performance(response_time, success, connection_error)

    async def close(self):
        """关闭连接器"""
        self.logger.debug("正在关闭HousingMonitorConnector...")
        try:
            await self.adaptive_manager.close()
            # 额外等待确保所有异步清理完成
            await asyncio.sleep(0.1)
            self.logger.debug("HousingMonitorConnector已成功关闭")
        except Exception as e:
            from core.exceptions import handle_exception, is_ignorable_ssl_exception
            if is_ignorable_ssl_exception(e):
                self.logger.debug(f"HousingMonitorConnector关闭时出现可忽略的异常: {e}")
            else:
                handled_e = handle_exception(e, context="关闭HousingMonitorConnector", logger=self.logger)
                self.logger.warning(f"HousingMonitorConnector关闭时出错: {handled_e}")


class ConnectorFactory:
    """连接器工厂 - 提供统一的连接器创建接口"""

    _instance = None
    _connectors_cache = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.logger = Logger("ConnectorFactory")
            cls._instance._housing_connector = None
        return cls._instance

    def get_optimized_connector(self, profile: str = 'balanced', ssl_context: bool = False,
                              custom_config: Optional[Dict] = None) -> aiohttp.TCPConnector:
        """获取优化连接器 (带缓存)"""
        cache_key = f"{profile}_{ssl_context}_{hash(str(custom_config) if custom_config else 'default')}"

        if cache_key not in self._connectors_cache:
            config = OptimizedConnectorConfig(profile, custom_config)
            connector = config.create_connector(ssl_context)
            self._connectors_cache[cache_key] = (connector, config)
            self.logger.debug(f"缓存新连接器: {cache_key}")

        connector, config = self._connectors_cache[cache_key]
        return connector

    def get_housing_monitor_connector(self) -> HousingMonitorConnector:
        """获取专门的住房监控连接器"""
        if self._housing_connector is None:
            self._housing_connector = HousingMonitorConnector(self.logger)
        return self._housing_connector

    async def reset_housing_monitor_connector(self):
        """关闭并重置当前的住房监控连接器实例"""
        if self._housing_connector:
            self.logger.info("开始重置住房监控连接器...")
            try:
                # 彻底关闭旧的连接器
                await self._housing_connector.close()
                self.logger.debug("旧的住房监控连接器已关闭")

                # 强制等待确保完全清理
                await asyncio.sleep(0.2)

            except Exception as e:
                from core.exceptions import is_ignorable_ssl_exception
                if not is_ignorable_ssl_exception(e):
                    self.logger.warning(f"关闭旧的住房监控连接器时出错: {e}")
            finally:
                self._housing_connector = None
                self.logger.info("住房监控连接器已重置，将在下次请求时创建新实例")

        # 清理连接器缓存，确保下次创建全新连接器
        if self._connectors_cache:
            cache_size = len(self._connectors_cache)
            self._connectors_cache.clear()
            self.logger.debug(f"已清理 {cache_size} 个缓存的连接器")

    async def force_clear_all_connections(self):
        """强制清理所有连接器和缓存（用于代理切换时的彻底重置）"""
        self.logger.info("开始强制清理所有连接器...")

        cleanup_tasks = []

        # 关闭住房监控连接器
        if self._housing_connector:
            cleanup_tasks.append(self._housing_connector.close())

        # 关闭所有缓存的连接器
        for cache_key, (connector, config) in self._connectors_cache.items():
            if connector and not connector.closed:
                cleanup_tasks.append(connector.close())

        # 并发执行所有清理任务
        if cleanup_tasks:
            results = await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            errors = [r for r in results if isinstance(r, Exception)]
            if errors:
                self.logger.warning(f"清理过程中出现 {len(errors)} 个错误，但不影响重置")

        # 清理缓存和实例
        cache_size = len(self._connectors_cache)
        self._connectors_cache.clear()
        self._housing_connector = None

        # 强制等待确保所有连接完全释放
        await asyncio.sleep(0.3)

        self.logger.info(f"强制清理完成，已清理住房连接器和 {cache_size} 个缓存连接器")

    async def warmup_connections_with_proxy(self, proxy_url: Optional[str] = None):
        """使用新代理预热连接（在代理切换后调用）"""
        self.logger.info("开始使用新代理预热连接...")
        try:
            # 创建新的住房监控连接器
            housing_connector = self.get_housing_monitor_connector()

            # 预热连接池
            await housing_connector.warmup_connections()

            self.logger.info("新代理连接预热完成")
        except Exception as e:
            self.logger.warning(f"预热连接失败，但不影响正常使用: {e}")

    def get_connector_config(self, profile: str = 'balanced') -> OptimizedConnectorConfig:
        """获取连接器配置实例"""
        return OptimizedConnectorConfig(profile)

    async def close_all_cached_connectors(self):
        """关闭所有缓存的连接器"""
        self.logger.info("开始关闭所有缓存的连接器...")

        cleanup_errors = []

        # 关闭住房监控连接器
        if self._housing_connector:
            try:
                await self._housing_connector.close()
                self._housing_connector = None
                self.logger.debug("住房监控连接器已关闭")
            except Exception as e:
                cleanup_errors.append(f"住房监控连接器: {e}")

        # 关闭缓存的连接器
        for cache_key, (connector, config) in self._connectors_cache.items():
            try:
                if not connector.closed:
                    await connector.close()
                    # 等待连接器完全关闭
                    await asyncio.sleep(0.05)
                    self.logger.debug(f"已关闭连接器: {cache_key}")
            except Exception as e:
                from core.exceptions import is_ignorable_ssl_exception
                if is_ignorable_ssl_exception(e):
                    self.logger.debug(f"关闭连接器时出现可忽略的异常 {cache_key}: {e}")
                else:
                    cleanup_errors.append(f"{cache_key}: {e}")

        # 清理缓存
        self._connectors_cache.clear()

        # 最终等待，确保所有清理操作完成
        await asyncio.sleep(0.2)

        # 报告清理结果
        if cleanup_errors:
            self.logger.warning(f"关闭连接器时出现 {len(cleanup_errors)} 个错误: {'; '.join(cleanup_errors)}")

        self.logger.info("所有缓存连接器关闭完成")


class ConnectionPoolMonitor:
    """连接池监控器 - 提供连接池使用统计"""

    def __init__(self, connector: aiohttp.TCPConnector, name: str = "Unknown"):
        self.connector = connector
        self.name = name
        self.logger = Logger(f"PoolMonitor[{name}]")
        self.start_time = time.time()

    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        try:
            stats = {
                'name': self.name,
                'total_connections': len(self.connector._conns) if hasattr(self.connector, '_conns') else 0,
                'limit': self.connector.limit,
                'limit_per_host': self.connector.limit_per_host,
                'closed': self.connector.closed,
                'uptime_seconds': time.time() - self.start_time
            }

            # 计算使用率
            if stats['limit'] > 0:
                stats['usage_percentage'] = (stats['total_connections'] / stats['limit']) * 100
            else:
                stats['usage_percentage'] = 0

            return stats

        except Exception as e:
            self.logger.warning(f"获取连接池统计失败: {e}")
            return {'name': self.name, 'error': str(e)}

    def log_stats(self):
        """记录连接池统计到日志"""
        stats = self.get_stats()
        if 'error' not in stats:
            self.logger.info(f"连接池状态: {stats['total_connections']}/{stats['limit']} "
                           f"连接 ({stats['usage_percentage']:.1f}% 使用率)")
        else:
            self.logger.warning(f"无法获取连接池统计: {stats['error']}")


# 全局连接器工厂实例
connector_factory = ConnectorFactory()

# 便捷函数
def get_optimized_connector(profile: str = 'balanced', ssl_context: bool = False) -> aiohttp.TCPConnector:
    """获取优化连接器的便捷函数"""
    return connector_factory.get_optimized_connector(profile, ssl_context)

def get_housing_monitor_connector() -> HousingMonitorConnector:
    """获取住房监控专用连接器的便捷函数"""
    return connector_factory.get_housing_monitor_connector()

async def reset_housing_monitor_connector():
    """重置全局住房监控连接器的便捷函数"""
    await connector_factory.reset_housing_monitor_connector()

def create_connector_config(profile: str = 'balanced') -> OptimizedConnectorConfig:
    """创建连接器配置的便捷函数"""
    return OptimizedConnectorConfig(profile)