"""
监控服务主入口 - 集成API服务器和监控核心
"""

import asyncio
import logging
import signal
import sys
import os
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from monitor_service.config import monitor_config
from monitor_service.core.monitor import MonitorService
from monitor_service.api.server import MonitorAPIServer
from core.utils.unified_logging import setup_unified_logging


class MonitorServiceApp:
    """监控服务应用主类"""

    def __init__(self):
        self.monitor_service: Optional[MonitorService] = None
        self.api_server: Optional[MonitorAPIServer] = None
        self.api_runner = None
        self.logger = setup_unified_logging("MonitorServiceApp")
        self.shutdown_requested = False

    def setup_logging(self):
        """设置日志配置 - 现在使用统一日志系统"""
        # 统一日志系统已在__init__中初始化，这里只需要记录启动信息
        self.logger.info("监控服务应用日志系统初始化完成")

    async def initialize(self):
        """初始化监控服务和API服务器"""
        try:
            self.logger.info("正在初始化监控服务...")

            # 创建监控服务实例
            self.monitor_service = MonitorService()

            # 创建API服务器实例并设置监控服务
            self.api_server = MonitorAPIServer(self.monitor_service)

            self.logger.info("监控服务初始化完成")

        except Exception as e:
            self.logger.error(f"初始化监控服务失败: {str(e)}")
            raise

    async def start_api_server(self):
        """启动API服务器"""
        try:
            self.logger.info("正在启动API服务器...")
            self.api_runner = await self.api_server.start_server()
            self.logger.info(f"API服务器已启动，监听 {monitor_config.host}:{monitor_config.port}")
        except Exception as e:
            self.logger.error(f"启动API服务器失败: {str(e)}")
            raise

    def setup_signal_handlers(self):
        """设置信号处理器"""
        # Windows系统下信号处理器支持有限，直接使用默认的KeyboardInterrupt处理
        if sys.platform == 'win32':
            self.logger.debug("Windows系统：使用默认的KeyboardInterrupt处理方式")
            return

        try:
            loop = asyncio.get_running_loop()

            def signal_handler(signame):
                self.logger.info(f"收到信号 {signame}，开始优雅关闭...")
                self.shutdown_requested = True
                asyncio.create_task(self.shutdown())

            # 仅在非Windows系统注册信号处理器
            signals_to_handle = [signal.SIGINT, signal.SIGTERM]

            for sig in signals_to_handle:
                try:
                    loop.add_signal_handler(sig, lambda s=sig: signal_handler(s))
                    self.logger.debug(f"成功注册信号处理器: {sig}")
                except (OSError, RuntimeError, NotImplementedError) as e:
                    self.logger.debug(f"跳过信号处理器 {sig}: {e}")

        except Exception as e:
            self.logger.debug(f"设置信号处理器失败: {e}，将使用默认处理方式")

    async def run(self):
        """运行监控服务应用"""
        try:
            # 设置日志
            self.setup_logging()

            # 初始化服务
            await self.initialize()

            # 启动API服务器
            await self.start_api_server()

            # 设置信号处理器
            self.setup_signal_handlers()

            self.logger.info("监控服务应用启动完成")
            self.logger.info(f"API服务器地址: http://{monitor_config.host}:{monitor_config.port}")
            self.logger.info("可用端点:")
            self.logger.info("  GET /health        - 健康检查")
            self.logger.info("  GET /status        - 获取监控状态")
            self.logger.info("  POST /start        - 启动监控")
            self.logger.info("  POST /stop         - 停止监控")
            self.logger.info("  POST /reload-config - 重载配置")
            self.logger.info("  GET /proxy/status  - 获取代理状态")
            self.logger.info("  POST /proxy/refresh - 刷新代理")

            # 保持服务运行
            while not self.shutdown_requested:
                await asyncio.sleep(1)

        except Exception as e:
            self.logger.error(f"运行监控服务应用时出错: {str(e)}")
            raise

    async def shutdown(self):
        """优雅关闭应用"""
        if self.shutdown_requested:
            return

        self.shutdown_requested = True
        self.logger.info("开始关闭监控服务应用...")

        try:
            # 关闭监控服务
            if self.monitor_service:
                self.logger.info("正在关闭监控服务...")
                if hasattr(self.monitor_service, 'state_manager') and self.monitor_service.state_manager.is_running:
                    await self.monitor_service.shutdown("app_shutdown")

            # 关闭API服务器
            if self.api_runner:
                self.logger.info("正在关闭API服务器...")
                await self.api_runner.cleanup()

            self.logger.info("监控服务应用已优雅关闭")

        except Exception as e:
            self.logger.error(f"关闭应用时出错: {str(e)}")


async def main():
    """主函数"""
    app = MonitorServiceApp()

    try:
        await app.run()
    except KeyboardInterrupt:
        print("\n收到中断信号，正在关闭...")
    except Exception as e:
        logging.error(f"监控服务应用异常退出: {str(e)}")
        sys.exit(1)
    finally:
        if not app.shutdown_requested:
            await app.shutdown()


# 此模块由 start_monitor_service.py 启动，不需要直接运行