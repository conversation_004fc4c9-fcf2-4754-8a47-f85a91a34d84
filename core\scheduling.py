import threading
import time
from datetime import datetime, timedelta
import logging
from typing import Dict, Optional, List, Callable, Any

class ScheduledTask:
    """表示一个定时任务"""
    def __init__(self, name: str, action: Callable[[str], None], enabled: bool = True):
        self.name = name                # 任务名称
        self.action = action            # 要执行的操作（函数）
        self.enabled = enabled          # 是否启用
        self.start_time: Optional[str] = None    # 启动时间（HH:MM格式）
        self.stop_time: Optional[str] = None     # 停止时间（HH:MM格式）
        self.days: List[int] = []       # 执行日期 [0-6]，0表示周一，6表示周日
        self.last_run: Optional[datetime] = None    # 上次执行时间
        self._last_executed_minute: Optional[int] = None  # 防止同一分钟内重复执行

    def get_next_occurrence(self, from_time: datetime, time_str: Optional[str]) -> Optional[datetime]:
        """计算下一个发生时间"""
        if not time_str:
            return None

        try:
            hour, minute = map(int, time_str.split(':'))
        except (ValueError, AttributeError):
            return None

        # 如果没有设置执行日期，则假设每天都执行
        days_to_check = self.days if self.days else list(range(7))
        if not days_to_check: # 如果days为空列表
            return None

        # 检查从今天开始的8天（包括今天和未来7天）
        for i in range(8):
            check_date = from_time.date() + timedelta(days=i)

            if check_date.weekday() not in days_to_check:
                continue

            next_occurrence = datetime.combine(check_date, datetime.min.time()).replace(hour=hour, minute=minute)

            # 如果是今天，并且时间已经过去，则跳过
            if i == 0 and next_occurrence <= from_time:
                continue

            return next_occurrence

        return None

    def should_run(self, current_time: datetime) -> bool:
        """检查任务是否应该在当前时间运行"""
        if not self.enabled or not self.start_time:
            return False

        # 防止在同一分钟内重复执行
        current_minute = current_time.hour * 60 + current_time.minute
        if self._last_executed_minute == current_minute:
            return False

        # 解析时间
        current_hour, current_minute_of_hour = current_time.hour, current_time.minute
        try:
            start_hour, start_minute = map(int, self.start_time.split(':'))
        except (ValueError, AttributeError):
            return False

        # 检查是否是指定的星期几
        if self.days and current_time.weekday() not in self.days:
            return False

        # 检查时间匹配
        is_time_match = current_hour == start_hour and current_minute_of_hour == start_minute

        if is_time_match:
            self._last_executed_minute = current_minute

        return is_time_match

    def should_stop(self, current_time: datetime) -> bool:
        """检查任务是否应该在当前时间停止"""
        if not self.enabled or not self.stop_time:
            return False

        # 解析时间
        current_hour, current_minute = current_time.hour, current_time.minute
        try:
            stop_hour, stop_minute = map(int, self.stop_time.split(':'))
        except (ValueError, AttributeError):
            return False

        # 检查是否是指定的星期几
        if self.days and current_time.weekday() not in self.days:
            return False

        # 检查时间匹配
        return current_hour == stop_hour and current_minute == stop_minute

class Scheduler:
    """定时任务调度器 - 线程安全版本"""
    def __init__(self, logger: Optional[logging.Logger] = None):
        self._tasks: Dict[str, ScheduledTask] = {}    # 任务列表
        self._running = False           # 调度器运行状态
        self._thread: Optional[threading.Thread] = None    # 调度线程
        self._lock = threading.RLock()  # 可重入锁，保护共享资源
        self._stop_event = threading.Event()  # 停止事件
        self.logger = logger or logging.getLogger(__name__)

    @property
    def tasks(self) -> Dict[str, ScheduledTask]:
        """获取任务字典的只读副本"""
        with self._lock:
            return self._tasks.copy()

    @property
    def running(self) -> bool:
        """获取运行状态"""
        with self._lock:
            return self._running

    def add_task(self, task: ScheduledTask) -> ScheduledTask:
        """添加任务 - 线程安全"""
        with self._lock:
            self._tasks[task.name] = task
            self.logger.info(f"添加任务: {task.name}")
            return task

    def remove_task(self, task_name: str) -> bool:
        """移除任务 - 线程安全"""
        with self._lock:
            if task_name in self._tasks:
                del self._tasks[task_name]
                self.logger.info(f"移除任务: {task_name}")
                return True
            return False

    def get_task(self, task_name: str) -> Optional[ScheduledTask]:
        """获取任务 - 线程安全"""
        with self._lock:
            return self._tasks.get(task_name)

    def get_all_tasks(self) -> List[ScheduledTask]:
        """获取所有任务 - 线程安全"""
        with self._lock:
            return list(self._tasks.values())

    def update_task(self, task_name: str, **kwargs: Any) -> bool:
        """更新任务 - 线程安全"""
        with self._lock:
            task = self._tasks.get(task_name)
            if not task:
                return False

            for key, value in kwargs.items():
                if hasattr(task, key):
                    setattr(task, key, value)

            self.logger.info(f"更新任务: {task_name}, 参数: {kwargs}")
            return True

    def clear_tasks(self) -> None:
        """清空所有任务 - 线程安全"""
        with self._lock:
            removed_count = len(self._tasks)
            self._tasks.clear()
            self.logger.info(f"已清空所有任务，共移除 {removed_count} 个任务")

    def update_tasks_from_dict(self, data: Dict[str, Any], action_func: Callable[[str], None]) -> bool:
        """从字典批量更新任务配置 - 线程安全

        Args:
            data: 包含tasks字典的配置数据
            action_func: 任务执行函数

        Returns:
            bool: 操作是否成功
        """
        if not data or not isinstance(data, dict):
            self.logger.error("批量更新任务失败：无效的配置数据")
            return False

        task_configs = data.get("tasks", {})
        if not isinstance(task_configs, dict):
            self.logger.error("批量更新任务失败：tasks不是字典类型")
            return False

        # 空的tasks字典也视为无效配置
        if not task_configs:
            self.logger.error("批量更新任务失败：tasks字典为空")
            return False

        with self._lock:
            try:
                # 清空现有任务
                old_task_count = len(self._tasks)
                self._tasks.clear()

                # 添加新任务
                for name, config in task_configs.items():
                    if not isinstance(config, dict):
                        self.logger.warning(f"跳过无效的任务配置: {name}")
                        continue

                    task = ScheduledTask(name, action_func)
                    task.enabled = config.get("enabled", True)
                    task.start_time = config.get("start_time")
                    task.stop_time = config.get("stop_time")
                    task.days = config.get("days", [])

                    # 恢复最后执行时间
                    last_run_str = config.get("last_run")
                    if last_run_str:
                        try:
                            task.last_run = datetime.fromisoformat(last_run_str)
                        except (ValueError, TypeError):
                            task.last_run = None

                    self._tasks[name] = task

                new_task_count = len(self._tasks)
                self.logger.info(f"批量更新任务完成：移除 {old_task_count} 个，添加 {new_task_count} 个")
                return True

            except Exception as e:
                self.logger.error(f"批量更新任务时发生异常: {str(e)}", exc_info=True)
                return False

    def start(self) -> bool:
        """启动调度器 - 线程安全"""
        with self._lock:
            if self._running:
                self.logger.warning("调度器已在运行中")
                return False

            self._running = True
            self._stop_event.clear()
            self._thread = threading.Thread(target=self._run, daemon=True, name="Scheduler-Thread")
            self._thread.start()
            self.logger.info("调度器已启动")
            return True

    def stop(self, timeout: float = 5.0) -> bool:
        """停止调度器 - 线程安全"""
        with self._lock:
            if not self._running:
                return True

            self.logger.info("正在停止调度器...")
            self._running = False
            self._stop_event.set()

        # 在锁外等待线程结束
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=timeout)
            if self._thread.is_alive():
                self.logger.warning(f"调度器线程在 {timeout} 秒后仍未结束")
                return False

        self.logger.info("调度器已停止")
        return True

    def _run(self) -> None:
        """运行调度器主循环 - 改进的时间精度和线程安全"""
        last_check_time = datetime.now()

        while self._running:
            try:
                # 使用事件等待而不是简单的sleep，提高响应性
                if self._stop_event.wait(timeout=1.0):  # 1秒检查一次，更加精确
                    break

                current_time = datetime.now()

                # 只在分钟边界检查任务，避免重复执行
                if (current_time.minute != last_check_time.minute or
                    current_time.hour != last_check_time.hour or
                    current_time.day != last_check_time.day):

                    self._check_and_execute_tasks(current_time)
                    last_check_time = current_time

            except Exception as e:
                self.logger.error(f"调度器运行错误: {str(e)}", exc_info=True)
                # 出错后等待一段时间，避免快速失败循环
                if not self._stop_event.wait(timeout=10.0):
                    continue
                else:
                    break

        self.logger.info("调度器主循环已退出")

    def _check_and_execute_tasks(self, current_time: datetime) -> None:
        """检查并执行任务 - 线程安全"""
        # 获取任务的快照，避免在迭代过程中字典被修改
        with self._lock:
            tasks_snapshot = list(self._tasks.items())

        for task_name, task in tasks_snapshot:
            if not self._running:  # 检查是否需要停止
                break

            try:
                # 检查启动任务
                if task.should_run(current_time):
                    self._execute_task_action(task, "start", current_time)

                # 检查停止任务
                if task.should_stop(current_time):
                    self._execute_task_action(task, "stop", current_time)

            except Exception as e:
                self.logger.error(f"检查任务 {task_name} 时发生错误: {str(e)}", exc_info=True)

    def _execute_task_action(self, task: ScheduledTask, action: str, current_time: datetime) -> None:
        """执行任务动作"""
        try:
            self.logger.info(f"执行任务: {task.name} ({action}) - {current_time.strftime('%H:%M:%S')}")
            task.action(action)

            # 更新最后执行时间
            with self._lock:
                task.last_run = current_time

        except Exception as e:
            self.logger.error(f"执行任务 {task.name} ({action}) 失败: {str(e)}", exc_info=True)

    def to_dict(self) -> Dict[str, Any]:
        """将调度器配置转换为字典 - 线程安全"""
        with self._lock:
            result = {"tasks": {}}

            for name, task in self._tasks.items():
                result["tasks"][name] = {
                    "enabled": task.enabled,
                    "start_time": task.start_time,
                    "stop_time": task.stop_time,
                    "days": task.days,
                    "last_run": task.last_run.isoformat() if task.last_run else None
                }

            return result

    def from_dict(self, data: Dict[str, Any], action_func: Callable[[str], None]) -> None:
        """从字典恢复调度器配置 - 线程安全"""
        if not data or not isinstance(data, dict):
            return

        with self._lock:
            task_configs = data.get("tasks", {})
            for name, config in task_configs.items():
                task = ScheduledTask(name, action_func)
                task.enabled = config.get("enabled", True)
                task.start_time = config.get("start_time")
                task.stop_time = config.get("stop_time")
                task.days = config.get("days", [])

                # 恢复最后执行时间
                last_run_str = config.get("last_run")
                if last_run_str:
                    try:
                        task.last_run = datetime.fromisoformat(last_run_str)
                    except (ValueError, TypeError):
                        task.last_run = None

                self._tasks[name] = task
                self.logger.info(f"从配置恢复任务: {name}")

    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态信息"""
        with self._lock:
            tasks_list = []
            now = datetime.now()

            for task in self._tasks.values():
                next_start = task.get_next_occurrence(now, task.start_time)
                next_stop = task.get_next_occurrence(now, task.stop_time)

                is_running_period = False
                remaining_time_str = None

                # 只有当任务启用，并且有有效的起止时间时，才进行判断
                if task.enabled and task.start_time and task.stop_time:
                    # 获取上一个开始时间
                    last_start_for_stop_check = task.get_next_occurrence((now - timedelta(days=7)).replace(hour=0), task.start_time)
                    # 获取上一个停止时间
                    last_stop_for_start_check = task.get_next_occurrence((now - timedelta(days=7)).replace(hour=0), task.stop_time)

                    # 复杂的周期判断：
                    # 1. 下一个停止时间早于下一个启动时间，说明当前在运行周期内
                    # 2. 或者，上一个启动时间晚于上一个停止时间，也说明在运行周期内
                    if (next_stop and next_start and next_stop < next_start) or \
                       (last_start_for_stop_check and last_stop_for_start_check and last_start_for_stop_check > last_stop_for_start_check):
                        is_running_period = True
                        if next_stop:
                            remaining_delta = next_stop - now
                            if remaining_delta.total_seconds() > 0:
                                hours, remainder = divmod(int(remaining_delta.total_seconds()), 3600)
                                minutes, seconds = divmod(remainder, 60)
                                remaining_time_str = f"{hours}小时{minutes}分钟" if hours > 0 else f"{minutes}分钟"

                tasks_list.append({
                    "name": task.name,
                    "enabled": task.enabled,
                    "start_time": task.start_time,
                    "stop_time": task.stop_time,
                    "days": task.days,
                    "last_run": task.last_run.isoformat() if task.last_run else None,
                    "next_start": next_start.isoformat() if next_start else None,
                    "next_stop": next_stop.isoformat() if next_stop else None,
                    "is_running_period": is_running_period,
                    "remaining_time": remaining_time_str
                })

            # 假设只有一个任务，直接返回该任务的状态
            final_status = tasks_list[0] if tasks_list else {}
            final_status.update({
                "running": self._running,
                "task_count": len(self._tasks),
                "thread_alive": self._thread.is_alive() if self._thread else False,
            })
            return final_status

    def __del__(self):
        """析构函数，确保清理资源"""
        try:
            if self._running:
                self.stop(timeout=2.0)
        except Exception:
            pass  # 忽略清理时的异常