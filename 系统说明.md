# 青城住房监控系统 - 系统说明文档

本文档详细介绍了青城住房监控系统的设计、功能和技术实现。

## 1. 项目结构分析

本章节分析项目的整体目录结构、文件组织方式以及各个核心模块的职责。

### 1.1. 总体结构

系统采用前后端分离的微服务架构，主要由以下几个部分组成：

- **主控制脚本**: 负责统一启动和管理系统中的各个服务。
- **核心业务逻辑 (`core`)**: 包含项目所有核心功能，如数据抓取、监控、数据库交互等。
- **监控服务 (`monitor_service`)**: 一个独立的服务，负责执行实际的监控任务。
- **Web控制台 (`web`)**: 基于Flask的Web应用，提供用户交互界面和API。
- **配置文件 (`config`, `*.yaml`)**: 管理不同环境下的所有配置项。
- **工具和服务 (`services`, `utils`)**: 提供辅助功能，如通知、日志等。
- **ZDQF**: 包含一些用于自动登录和模拟操作的特定脚本和配置文件。

### 1.2. 目录与文件职责

| 目录/文件 | 主要职责 |
| :--- | :--- |
| `start_all.py` | **主启动脚本**。负责管理和监控 `monitor_service` 和 `web` 两个核心服务的生命周期，包括启动、停止、健康检查和日志记录。 |
| `requirements.txt` | **项目依赖**。列出了项目运行所需的所有Python库，如Flask, SQLAlchemy, requests等。 |
| `config/` | **配置中心**。`unified_config.py` 实现了统一的配置加载逻辑，整合了默认配置 (`default_config.py`)、环境特定配置 (`config_dev.yaml`, `config_prod.yaml`) 和用户自定义配置 (`user_config.json`)。 |
| `core/` | **核心业务逻辑**。这是系统的核心，包含了大部分业务功能的实现。 |
| `core/data/` | **数据层**。使用SQLAlchemy定义数据库模型 (`models.py`)，并提供数据库会话管理 (`database.py`) 和数据操作 (`manager.py`)。 |
| `core/grab/` | **抓取模块**。`grab_executor.py` 实现了数据抓取的核心逻辑，负责从目标网站获取房源信息。 |
| `core/monitor/` | **监控逻辑**。`house_monitor.py` 包含了房源监控的核心算法和逻辑，`state_manager.py` 负责管理监控状态。 |
| `core/network/` | **网络处理**。封装了HTTP客户端 (`http_client.py`)、代理管理 (`proxy_manager.py`) 和动态请求头生成等功能，用于模拟真实的网络请求。 |
| `core/scheduling.py` | **任务调度**。实现了基于时间的任务调度器，用于定时执行监控和抓取任务。 |
| `core/utils/` | **通用工具**。提供了日志 (`unified_logging.py`)、数据验证 (`validators.py`)、敏感信息脱敏 (`sensitive_data_masker.py`) 等一系列通用工具函数。 |
| `monitor_service/` | **监控服务**。一个独立的Flask应用，通过API接收指令，并调用 `core` 模块执行具体的监控任务。这是系统后台任务的执行者。 |
| `web/` | **Web控制台**。项目的前端和Web API部分，也是一个独立的Flask应用。 |
| `web/api/` | **后端API**。提供了丰富的REST API，用于前端与后端的数据交互，涵盖设备管理、房源信息、系统配置、通知等。 |
| `web/auth/` | **用户认证**。处理用户登录和会话管理。 |
| `web/views/` | **前端视图**。基于Flask的视图函数，渲染HTML页面 (`templates/`)。 |
| `templates/` | **HTML模板**。存放所有前端页面的HTML模板文件。 |
| `static/` | **静态文件**。存放CSS、JavaScript、图片等前端静态资源。 |
| `services/notification/` | **通知服务**。实现了多种通知渠道（如 Bark, WxPusher）的管理和消息发送。 |
| `ZDQF/` | **特定功能目录**。包含用于处理特定任务（如模拟“智慧区县”平台登录）的脚本和用户配置文件。|

## 2. 系统功能实现

本章节详细分析监控系统的核心功能实现，包括房源监控、代理管理、通知系统、数据库模型和任务调度机制。

### 2.1. 数据库模型

系统的核心数据模型定义在 `core/data/models.py` 中，使用 `dataclasses` 实现，清晰且易于维护。

- **`Device`**: 代表一个通知设备，如手机或PC。
  - `id`: 唯一标识符。
  - `name`: 设备名称。
  - `type`: 设备类型（如 `bark`, `wxpush`）。
  - `config`: 包含特定于设备的配置，如API密钥或用户ID。

- **`House`**: 代表一个需要监控的小区或房源项目。
  - `name`: 小区名称，作为唯一标识。
  - `enabled`: 是否启用监控。
  - `device_ids`: 关联的通知设备ID列表。
  - `last_count`: 上次监控到的房源数量。

- **`HouseDetail`**: 代表一个具体的房源信息。
  - `id`: 房源的唯一ID。
  - `position`: 位置（如楼号、单元）。
  - `roomno`: 房间号。
  - `area`, `rent`, `direction`, `outtype`: 面积、租金、朝向、户型等。

- **`MonitorResult`**: 封装一次监控任务的结果。
  - `house_name`: 监控的小区名称。
  - `old_count`, `new_count`: 变化前后的数量。
  - `change_type`: 变化类型（增加、减少、无变化）。
  - `details`: 如果是新增，则包含 `HouseDetail` 列表。

### 2.2. 房源监控流程

房源监控是系统的核心功能，其主要逻辑在 `core/monitor/house_monitor.py` 中实现。

1.  **触发监控**: 调度器在指定时间触发监控任务。
2.  **获取最新数据**: `HouseMonitor` 调用 `ApiClient` (`core/network/api_client.py`) 异步从目标网站API获取指定小区的最新房源数量和`estate_id`。
3.  **对比历史数据**: 从 `HistoryManager` (`core/data/history_manager.py`) 中获取上次保存的房源数量。
4.  **无变化**: 如果数量没有变化，记录日志并结束。
5.  **数量增加**:
    -   立即发送一条简单的数量增加通知。
    -   调用 `ApiClient` 获取该小区所有房源的详细列表。
    -   通过与 `HistoryManager` 中存储的已知房源ID集合进行比对，识别出“真正”的新增房源。
    -   发送包含新增房源详细信息（户型、面积、租金等）的第二条通知。
    -   **触发抢房逻辑**: 如果有配置好的抢房设备 (`grab_devices`)，系统会检查新房源是否满足设备上设置的条件（如面积、楼层、朝向等），如果满足，则调用 `GrabExecutor` 执行自动抢房流程。
6.  **数量减少**: 发送一条数量减少的通知，并更新基线数据。
7.  **更新历史记录**: 将最新的房源数量和ID集合写回 `HistoryManager`，为下次监控做准备。

### 2.3. 数据抓取与抢房

- **数据抓取 (`core/grab/grab_executor.py`)**: `GrabExecutor` 封装了与目标网站交互的所有逻辑，它不仅仅是数据抓取，更是一个自动化的操作执行器。
- **抢房流程**:
    1.  当监控到新房源且满足条件时，`HouseMonitor` 会调用 `GrabExecutor`。
    2.  `GrabExecutor` 首先验证与该抢房设备关联的`Cookie`是否有效。
    3.  然后，它会读取并更新位于 `ZDQF/profiles/{username}/` 目录下的用户特定配置文件 `config.json`，将新房源的ID写入。
    4.  最后，通过调用 `ZDQF/login.py` 中的 `run_application_for_profile` 函数，模拟用户的操作，提交租房申请。这个过程包括了复杂的模拟登录、文件上传和表单提交逻辑。

### 2.4. 通知系统

通知系统 (`services/notification/manager.py`) 负责将监控结果推送给用户。

- **多渠道支持**: 系统内置了对 `Bark`, `WxPush`, `PushMe` 等多种通知服务的支持。
- **设备管理**: 用户可以在Web界面上添加和管理自己的通知设备。每个设备与一个或多个监控的房源关联。
- **消息格式化**: `NotificationManager` 会根据 `MonitorResult` 的内容，生成可读性强的通知标题和内容。对于新增房源，会格式化包含户型、面积、租金等核心信息的详细列表。
- **批量发送**: 为了提高效率，对 `WxPush` 这类支持批量推送的服务，管理器会聚合所有目标用户，进行一次性批量发送。

### 2.5. 任务调度

系统使用一个自定义的轻量级调度器 (`core/scheduling.py`) 来管理定时任务。

- **`Scheduler`**: 调度器在一个独立的后台线程中运行。
- **`ScheduledTask`**: 每个任务可以配置启动时间 (`start_time`)、停止时间 (`stop_time`) 和执行的星期 (`days`)。
- **任务执行**: 调度器每分钟检查一次所有任务，如果当前时间符合某个任务的执行条件，就会调用该任务关联的 `action` 函数。在本项目中，该函数通常是触发 `monitor_service` 开始一轮全面的房源监控。
- **动态配置**: 调度器的所有任务配置都可以通过Web API动态更新，并持久化到配置文件中，实现了灵活的任务管理。

## 3. 前端功能分析

系统的前端是一个基于Flask和Jinja2模板引擎的Web控制台，为用户提供了丰富的交互界面来管理和监控整个系统。

### 3.1. 页面结构与模板

前端页面主要由几个核心HTML模板构成，位于 `templates/` 目录下：

- **`index.html`**: **主控制面板**。这是系统的核心页面，一个单页应用（SPA）风格的仪表盘。它通过JavaScript动态加载和展示所有关键信息，包括：
  - 监控任务的状态（运行中/已停止）。
  - 小区列表及其当前的房源数量。
  - 通知设备列表和抢房设备列表。
  - 系统配置（如代理、通知设置）。
  - 系统活动日志。

- **`logs.html`**: **日志查看页面**。提供一个专门的界面来查看和搜索详细的系统运行日志。

- **`login.html`**: **登录页面**。用于用户身份验证。

- **`first_setup.html`**: **首次安装向导**。当系统检测到没有配置管理员账户时，会引导用户到此页面进行初始设置。

### 3.2. 核心交互逻辑 (index.html)

`index.html` 是前端的核心，其交互逻辑完全由内联的JavaScript和Vue.js驱动，通过AJAX与后端API通信，实现了页面的动态更新，无需刷新页面。

- **数据获取**: 页面加载后，JavaScript会立即向后端API（如 `/api/status`, `/api/houses`, `/api/devices`）发出请求，获取系统的初始状态和所有配置数据。
- **数据绑定**: 使用Vue.js将从API获取的数据动态绑定到HTML元素上。用户的操作（如点击按钮、修改表单）会更新Vue实例中的数据，这些变化会自动反映到UI上。
- **状态轮询**: 页面会定时（例如每5秒）向 `/api/status` 和 `/api/logs` 等端点发送请求，获取最新的系统状态和日志，从而实现近乎实时的监控数据显示。
- **用户操作**:
  - **启动/停止监控**: 点击按钮会触发一个到 `/api/monitor/start` 或 `/api/monitor/stop` 的POST请求。
  - **增删改配置**: 对小区、设备、代理等配置的修改会触发表单提交，通过AJAX将更新后的数据发送到对应的API端点（如 `/api/houses/add`, `/api/devices/update`）。
  - **日志显示**: 最新的日志条目会动态地添加到日志显示区域的顶部，并有自动滚动的效果。

### 3.3. 静态资源

静态资源（CSS, JavaScript库，图片）位于 `static/` 目录下。项目使用了Vue.js和Element-UI作为核心的前端框架，为系统提供了现代化且响应式的用户界面。

## 4. 后端API设计

系统的后端提供了一套完整的RESTful API，用于前后端数据交互和系统管理。所有API都定义在 `web/api/` 目录下，并使用 `@login_required` 装饰器进行权限保护。

### 4.1. 认证与权限

- **登录 (`/login`)**: 用户通过提交用户名和密码进行登录。服务器验证成功后，通过 `session` 记录登录状态。
- **权限控制**: `web/utils/decorators.py` 中的 `@login_required` 装饰器会检查 `session` 中是否存在 `logged_in` 标志，如果不存在，则将用户重定向到登录页面，从而保护所有需要认证的API和页面。

### 4.2. 核心API端点

| 模块 | 端点 | 方法 | 功能描述 |
| :--- | :--- | :--- | :--- |
| **监控** | `/api/status` | GET | 获取监控服务的运行状态、代理信息、剩余代理数等。 |
| | `/api/monitor/start` | POST | 启动后台的监控服务。 |
| | `/api/monitor/stop` | POST | 停止后台的监控服务。 |
| | `/api/proxy/refresh` | POST | 强制刷新代理IP。 |
| **房源** | `/api/houses` | GET | 获取所有监控中的小区列表。 |
| | `/api/houses` | POST | 添加一个新的监控小区。 |
| | `/api/houses/<name>` | PUT | 更新指定小区的配置。 |
| | `/api/houses/<name>` | DELETE | 删除一个监控小区及其所有相关历史数据。 |
| **设备** | `/api/devices` | GET | 获取所有通知设备列表，支持分页、筛选和排序。 |
| | `/api/devices` | POST | 添加一个新的通知设备。 |
| | `/api/devices/<id>` | PUT | 更新指定设备的信息。 |
| | `/api/devices/<id>` | DELETE | 删除一个通知设备（如果未被任何房源使用）。 |
| **抢房设备** | `/api/grab_devices` | GET | 获取所有抢房设备列表。 |
| | `/api/grab_devices` | POST | 添加一个新的抢房设备。 |
| | `/api/grab_devices/<id>` | PUT | 更新抢房设备的配置，包括抢房条件。 |
| | `/api/grab_devices/<id>/login` | POST | 为抢房设备执行登录操作，获取`Cookie`和`Token`。|
| **配置** | `/api/config` | GET | 获取系统所有配置。 |
| | `/api/config` | POST | 更新系统配置。 |
| **调度** | `/api/schedule/status` | GET | 获取调度器的当前状态和任务配置。 |
| | `/api/schedule/update` | POST | 更新调度任务配置。 |

## 5. 技术架构

本章节阐述系统使用的技术栈、核心设计模式与架构原则。

### 5.1. 技术栈

- **后端框架**: **Flask**。整个Web服务和API都基于轻量级的Flask框架构建。
- **网络请求**:
    - **`requests`**: 用于在Web服务和监控服务之间进行同步的API调用。
    - **`aiohttp`**: 用于在核心监控逻辑中执行高效的异步HTTP请求，以并发抓取数据。
- **数据库**: **SQLite** + **SQLAlchemy**。系统使用SQLite作为其默认数据库，并通过SQLAlchemy作为ORM（对象关系映射）与数据库进行交互，简化了数据操作。数据库文件为 `monitoring.db`。
- **任务调度**: 自定义的基于线程的调度器 (`core/scheduling.py`)，用于定时执行监控任务。
- **配置管理**: **PyYAML** 和 **JSON**。系统使用YAML (`.yaml`) 和JSON (`.json`) 文件来管理不同环境的配置。
- **前端框架**: **Vue.js** + **Element-UI**。提供了现代、响应式的用户界面。
- **OCR**: **pytesseract** + **Pillow**。用于识别登录过程中的图形验证码。

### 5.2. 架构模式

系统采用了清晰的、面向服务的架构，将不同的职责分离到独立的服务中。

- **微服务架构**: 系统被明确地分为两个主要的服务：
    1.  **`web` (Web控制台)**: 负责提供用户界面和API。它是一个Flask应用，不执行任何耗时的阻塞任务。
    2.  **`monitor_service` (监控服务)**: 同样是一个Flask应用，但它的主要职责是执行后台的、耗时的监控任务。它通过自己的API接收来自Web控制台的指令。
- **进程管理**: `start_all.py` 脚本充当了一个轻量级的进程管理器。它使用Python的 `subprocess` 模块来独立启动 `web` 和 `monitor_service` 两个服务。它还负责监控这两个服务的健康状况，并在它们意外退出时记录日志。这种设计确保了UI的响应性和后台任务的稳定性是相互隔离的。
- **异步事件驱动**: 在 `core` 模块中，特别是在网络请求和监控逻辑中，广泛使用了 `asyncio` 和 `aiohttp`。这使得系统能够以非阻塞的方式高效地并发处理大量的网络IO操作，极大地提高了数据抓取的效率。

### 5.3. 配置管理

系统拥有一套强大且灵活的统一配置管理机制，在 `config/unified_config.py` 中实现。

- **分层配置**: 配置采用分层加载，优先级从低到高依次为：
    1.  **默认配置 (`default_config.py`)**: 提供系统运行所需的所有默认值。
    2.  **环境配置 (`config_dev.yaml`, `config_prod.yaml`)**: 根据 `APP_ENV` 环境变量加载对应的环境特定配置。
    3.  **用户配置 (`user_config.json`)**: 用户可以通过Web界面修改的配置会保存在此文件中，具有最高优先级，且此文件不应被版本控制系统追踪，以保护敏感信息。
- **统一访问**: 所有模块都通过 `get_config()` 函数获取一个单例的 `UnifiedConfigManager` 实例，从而以统一的方式访问所有配置项，无需关心其来源。
- **敏感信息管理**: 用户的登录凭证等敏感信息存储在 `user_config.json` 中，或者可以通过环境变量进行覆盖，增强了安全性。

### 5.4. 部署与启动

- **依赖安装**: 通过 `pip install -r requirements.txt` 安装所有必要的依赖库。
- **一键启动**: 执行 `python start_all.py` 即可启动整个系统。该脚本会自动按依赖顺序启动监控服务和Web控制台。
- **环境切换**: 通过设置 `APP_ENV` 环境变量（如 `dev` 或 `prod`）来加载不同的配置文件，以适应开发和生产环境。
